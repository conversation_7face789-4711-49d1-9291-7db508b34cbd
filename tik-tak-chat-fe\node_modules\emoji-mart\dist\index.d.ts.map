{"mappings": ";AAAA,qBAAa,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,QAItC;AAED,qBAAa,GAAG,EAAE,MAAM,GAAG,GAAG,CAQ7B;;;;;AESD,qBAAa,KAAK,EAAE;IAAE,EAAE,EAAE,MAAM,CAAA;CAAE,QAWjC;AAED,uBAAa,EAAE,eAAe,EAAE,OAAO,EAAE;;;CAAA,SAuDxC;;;;;;AEjFD,OAAO,IAAI,SAAW,CAAA;AACtB,OAAO,IAAI,SAAW,CAAA;AAoBtB,qBAAqB,OAAO,KAAA,EAAE,EAAE,MAAM,EAAE;;CAAK,iBAe5C;ACxCD,uBAAa,OAAO,KAAA,OAUnB;AAED,+BAEC;AAED,wBAAsB,KAAK,KAAA,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;;;CAAK,gBA0DvD;;;;;;;AC1ED,OAAO,MAAM,mBASZ,CAAA;ACsCD,uCAA6C,YAAY,KAAA,gBAmBxD;AGrED,QAAA,MAAM;;;CAGM,CAAA;AAEZ,yBAAiC,SAAQ,iBAAiB;IACxD,MAAM,KAAK,kBAAkB,aAE5B;gBAEW,KAAK,KAAK;IAatB,MAAM,CAAC,KAAK,KAAK;IAMjB,wBAAwB,CAAC,IAAI,KAAA,EAAE,CAAC,KAAA,EAAE,QAAQ,KAAA;IAkB1C,oBAAoB;CAOrB;ACtDD,2BAAmC,SAAQ,WAAW;gBACxC,KAAK,KAAA,EAAE,EAAE,MAAM,EAAE;;KAAK;IAOlC,SAAS;IAIT,YAAY,CAAC,MAAM,KAAA;CAQpB;AGhBD;IACE;;;;;;;;;;;;;;;;;MAAyB;IAEzB,wBAEC;IAED,mCAWC;IAPG,eAA0B;CAQ/B;AOlBD,mBAAmC,SAAQ,aAAa;IACtD,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAAc;gBAEd,KAAK,KAAA;IAIX,iBAAiB;CAYxB;ACxBD,OAAO,EAAE,OAAO,IAAI,YAAY,EAAE,MAAM,iCAAiC,CAAA", "sources": ["packages/emoji-mart/src/src/helpers/store.ts", "packages/emoji-mart/src/src/helpers/native-support.ts", "packages/emoji-mart/src/src/helpers/frequently-used.ts", "packages/emoji-mart/src/src/components/Picker/PickerProps.ts", "packages/emoji-mart/src/src/config.ts", "packages/emoji-mart/src/src/helpers/search-index.ts", "packages/emoji-mart/src/src/helpers/index.ts", "packages/emoji-mart/src/src/utils.ts", "packages/emoji-mart/src/src/icons.tsx", "packages/emoji-mart/src/src/components/Emoji/Emoji.tsx", "packages/emoji-mart/src/src/components/HTMLElement/HTMLElement.ts", "packages/emoji-mart/src/src/components/HTMLElement/ShadowElement.ts", "packages/emoji-mart/src/src/components/HTMLElement/index.ts", "packages/emoji-mart/src/src/components/Emoji/EmojiProps.ts", "packages/emoji-mart/src/src/components/Emoji/EmojiElement.jsx", "packages/emoji-mart/src/src/components/Emoji/index.ts", "packages/emoji-mart/src/src/components/Navigation/Navigation.tsx", "packages/emoji-mart/src/src/components/Navigation/index.ts", "packages/emoji-mart/src/src/components/HOCs/PureInlineComponent.ts", "packages/emoji-mart/src/src/components/HOCs/index.ts", "packages/emoji-mart/src/src/components/Picker/Picker.tsx", "packages/emoji-mart/src/src/components/Picker/PickerElement.tsx", "packages/emoji-mart/src/src/components/Picker/index.ts", "packages/emoji-mart/src/src/index.ts", "packages/emoji-mart/src/index.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "export { PickerElement as Picker } from './components/Picker'\nexport { EmojiElement as Emoji } from './components/Emoji'\n\nexport { FrequentlyUsed, SafeFlags, SearchIndex, Store } from './helpers'\n\nexport { init, Data, I18n } from './config'\n\nexport { getEmojiDataFromNative } from './utils'\n"], "names": [], "version": 3, "file": "index.d.ts.map"}