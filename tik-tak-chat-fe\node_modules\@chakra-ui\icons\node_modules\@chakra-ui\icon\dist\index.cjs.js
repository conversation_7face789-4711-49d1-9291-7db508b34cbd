"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target, mod));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  Icon: () => Icon,
  createIcon: () => createIcon,
  default: () => icon_default
});
module.exports = __toCommonJS(src_exports);

// ../../react-shim.js
var import_react = __toESM(require("react"));

// src/icon.tsx
var import_system = require("@chakra-ui/system");
var import_shared_utils = require("@chakra-ui/shared-utils");
var fallbackIcon = {
  path: /* @__PURE__ */ import_react.default.createElement("g", {
    stroke: "currentColor",
    strokeWidth: "1.5"
  }, /* @__PURE__ */ import_react.default.createElement("path", {
    strokeLinecap: "round",
    fill: "none",
    d: "M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25"
  }), /* @__PURE__ */ import_react.default.createElement("path", {
    fill: "currentColor",
    strokeLinecap: "round",
    d: "M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0"
  }), /* @__PURE__ */ import_react.default.createElement("circle", {
    fill: "none",
    strokeMiterlimit: "10",
    cx: "12",
    cy: "12",
    r: "11.25"
  })),
  viewBox: "0 0 24 24"
};
var Icon = (0, import_system.forwardRef)((props, ref) => {
  const {
    as: element,
    viewBox,
    color = "currentColor",
    focusable = false,
    children,
    className,
    __css,
    ...rest
  } = props;
  const _className = (0, import_shared_utils.cx)("chakra-icon", className);
  const styles = {
    w: "1em",
    h: "1em",
    display: "inline-block",
    lineHeight: "1em",
    flexShrink: 0,
    color,
    ...__css
  };
  const shared = {
    ref,
    focusable,
    className: _className,
    __css: styles
  };
  const _viewBox = viewBox ?? fallbackIcon.viewBox;
  if (element && typeof element !== "string") {
    return /* @__PURE__ */ import_react.default.createElement(import_system.chakra.svg, {
      as: element,
      ...shared,
      ...rest
    });
  }
  const _path = children ?? fallbackIcon.path;
  return /* @__PURE__ */ import_react.default.createElement(import_system.chakra.svg, {
    verticalAlign: "middle",
    viewBox: _viewBox,
    ...shared,
    ...rest
  }, _path);
});
Icon.displayName = "Icon";
var icon_default = Icon;

// src/create-icon.tsx
var import_system2 = require("@chakra-ui/system");
var import_react2 = require("react");
function createIcon(options) {
  const {
    viewBox = "0 0 24 24",
    d: pathDefinition,
    displayName,
    defaultProps = {}
  } = options;
  const path = import_react2.Children.toArray(options.path);
  const Comp = (0, import_system2.forwardRef)((props, ref) => /* @__PURE__ */ import_react.default.createElement(Icon, {
    ref,
    viewBox,
    ...defaultProps,
    ...props
  }, path.length ? path : /* @__PURE__ */ import_react.default.createElement("path", {
    fill: "currentColor",
    d: pathDefinition
  })));
  Comp.displayName = displayName;
  return Comp;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Icon,
  createIcon
});
