{"mappings": "iKAAe,SAAAA,EAAgCC,GAC7C,QAAa,IAATA,EACF,MAAM,IAAIC,eAAe,6DAG3B,OAAOD,C,CCLT,SAASE,EAAmBC,EAAKC,EAASC,EAAQC,EAAOC,EAAQC,EAAKC,GACpE,IACE,IAAIC,EAAOP,EAAIK,GAAKC,GAChBE,EAAQD,EAAKC,K,CACjB,MAAOC,GAEP,YADAP,EAAOO,E,CAILF,EAAKG,KACPT,EAAQO,GAERG,QAAQV,QAAQO,GAAOI,KAAKT,EAAOC,E,CAIxB,SAAAS,EAA2BC,GACxC,OAAO,WACL,IAAIjB,EAAOkB,KACTC,EAAOC,UACT,OAAO,IAAIN,SAAQ,SAAUV,EAASC,GACpC,IAAIF,EAAMc,EAAGI,MAAMrB,EAAMmB,GAEzB,SAASb,EAAMK,GACbT,EAAmBC,EAAKC,EAASC,EAAQC,EAAOC,EAAQ,OAAQI,E,CAGlE,SAASJ,EAAOe,GACdpB,EAAmBC,EAAKC,EAASC,EAAQC,EAAOC,EAAQ,QAASe,E,CAGnEhB,OAAMiB,E,KC/BG,SAAAC,EAAyBC,EAAUC,GAChD,KAAMD,aAAoBC,GACxB,MAAM,IAAIC,UAAU,oC,CCFxB,SAASC,EAAkBC,EAAQC,GACjC,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CACrC,IAAIE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWzB,IAAKyB,E,EAInC,SAAAM,EAAsBb,EAAac,EAAYC,GAG5D,OAFID,GAAYZ,EAAkBF,EAAYgB,UAAWF,GACrDC,GAAab,EAAkBF,EAAae,GACzCf,C,CCbM,SAAAiB,EAAyBC,EAAKpC,EAAKG,GAYhD,OAXIH,KAAOoC,EACTP,OAAOC,eAAeM,EAAKpC,EAAK,CAC9BG,MAAOA,EACPuB,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZQ,EAAIpC,GAAOG,EAGNiC,C,CCZT,SAASC,EAAeC,EAAGC,GAMzB,OALAF,EAAiBR,OAAOW,gBAAkB,SAAwBC,EAAGC,GAEnE,OADAD,EAAEE,UAAYD,EACPD,C,GAGaH,EAAGC,E,CAGZ,SAAAK,EAAyBH,EAAGC,GACzC,OAAOL,EAAeI,EAAGC,E,CCRZ,SAAAG,EAAmBC,EAAUC,GAC1C,GAA0B,mBAAfA,GAA4C,OAAfA,EACtC,MAAM,IAAI5B,UAAU,sDAGtB2B,EAASZ,UAAYL,OAAOmB,OAAOD,GAAcA,EAAWb,UAAW,CACrEe,YAAa,CACX9C,MAAO2C,EACPlB,UAAU,EACVD,cAAc,KAGdoB,GAAYH,EAAeE,EAAUC,E,CCZ5B,SAAAG,EAAuB7B,GACpC,IAAK,IAAIE,EAAI,EAAGA,EAAIX,UAAUY,OAAQD,IAAK,CACzC,IAAI4B,EAAyB,MAAhBvC,UAAUW,GAAaX,UAAUW,GAAK,GAC/C6B,EAAUvB,OAAOwB,KAAKF,GAEkB,mBAAjCtB,OAAOyB,wBAChBF,EAAUA,EAAQG,OAAO1B,OAAOyB,sBAAsBH,GAAQK,QAAO,SAAUC,GAC7E,OAAO5B,OAAO6B,yBAAyBP,EAAQM,GAAK/B,U,MAIxD0B,EAAQO,SAAQ,SAAU3D,GACxBmC,EAAed,EAAQrB,EAAKmD,EAAOnD,G,IAIvC,OAAOqB,C,CClBM,SAAAuC,EAAyBC,GACtC,GAAIC,MAAMC,QAAQF,GAAM,OAAOA,C,CCDlB,SAAAG,EAA0BC,GACvC,GAAsB,oBAAXC,QAAmD,MAAzBD,EAAKC,OAAOC,WAA2C,MAAtBF,EAAK,cAAuB,OAAOH,MAAMM,KAAKH,E,CCDvG,SAAAI,IACb,MAAM,IAAIlD,UAAU,6I,CCDP,SAAAmD,EAA2BT,EAAKU,IAClC,MAAPA,GAAeA,EAAMV,EAAIrC,UAAQ+C,EAAMV,EAAIrC,QAC/C,IAAK,IAAID,EAAI,EAAGiD,EAAO,IAAIV,MAAMS,GAAMhD,EAAIgD,EAAKhD,IAAKiD,EAAKjD,GAAKsC,EAAItC,GACnE,OAAOiD,C,CCDM,SAAAC,EAAqChC,EAAGiC,GACrD,GAAKjC,EAAL,CACA,GAAiB,iBAANA,EAAgB,OAAO6B,EAAkB7B,EAAGiC,GACvD,IAAIC,EAAI9C,OAAOK,UAAU0C,SAASC,KAAKpC,GAAGqC,MAAM,GAAG,GAEnD,MADU,WAANH,GAAkBlC,EAAEQ,cAAa0B,EAAIlC,EAAEQ,YAAY8B,MAC7C,QAANJ,GAAqB,QAANA,EAAoBb,MAAMM,KAAKO,GACxC,cAANA,GAAqB,2CAA2CK,KAAKL,GAChEL,EAAkB7B,EAAGiC,QAD9B,CALQ,C,CCEK,SAAAO,EAAwBpB,EAAKtC,GAC1C,OAAOqC,EAAeC,IAAQG,EAAqBH,IAAWY,EAA2BZ,EAAKtC,IAAM8C,G,CCJvF,SAAAa,EAA4BrB,GACzC,GAAIC,MAAMC,QAAQF,GAAM,OAAOS,EAAkBT,E,CCHpC,SAAAsB,IACb,MAAM,IAAIhE,UAAU,wI,CCIP,SAAAiE,EAA4BvB,GACzC,OAAOqB,EAAkBrB,IAAQG,EAAgBH,IAAQY,EAA2BZ,IAAQsB,G,CCN/E,SAAAE,IACX,GAAuB,oBAAZC,UAA4BA,QAAQC,UAAW,OAAO,EACjE,GAAID,QAAQC,UAAUC,KAAM,OAAO,EACnC,GAAqB,mBAAVC,MAAsB,OAAO,EACxC,IAEI,OADAC,QAAQxD,UAAUyD,QAAQd,KAAKS,QAAQC,UAAUG,QAAS,IAAI,WAAY,MACnE,C,CACT,MAAOE,GACL,OAAO,C,ECRf,SAASC,EAAevD,GAItB,OAHAuD,EAAiBhE,OAAOW,eAAiBX,OAAOiE,eAAiB,SAAwBrD,GACvF,OAAOA,EAAEE,WAAad,OAAOiE,eAAerD,E,GAExBH,E,CAGT,SAAAyD,EAAyBtD,GACtC,OAAOoD,EAAepD,E,CCRT,SAAAuD,EAAiB5D,GAE5B,OAAOA,GAAOA,EAAIa,cAAgBiB,OAAS,gBAAkB9B,C,CCClD,SAAA6D,EAAoCzG,EAAMqF,GACvD,OAAIA,GAA2B,WAAlBmB,EAAQnB,IAAsC,mBAATA,EAI3CtF,EAAsBC,GAHpBqF,C,CCDI,SAAAqB,EAAsBC,GACjC,IAAIC,EAA4Bf,IAChC,OAAO,WACH,IACIgB,EADAC,EAAQP,EAAgBI,GAE5B,GAAIC,EAA2B,CAC3B,IAAIG,EAAYR,EAAgBrF,MAAMuC,YACtCoD,EAASf,QAAQC,UAAUe,EAAO1F,UAAW2F,E,MAE7CF,EAASC,EAAMzF,MAAMH,KAAME,WAE/B,OAAOqF,EAA2BvF,KAAM2F,E,+ZCR5CG,EAAW,SAAUC,GACvB,aAEA,IAEI1F,EAFA2F,EAAK7E,OAAOK,UACZyE,EAASD,EAAGE,eAEZC,EAA4B,mBAAX3C,OAAwBA,OAAS,GAClD4C,EAAiBD,EAAQ1C,UAAY,aACrC4C,EAAsBF,EAAQG,eAAiB,kBAC/CC,EAAoBJ,EAAQK,aAAe,gBAE/C,SAASC,EAAO/E,EAAKpC,EAAKG,GAOxB,OANA0B,OAAOC,eAAeM,EAAKpC,EAAK,CAC9BG,MAAOA,EACPuB,YAAY,EACZC,cAAc,EACdC,UAAU,IAELQ,EAAIpC,E,CAEb,IAEEmH,EAAO,GAAI,G,CACX,MAAOC,GACPD,EAAS,SAAS/E,EAAKpC,EAAKG,GAC1B,OAAOiC,EAAIpC,GAAOG,C,EAItB,SAASkH,EAAKC,EAASC,EAAS/H,EAAMgI,GAEpC,IAAIC,EAAiBF,GAAWA,EAAQrF,qBAAqBwF,EAAYH,EAAUG,EAC/EC,EAAY9F,OAAOmB,OAAOyE,EAAevF,WACzC0F,EAAU,IAAIC,EAAQL,GAAe,IAMzC,OAFAG,EAAUG,QAuMZ,SAA0BR,EAAS9H,EAAMoI,GACvC,IAAIG,EAAQC,EAEZ,OAAO,SAAgBC,EAAQhI,GAC7B,GAAI8H,IAAUG,EACZ,MAAM,IAAIC,MAAM,gCAGlB,GAAIJ,IAAUK,EAAmB,CAC/B,GAAe,UAAXH,EACF,MAAMhI,EAKR,OAAOoI,G,CAMT,IAHAT,EAAQK,OAASA,EACjBL,EAAQ3H,IAAMA,IAED,CACX,IAAIqI,EAAWV,EAAQU,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUV,GACnD,GAAIW,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,C,EAIX,GAAuB,SAAnBX,EAAQK,OAGVL,EAAQc,KAAOd,EAAQe,MAAQf,EAAQ3H,SAElC,GAAuB,UAAnB2H,EAAQK,OAAoB,CACrC,GAAIF,IAAUC,EAEZ,MADAD,EAAQK,EACFR,EAAQ3H,IAGhB2H,EAAQgB,kBAAkBhB,EAAQ3H,I,KAEN,WAAnB2H,EAAQK,QACjBL,EAAQiB,OAAO,SAAUjB,EAAQ3H,KAGnC8H,EAAQG,EAER,IAAIY,EAASC,EAASzB,EAAS9H,EAAMoI,GACrC,GAAoB,WAAhBkB,EAAOE,KAAmB,CAO5B,GAJAjB,EAAQH,EAAQvH,KACZ+H,EACAa,EAEAH,EAAO7I,MAAQwI,EACjB,SAGF,MAAO,CACLtI,MAAO2I,EAAO7I,IACdI,KAAMuH,EAAQvH,K,CAGS,UAAhByI,EAAOE,OAChBjB,EAAQK,EAGRR,EAAQK,OAAS,QACjBL,EAAQ3H,IAAM6I,EAAO7I,I,GA/QPiJ,CAAiB5B,EAAS9H,EAAMoI,GAE7CD,C,CAcT,SAASoB,EAAStI,EAAI2B,EAAKnC,GACzB,IACE,MAAO,CAAE+I,KAAM,SAAU/I,IAAKQ,EAAGoE,KAAKzC,EAAKnC,G,CAC3C,MAAOa,GACP,MAAO,CAAEkI,KAAM,QAAS/I,IAAKa,E,EAhBjC2F,EAAQY,KAAOA,EAoBf,IAAIW,EAAyB,iBACzBiB,EAAyB,iBACzBf,EAAoB,YACpBE,EAAoB,YAIpBK,EAAmB,GAMvB,SAASf,IAAY,CACrB,SAASyB,IAAoB,CAC7B,SAASC,IAA6B,CAItC,IAAIC,EAAoB,GACxBlC,EAAOkC,EAAmBvC,GAAgB,WACxC,OAAOpG,I,IAGT,IAAI4I,EAAWzH,OAAOiE,eAClByD,EAA0BD,GAAYA,EAASA,EAASE,EAAO,MAC/DD,GACAA,IAA4B7C,GAC5BC,EAAO9B,KAAK0E,EAAyBzC,KAGvCuC,EAAoBE,GAGtB,IAAIE,EAAKL,EAA2BlH,UAClCwF,EAAUxF,UAAYL,OAAOmB,OAAOqG,GAYtC,SAASK,EAAsBxH,GAC7B,CAAC,OAAQ,QAAS,UAAUyB,SAAQ,SAASsE,GAC3Cd,EAAOjF,EAAW+F,GAAQ,SAAShI,GACjC,OAAOS,KAAKoH,QAAQG,EAAQhI,E,OAkClC,SAAS0J,EAAchC,EAAWiC,GAChC,SAASC,EAAO5B,EAAQhI,EAAKL,EAASC,GACpC,IAAIiJ,EAASC,EAASpB,EAAUM,GAASN,EAAW1H,GACpD,GAAoB,UAAhB6I,EAAOE,KAEJ,CACL,IAAI3C,EAASyC,EAAO7I,IAChB6J,EAAQzD,EAAOlG,MACnB,OAAI2J,GACiB,iBAAVA,GACPnD,EAAO9B,KAAKiF,EAAO,WACdF,EAAYhK,QAAQkK,EAAMC,SAASxJ,MAAK,SAASJ,GACtD0J,EAAO,OAAQ1J,EAAOP,EAASC,E,IAC9B,SAASiB,GACV+I,EAAO,QAAS/I,EAAKlB,EAASC,E,IAI3B+J,EAAYhK,QAAQkK,GAAOvJ,MAAK,SAASyJ,GAI9C3D,EAAOlG,MAAQ6J,EACfpK,EAAQyG,E,IACP,SAASjG,GAGV,OAAOyJ,EAAO,QAASzJ,EAAOR,EAASC,E,IAvBzCA,EAAOiJ,EAAO7I,I,CA4BlB,IAAIgK,EAgCJvJ,KAAKoH,QA9BL,SAAiBG,EAAQhI,GACvB,SAASiK,IACP,OAAO,IAAIN,GAAY,SAAShK,EAASC,GACvCgK,EAAO5B,EAAQhI,EAAKL,EAASC,E,IAIjC,OAAOoK,EAaLA,EAAkBA,EAAgB1J,KAChC2J,EAGAA,GACEA,G,EAkHV,SAAS1B,EAAoBF,EAAUV,GACrC,IAAIK,EAASK,EAASnE,SAASyD,EAAQK,QACvC,GAAIA,IAAWlH,EAAW,CAKxB,GAFA6G,EAAQU,SAAW,KAEI,UAAnBV,EAAQK,OAAoB,CAE9B,GAAIK,EAASnE,SAAiB,SAG5ByD,EAAQK,OAAS,SACjBL,EAAQ3H,IAAMc,EACdyH,EAAoBF,EAAUV,GAEP,UAAnBA,EAAQK,QAGV,OAAOQ,EAIXb,EAAQK,OAAS,QACjBL,EAAQ3H,IAAM,IAAIkB,UAChB,iD,CAGJ,OAAOsH,C,CAGT,IAAIK,EAASC,EAASd,EAAQK,EAASnE,SAAUyD,EAAQ3H,KAEzD,GAAoB,UAAhB6I,EAAOE,KAIT,OAHApB,EAAQK,OAAS,QACjBL,EAAQ3H,IAAM6I,EAAO7I,IACrB2H,EAAQU,SAAW,KACZG,EAGT,IAAIvI,EAAO4I,EAAO7I,IAElB,OAAMC,EAOFA,EAAKG,MAGPuH,EAAQU,EAAS6B,YAAcjK,EAAKC,MAGpCyH,EAAQwC,KAAO9B,EAAS+B,QAQD,WAAnBzC,EAAQK,SACVL,EAAQK,OAAS,OACjBL,EAAQ3H,IAAMc,GAUlB6G,EAAQU,SAAW,KACZG,GANEvI,GA3BP0H,EAAQK,OAAS,QACjBL,EAAQ3H,IAAM,IAAIkB,UAAU,oCAC5ByG,EAAQU,SAAW,KACZG,E,CAoDX,SAAS6B,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxB7J,KAAKmK,WAAWC,KAAKN,E,CAGvB,SAASO,EAAcP,GACrB,IAAI1B,EAAS0B,EAAMQ,YAAc,GACjClC,EAAOE,KAAO,gBACPF,EAAO7I,IACduK,EAAMQ,WAAalC,C,CAGrB,SAASjB,EAAQL,GAIf9G,KAAKmK,WAAa,CAAC,CAAEJ,OAAQ,SAC7BjD,EAAY7D,QAAQ2G,EAAc5J,MAClCA,KAAKuK,OAAM,E,CA8Bb,SAASzB,EAAO0B,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAASpE,GAC9B,GAAIqE,EACF,OAAOA,EAAetG,KAAKqG,GAG7B,GAA6B,mBAAlBA,EAASd,KAClB,OAAOc,EAGT,IAAKE,MAAMF,EAAS1J,QAAS,CAC3B,IAAID,GAAI,EAAI8J,EAAO,SAASjB,IAC1B,OAAS7I,EAAI2J,EAAS1J,WAChBmF,EAAO9B,KAAKqG,EAAU3J,GAGxB,OAFA6I,EAAKjK,MAAQ+K,EAAS3J,GACtB6I,EAAK/J,MAAO,EACL+J,EAOX,OAHAA,EAAKjK,MAAQY,EACbqJ,EAAK/J,MAAO,EAEL+J,C,EAGT,OAAOiB,EAAKjB,KAAOiB,C,EAKvB,MAAO,CAAEjB,KAAM/B,E,CAIjB,SAASA,IACP,MAAO,CAAElI,MAAOY,EAAWV,MAAM,E,CA+MnC,OA7mBA8I,EAAkBjH,UAAYkH,EAC9BjC,EAAOsC,EAAI,cAAeL,GAC1BjC,EAAOiC,EAA4B,cAAeD,GAClDA,EAAkBmC,YAAcnE,EAC9BiC,EACAnC,EACA,qBAaFR,EAAQ8E,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOvI,YAClD,QAAOwI,IACHA,IAAStC,GAG2B,uBAAnCsC,EAAKH,aAAeG,EAAK1G,M,EAIhC0B,EAAQiF,KAAO,SAASF,GAQtB,OAPI3J,OAAOW,eACTX,OAAOW,eAAegJ,EAAQpC,IAE9BoC,EAAO7I,UAAYyG,EACnBjC,EAAOqE,EAAQvE,EAAmB,sBAEpCuE,EAAOtJ,UAAYL,OAAOmB,OAAOyG,GAC1B+B,C,EAOT/E,EAAQkF,MAAQ,SAAS1L,GACvB,MAAO,CAAE8J,QAAS9J,E,EAsEpByJ,EAAsBC,EAAczH,WACpCiF,EAAOwC,EAAczH,UAAW6E,GAAqB,WACnD,OAAOrG,I,IAET+F,EAAQkD,cAAgBA,EAKxBlD,EAAQmF,MAAQ,SAAStE,EAASC,EAAS/H,EAAMgI,EAAaoC,QACxC,IAAhBA,IAAwBA,EAActJ,SAE1C,IAAI2D,EAAO,IAAI0F,EACbtC,EAAKC,EAASC,EAAS/H,EAAMgI,GAC7BoC,GAGF,OAAOnD,EAAQ8E,oBAAoBhE,GAC/BtD,EACAA,EAAKmG,OAAO7J,MAAK,SAAS8F,GACxB,OAAOA,EAAOhG,KAAOgG,EAAOlG,MAAQ8D,EAAKmG,M,KAuKjDV,EAAsBD,GAEtBtC,EAAOsC,EAAIxC,EAAmB,aAO9BE,EAAOsC,EAAI3C,GAAgB,WACzB,OAAOpG,I,IAGTyG,EAAOsC,EAAI,YAAY,WACrB,MAAO,oB,IAkCThD,EAAQpD,KAAO,SAASwI,GACtB,IAAIxI,EAAO,GACX,IAAK,IAAIyI,KAAOD,EACdxI,EAAKyH,KAAKgB,GAMZ,OAJAzI,EAAK0I,UAIE,SAAS3B,IACd,KAAO/G,EAAK7B,QAAQ,CAClB,IAAIxB,EAAMqD,EAAK2I,MACf,GAAIhM,KAAO6L,EAGT,OAFAzB,EAAKjK,MAAQH,EACboK,EAAK/J,MAAO,EACL+J,C,CAQX,OADAA,EAAK/J,MAAO,EACL+J,C,GAsCX3D,EAAQ+C,OAASA,EAMjB3B,EAAQ3F,UAAY,CAClBe,YAAa4E,EAEboD,MAAO,SAASgB,GAcd,GAbAvL,KAAKwL,KAAO,EACZxL,KAAK0J,KAAO,EAGZ1J,KAAKgI,KAAOhI,KAAKiI,MAAQ5H,EACzBL,KAAKL,MAAO,EACZK,KAAK4H,SAAW,KAEhB5H,KAAKuH,OAAS,OACdvH,KAAKT,IAAMc,EAEXL,KAAKmK,WAAWlH,QAAQoH,IAEnBkB,EACH,IAAK,IAAIlH,KAAQrE,KAEQ,MAAnBqE,EAAKoH,OAAO,IACZxF,EAAO9B,KAAKnE,KAAMqE,KACjBqG,OAAOrG,EAAKD,MAAM,MACrBpE,KAAKqE,GAAQhE,E,EAMrBqL,KAAM,WACJ1L,KAAKL,MAAO,EAEZ,IACIgM,EADY3L,KAAKmK,WAAW,GACLG,WAC3B,GAAwB,UAApBqB,EAAWrD,KACb,MAAMqD,EAAWpM,IAGnB,OAAOS,KAAK4L,I,EAGd1D,kBAAmB,SAAS2D,GAC1B,GAAI7L,KAAKL,KACP,MAAMkM,EAGR,IAAI3E,EAAUlH,KACd,SAAS8L,EAAOC,EAAKC,GAYnB,OAXA5D,EAAOE,KAAO,QACdF,EAAO7I,IAAMsM,EACb3E,EAAQwC,KAAOqC,EAEXC,IAGF9E,EAAQK,OAAS,OACjBL,EAAQ3H,IAAMc,KAGN2L,C,CAGZ,IAAK,IAAInL,EAAIb,KAAKmK,WAAWrJ,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACpD,IAAIiJ,EAAQ9J,KAAKmK,WAAWtJ,GACxBuH,EAAS0B,EAAMQ,WAEnB,GAAqB,SAAjBR,EAAMC,OAIR,OAAO+B,EAAO,OAGhB,GAAIhC,EAAMC,QAAU/J,KAAKwL,KAAM,CAC7B,IAAIS,EAAWhG,EAAO9B,KAAK2F,EAAO,YAC9BoC,EAAajG,EAAO9B,KAAK2F,EAAO,cAEpC,GAAImC,GAAYC,EAAY,CAC1B,GAAIlM,KAAKwL,KAAO1B,EAAME,SACpB,OAAO8B,EAAOhC,EAAME,UAAU,GACzB,GAAIhK,KAAKwL,KAAO1B,EAAMG,WAC3B,OAAO6B,EAAOhC,EAAMG,W,MAGjB,GAAIgC,GACT,GAAIjM,KAAKwL,KAAO1B,EAAME,SACpB,OAAO8B,EAAOhC,EAAME,UAAU,OAG3B,KAAIkC,EAMT,MAAM,IAAIzE,MAAM,0CALhB,GAAIzH,KAAKwL,KAAO1B,EAAMG,WACpB,OAAO6B,EAAOhC,EAAMG,WAImC,C,IAMjE9B,OAAQ,SAASG,EAAM/I,GACrB,IAAK,IAAIsB,EAAIb,KAAKmK,WAAWrJ,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACpD,IAAIiJ,EAAQ9J,KAAKmK,WAAWtJ,GAC5B,GAAIiJ,EAAMC,QAAU/J,KAAKwL,MACrBvF,EAAO9B,KAAK2F,EAAO,eACnB9J,KAAKwL,KAAO1B,EAAMG,WAAY,CAChC,IAAIkC,EAAerC,EACnB,K,EAIAqC,IACU,UAAT7D,GACS,aAATA,IACD6D,EAAapC,QAAUxK,GACvBA,GAAO4M,EAAalC,aAGtBkC,EAAe,MAGjB,IAAI/D,EAAS+D,EAAeA,EAAa7B,WAAa,GAItD,OAHAlC,EAAOE,KAAOA,EACdF,EAAO7I,IAAMA,EAET4M,GACFnM,KAAKuH,OAAS,OACdvH,KAAK0J,KAAOyC,EAAalC,WAClBlC,GAGF/H,KAAKoM,SAAShE,E,EAGvBgE,SAAU,SAAShE,EAAQ8B,GACzB,GAAoB,UAAhB9B,EAAOE,KACT,MAAMF,EAAO7I,IAcf,MAXoB,UAAhB6I,EAAOE,MACS,aAAhBF,EAAOE,KACTtI,KAAK0J,KAAOtB,EAAO7I,IACM,WAAhB6I,EAAOE,MAChBtI,KAAK4L,KAAO5L,KAAKT,IAAM6I,EAAO7I,IAC9BS,KAAKuH,OAAS,SACdvH,KAAK0J,KAAO,OACa,WAAhBtB,EAAOE,MAAqB4B,IACrClK,KAAK0J,KAAOQ,GAGPnC,C,EAGTsE,OAAQ,SAASpC,GACf,IAAK,IAAIpJ,EAAIb,KAAKmK,WAAWrJ,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACpD,IAAIiJ,EAAQ9J,KAAKmK,WAAWtJ,GAC5B,GAAIiJ,EAAMG,aAAeA,EAGvB,OAFAjK,KAAKoM,SAAStC,EAAMQ,WAAYR,EAAMI,UACtCG,EAAcP,GACP/B,C,GAKbuE,MAAS,SAASvC,GAChB,IAAK,IAAIlJ,EAAIb,KAAKmK,WAAWrJ,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACpD,IAAIiJ,EAAQ9J,KAAKmK,WAAWtJ,GAC5B,GAAIiJ,EAAMC,SAAWA,EAAQ,CAC3B,IAAI3B,EAAS0B,EAAMQ,WACnB,GAAoB,UAAhBlC,EAAOE,KAAkB,CAC3B,IAAIiE,EAASnE,EAAO7I,IACpB8K,EAAcP,E,CAEhB,OAAOyC,C,EAMX,MAAM,IAAI9E,MAAM,wB,EAGlB+E,cAAe,SAAShC,EAAUf,EAAYE,GAa5C,OAZA3J,KAAK4H,SAAW,CACdnE,SAAUqF,EAAO0B,GACjBf,WAAYA,EACZE,QAASA,GAGS,SAAhB3J,KAAKuH,SAGPvH,KAAKT,IAAMc,GAGN0H,C,GAQJhC,C,CA9sBM,CAqtBgB0G,GAG/B,IACEC,mBAAqB5G,C,CACrB,MAAO6G,GAWmB,iBAAfC,WACTA,WAAWF,mBAAqB5G,EAEhC+G,SAAS,IAAK,yBAAdA,CAAwC/G,E,CC/uBrC,IC0BM1B,ECfP0I,ECRFC,ECuKAC,EAQEC,EAcFC,EJhMSC,EAAY,GACZC,EAAY,GACZC,sECON,SAASC,EAAO5L,EAAKd,OAEtB,IAAIC,KAAKD,EAAOc,EAAIb,GAAKD,EAAMC,UACPa,C,CASvB,SAAS6L,EAAWC,OACtBC,EAAaD,EAAKC,WAClBA,GAAYA,EAAWC,YAAYF,E,CEVxC,SAAgBG,EAAcrF,EAAM1H,EAAOgN,OAEzCtO,EACAuO,EACAhN,EAHGiN,EAAkB,OAIjBjN,KAAKD,EACA,OAALC,EAAYvB,EAAMsB,EAAMC,GACd,OAALA,EAAYgN,EAAMjN,EAAMC,GAC5BiN,EAAgBjN,GAAKD,EAAMC,MAG7BX,UAAUY,OAAS,IACtBgN,EAAgBF,SACf1N,UAAUY,OAAS,EAAIsD,EAAMD,KAAKjE,UAAW,GAAK0N,GAKjC,mBAARtF,GAA2C,MAArBA,EAAKyF,iBAChClN,KAAKyH,EAAKyF,sBACVD,EAAgBjN,KACnBiN,EAAgBjN,GAAKyH,EAAKyF,aAAalN,WAKnCmN,EAAY1F,EAAMwF,EAAiBxO,EAAKuO,EAAK,K,CAe9C,SAASG,EAAY1F,EAAM1H,EAAOtB,EAAKuO,EAAKI,OAG5CC,EAAQ,CACb5F,OACA1H,QACAtB,MACAuO,UACW,KAAAM,GACF,KAAAC,IACD,EAAAC,IACF,KAAAC,SAAA,EAKIjO,IACE,KAAAkO,IACA,KACZhM,mBAAalC,IACU,MAAZ4N,IAAqBlB,EAAUkB,UAI3B,MAAZA,GAAqC,MAAjBnB,EAAQoB,OAAepB,EAAQoB,MAAMA,GAEtDA,C,CAOD,SAASM,EAAS5N,UACjBA,EAAMgN,Q,CC5EP,SAASa,EAAU7N,EAAOsG,QAC3BtG,MAAQA,OACRsG,QAAUA,C,CAyET,SAASwH,EAAcR,EAAOS,MAClB,MAAdA,SAEIT,KACJQ,EAAcR,KAAeA,SAAwBU,QAAQV,GAAS,GACtE,aAGAW,EACGF,EAAaT,MAAgBpN,OAAQ6N,OAG5B,OAFfE,EAAUX,MAAgBS,KAEa,MAAhBE,aAIfA,YASmB,mBAAdX,EAAM5F,KAAqBoG,EAAcR,GAAS,I,CAuCjE,SAASY,EAAwBZ,GAAjC,IAGWrN,EACJkO,KAHyB,OAA1Bb,EAAQA,OAA8C,MAApBA,MAA0B,KAChEA,MAAaA,MAAiBc,KAAO,KAC5BnO,EAAI,EAAGA,EAAIqN,MAAgBpN,OAAQD,OAE9B,OADTkO,EAAQb,MAAgBrN,KACO,MAAdkO,MAAoB,CACxCb,MAAaA,MAAiBc,KAAOD,W,CAAAA,OAKhCD,EAAwBZ,E,EAoC1B,SAASe,EAAcC,KAE1BA,QACAA,WACDlC,EAAc5C,KAAK8E,KAClBC,SACFjC,IAAiBJ,EAAQsC,sBAEzBlC,EAAeJ,EAAQsC,oBACNnC,GAAOkC,E,CAK1B,SAASA,YACJE,EACIF,MAAyBnC,EAAclM,QAC9CuO,EAAQrC,EAAcsC,MAAK,SAACC,EAAGC,UAAMD,UAAkBC,S,IACvDxC,EAAgB,GAGhBqC,EAAMI,MAAK,SAAAP,GApGb,IAAyBQ,EAMnBC,EACEC,EANH1B,EACH2B,EACAC,EAkGKZ,QAnGLW,GADG3B,GADoBwB,EAqGQR,aAlG/BY,EAAYJ,SAGRC,EAAc,IACZC,EAAWtC,EAAO,GAAIY,QACPA,MAAkB,EAEvC6B,GACCD,EACA5B,EACA0B,EACAF,eACAI,EAAUE,gBACU,MAApB9B,MAA2B,CAAC2B,GAAU,KACtCF,EACU,MAAVE,EAAiBnB,EAAcR,GAAS2B,EACxC3B,OAED+B,GAAWN,EAAazB,GAEpBA,OAAc2B,GACjBf,EAAwBZ,I,ICtH3B,SAAgBgC,EACfJ,EACAK,EACAC,EACAC,EACAC,EACAC,EACAC,EACAb,EACAE,EACAY,GAVD,IAYK5P,EAAG6P,EAAGd,EAAUe,EAAYC,EAAQC,EAAeC,EAInDC,EAAeV,GAAkBA,OAA6BjD,EAE9D4D,EAAoBD,EAAYjQ,WAEpCsP,MAA2B,GACtBvP,EAAI,EAAGA,EAAIsP,EAAarP,OAAQD,OAgDlB,OA5CjB8P,EAAaP,MAAyBvP,GADrB,OAFlB8P,EAAaR,EAAatP,KAEqB,kBAAd8P,EACW,KAMtB,iBAAdA,GACc,iBAAdA,GAEc,gBAAdA,kBAAcrL,EAAdqL,IAEoC3C,EAC1C,KACA2C,EACA,KACA,KACAA,GAESvN,MAAMC,QAAQsN,GACmB3C,EAC1CQ,EACA,CAAEZ,SAAU+C,GACZ,KACA,KACA,MAESA,MAAoB,EAKa3C,EAC1C2C,EAAWrI,KACXqI,EAAW/P,MACX+P,EAAWrR,IACX,KACAqR,OAG0CA,OAS5CA,KAAqBP,EACrBO,MAAoBP,MAAwB,EAS9B,QAHdR,EAAWmB,EAAYlQ,KAIrB+O,GACAe,EAAWrR,KAAOsQ,EAAStQ,KAC3BqR,EAAWrI,OAASsH,EAAStH,KAE9ByI,EAAYlQ,eAAKR,IAIZqQ,EAAI,EAAGA,EAAIM,EAAmBN,IAAK,KACvCd,EAAWmB,EAAYL,KAKtBC,EAAWrR,KAAOsQ,EAAStQ,KAC3BqR,EAAWrI,OAASsH,EAAStH,KAC5B,CACDyI,EAAYL,UAAKrQ,K,CAGlBuP,EAAW,I,CAObG,GACCD,EACAa,EALDf,EAAWA,GAAYzC,EAOtBmD,EACAC,EACAC,EACAb,EACAE,EACAY,GAGDG,EAASD,OAEJD,EAAIC,EAAW9C,MAAQ+B,EAAS/B,KAAO6C,IACtCI,IAAMA,EAAO,IACdlB,EAAS/B,KAAKiD,EAAK1G,KAAKwF,EAAS/B,IAAK,KAAM8C,GAChDG,EAAK1G,KAAKsG,EAAGC,OAAyBC,EAAQD,IAGjC,MAAVC,GACkB,MAAjBC,IACHA,EAAgBD,GAIU,mBAAnBD,EAAWrI,MAClBqI,QAAyBf,MAEzBe,MAAsBd,EAASoB,EAC9BN,EACAd,EACAC,GAGDD,EAASqB,EACRpB,EACAa,EACAf,EACAmB,EACAH,EACAf,GAIgC,mBAAvBO,EAAe9H,OAQzB8H,MAA0BP,IAG3BA,GACAD,OAAiBC,GACjBA,EAAOpC,YAAcqC,IAIrBD,EAASnB,EAAckB,G,CAAAA,IAIzBQ,MAAsBS,EAGjBhQ,EAAImQ,EAAmBnQ,KACL,MAAlBkQ,EAAYlQ,KAEgB,mBAAvBuP,EAAe9H,MACC,MAAvByI,EAAYlQ,QACZkQ,EAAYlQ,QAAWuP,QAKvBA,MAA0B1B,EAAc2B,EAAgBxP,EAAI,IAG7DsQ,GAAQJ,EAAYlQ,GAAIkQ,EAAYlQ,QAKlCiQ,MACEjQ,EAAI,EAAGA,EAAIiQ,EAAKhQ,OAAQD,IAC5BuQ,GAASN,EAAKjQ,GAAIiQ,IAAOjQ,GAAIiQ,IAAOjQ,G,CAKvC,SAASoQ,EAAgBN,EAAYd,EAAQC,OAA7C,IAKM5B,EAHDgB,EAAIyB,MACJU,EAAM,EACHnC,GAAKmC,EAAMnC,EAAEpO,OAAQuQ,KACvBnD,EAAQgB,EAAEmC,MAMbnD,KAAgByC,EAGfd,EADwB,mBAAd3B,EAAM5F,KACP2I,EAAgB/C,EAAO2B,EAAQC,GAE/BoB,EACRpB,EACA5B,EACAA,EACAgB,EACAhB,MACA2B,WAMGA,C,CASD,SAASyB,EAAa1D,EAAU2D,UACtCA,EAAMA,GAAO,GACG,MAAZ3D,GAAuC,kBAAZA,IACpBxK,MAAMC,QAAQuK,GACxBA,EAAS6B,MAAK,SAAAV,GACbuC,EAAavC,EAAOwC,E,IAGrBA,EAAInH,KAAKwD,IAEH2D,C,CAGR,SAASL,EACRpB,EACAa,EACAf,EACAmB,EACAH,EACAf,GAND,IAQK2B,EAuBGC,EAAiBf,cAtBpBC,MAIHa,EAAUb,MAMVA,kBACM,GACM,MAAZf,GACAgB,GAAUf,GACW,MAArBe,EAAOnD,WAEPiE,EAAO,GAAc,MAAV7B,GAAkBA,EAAOpC,aAAeqC,EAClDA,EAAU6B,YAAYf,GACtBY,EAAU,SACJ,KAGDC,EAAS5B,EAAQa,EAAI,GACxBe,EAASA,EAAOG,cAAgBlB,EAAIK,EAAYjQ,OACjD4P,GAAK,KAEDe,GAAUb,QACPc,EAGR5B,EAAU+B,aAAajB,EAAQf,GAC/B2B,EAAU3B,C,CAAAA,gBAOR2B,EACMA,EAEAZ,EAAOgB,W,CCvSlB,SAASE,GAASC,EAAOzS,EAAKG,GACd,MAAXH,EAAI,GACPyS,EAAMC,YAAY1S,EAAKG,GAEvBsS,EAAMzS,GADa,MAATG,EACG,GACa,iBAATA,GAAqB4N,EAAmB/I,KAAKhF,GACjDG,EAEAA,EAAQ,I,CAYhB,SAASuS,GAAYC,EAAK5N,EAAM5E,EAAOyS,EAAU3B,GAAjD,IACF4B,EAEJpQ,EAAG,GAAa,UAATsC,KACc,iBAAT5E,EACVwS,EAAIF,MAAMK,QAAU3S,MACd,IACiB,iBAAZyS,IACVD,EAAIF,MAAMK,QAAUF,EAAW,IAG5BA,MACE7N,KAAQ6N,EACNzS,GAAS4E,KAAQ5E,GACtBqS,GAASG,EAAIF,MAAO1N,EAAM,OAKzB5E,MACE4E,KAAQ5E,EACPyS,GAAYzS,EAAM4E,KAAU6N,EAAS7N,IACzCyN,GAASG,EAAIF,MAAO1N,EAAM5E,EAAM4E,G,MAOhC,GAAgB,MAAZA,EAAK,IAA0B,MAAZA,EAAK,GAChC8N,EAAa9N,KAAUA,EAAOA,EAAKgO,mBAAoB,KAGxBhO,EAA3BA,EAAKiO,gBAAiBL,EAAY5N,EAAKiO,cAAclO,MAAM,GACnDC,EAAKD,MAAM,GAElB6N,EAAIM,IAAYN,EAAIM,EAAa,IACtCN,EAAIM,EAAWlO,EAAO8N,GAAc1S,EAEhCA,EACEyS,GAEJD,EAAIO,iBAAiBnO,EADL8N,EAAaM,GAAoBC,GACbP,GAIrCF,EAAIU,oBAAoBtO,EADR8N,EAAaM,GAAoBC,GACVP,QAElC,GAAa,4BAAT9N,EAAoC,IAC1CkM,EAIHlM,EAAOA,EAAKgO,qBAAsB,KAAKA,iBAAkB,UACnD,GACG,SAAThO,GACS,SAATA,GACS,SAATA,GAGS,aAATA,GACS,aAATA,GACAA,KAAQ4N,MAGPA,EAAI5N,GAAiB,MAAT5E,EAAgB,GAAKA,QAE3BsC,CACEmD,CAAP,MAAOA,IAUW,mBAAVzF,IAGD,MAATA,SACCA,GAAgC,MAAZ4E,EAAK,IAA0B,MAAZA,EAAK,IAE7C4N,EAAIW,aAAavO,EAAM5E,GAEvBwS,EAAIY,gBAAgBxO,G,EAUvB,SAASqO,GAAWxN,QACdqN,EAAWrN,EAAEoD,SAAcwE,EAAQgG,MAAQhG,EAAQgG,MAAM5N,GAAKA,E,CAGpE,SAASuN,GAAkBvN,QACrBqN,EAAWrN,EAAEoD,SAAawE,EAAQgG,MAAQhG,EAAQgG,MAAM5N,GAAKA,E,CCpInE,SAAgB6K,GACfD,EACAiD,EACAnD,EACAU,EACAC,EACAC,EACAb,EACAE,EACAY,GATD,IAWKY,EAoBEnC,EAAG8D,EAAOC,EAAUC,EAAUC,EAAUC,EACxCC,EAKAC,EACAC,EAqIApD,EA/JLqD,EAAUT,EAASzK,iBAIhByK,EAASxQ,YAA2B,OAAO,KAGpB,MAAvBqN,QACHa,EAAcb,MACdC,EAASkD,MAAgBnD,MAEzBmD,MAAsB,KACtBvC,EAAoB,CAACX,KAGjBwB,EAAMvE,QAAgBuE,EAAI0B,OAG9BrB,EAAO,GAAsB,mBAAX8B,EAAuB,IAEpCH,EAAWN,EAASnS,MAKpB0S,GADJjC,EAAMmC,EAAQC,cACQnD,EAAce,OAChCkC,EAAmBlC,EACpBiC,EACCA,EAAS1S,MAAMnB,MACf4R,KACDf,EAGCV,MAEHwD,GADAlE,EAAI6D,MAAsBnD,UAC0BV,OAGhD,cAAesE,GAAWA,EAAQhS,UAAUkS,OAE/CX,MAAsB7D,EAAI,IAAIsE,EAAQH,EAAUE,IAGhDR,MAAsB7D,EAAI,IAAIT,EAAU4E,EAAUE,GAClDrE,EAAE3M,YAAciR,EAChBtE,EAAEwE,OAASC,IAERL,GAAUA,EAASM,IAAI1E,GAE3BA,EAAEtO,MAAQyS,EACLnE,EAAE7H,QAAO6H,EAAE7H,MAAQ,IACxB6H,EAAEhI,QAAUqM,EACZrE,MAAmBoB,EACnB0C,EAAQ9D,SACRA,MAAqB,IAIF,MAAhBA,QACHA,MAAeA,EAAE7H,OAEsB,MAApCmM,EAAQK,2BACP3E,OAAgBA,EAAE7H,QACrB6H,MAAe5B,EAAO,GAAI4B,QAG3B5B,EACC4B,MACAsE,EAAQK,yBAAyBR,EAAUnE,SAI7C+D,EAAW/D,EAAEtO,MACbsS,EAAWhE,EAAE7H,MAGT2L,EAEkC,MAApCQ,EAAQK,0BACgB,MAAxB3E,EAAE4E,oBAEF5E,EAAE4E,qBAGwB,MAAvB5E,EAAE6E,mBACL7E,MAAmB9E,KAAK8E,EAAE6E,uBAErB,IAE+B,MAApCP,EAAQK,0BACRR,IAAaJ,GACkB,MAA/B/D,EAAE8E,2BAEF9E,EAAE8E,0BAA0BX,EAAUE,IAIpCrE,OAC0B,MAA3BA,EAAE+E,4BACF/E,EAAE+E,sBACDZ,EACAnE,MACAqE,IAEFR,QAAuBnD,MACtB,CACDV,EAAEtO,MAAQyS,EACVnE,EAAE7H,MAAQ6H,MAEN6D,QAAuBnD,QAAoBV,UAC/CA,MAAW6D,EACXA,MAAgBnD,MAChBmD,MAAqBnD,MACrBmD,MAAmB9P,SAAQ,SAAAiL,GACtBA,IAAOA,KAAgB6E,E,IAExB7D,MAAmBpO,QACtB6O,EAAYvF,KAAK8E,SAGZwC,C,CAGsB,MAAzBxC,EAAEgF,qBACLhF,EAAEgF,oBAAoBb,EAAUnE,MAAcqE,GAGnB,MAAxBrE,EAAEiF,oBACLjF,MAAmB9E,MAAK,WACvB8E,EAAEiF,mBAAmBlB,EAAUC,EAAUC,E,IAK5CjE,EAAEhI,QAAUqM,EACZrE,EAAEtO,MAAQyS,EACVnE,EAAE7H,MAAQ6H,OAELmC,EAAMvE,QAAkBuE,EAAI0B,GAEjC7D,SACAA,MAAW6D,EACX7D,MAAeY,EAEfuB,EAAMnC,EAAEwE,OAAOxE,EAAEtO,MAAOsO,EAAE7H,MAAO6H,EAAEhI,SAGnCgI,EAAE7H,MAAQ6H,MAEe,MAArBA,EAAEkF,kBACL9D,EAAgBhD,EAAOA,EAAO,GAAIgD,GAAgBpB,EAAEkF,oBAGhDpB,GAAsC,MAA7B9D,EAAEmF,0BACflB,EAAWjE,EAAEmF,wBAAwBpB,EAAUC,IAK5C/C,EADI,MAAPkB,GAAeA,EAAI/I,OAASkG,GAAuB,MAAX6C,EAAI/R,IACL+R,EAAIzQ,MAAMgN,SAAWyD,EAE7DnB,EACCJ,EACA1M,MAAMC,QAAQ8M,GAAgBA,EAAe,CAACA,GAC9C4C,EACAnD,EACAU,EACAC,EACAC,EACAb,EACAE,EACAY,GAGDvB,EAAEF,KAAO+D,MAGTA,MAAsB,KAElB7D,MAAmBpO,QACtB6O,EAAYvF,KAAK8E,GAGdkE,IACHlE,MAAkBA,KAAyB,MAG5CA,Q,MAEqB,MAArBsB,GACAuC,QAAuBnD,OAEvBmD,MAAqBnD,MACrBmD,MAAgBnD,OAEhBmD,MAgEH,SACCd,EACAc,EACAnD,EACAU,EACAC,EACAC,EACAb,EACAc,GARD,IAoBS1B,EAsDHuF,EACAC,EAjEDtB,EAAWrD,EAAShP,MACpByS,EAAWN,EAASnS,MACpB4T,EAAWzB,EAASzK,KACpBzH,EAAI,KAGS,QAAb2T,IAAoBjE,MAEC,MAArBC,OACI3P,EAAI2P,EAAkB1P,OAAQD,QAC9BkO,EAAQyB,EAAkB3P,KAO/B,iBAAkBkO,KAAYyF,IAC7BA,EAAWzF,EAAM0F,YAAcD,EAA8B,IAAnBzF,EAAMyF,UAChD,CACDvC,EAAMlD,EACNyB,EAAkB3P,GAAK,U,CAAA,GAMf,MAAPoR,EAAa,IACC,OAAbuC,SAEIE,SAASC,eAAetB,GAI/BpB,EADG1B,EACGmE,SAASE,gBACd,6BAEAJ,GAGKE,SAAS/G,cAEd6G,EACAnB,EAASwB,IAAMxB,GAKjB7C,EAAoB,KAEpBC,I,CAAc,GAGE,OAAb+D,EAECvB,IAAaI,GAAc5C,GAAewB,EAAI6C,OAASzB,IAC1DpB,EAAI6C,KAAOzB,OAEN,IAEN7C,EAAoBA,GAAqBpM,EAAMD,KAAK8N,EAAI8C,YAIpDT,GAFJrB,EAAWrD,EAAShP,OAASuM,GAEN6H,wBACnBT,EAAUlB,EAAS2B,yBAIlBvE,EAAa,IAGQ,MAArBD,MACHyC,EAAW,GACNpS,EAAI,EAAGA,EAAIoR,EAAIgD,WAAWnU,OAAQD,IACtCoS,EAAShB,EAAIgD,WAAWpU,GAAGwD,MAAQ4N,EAAIgD,WAAWpU,GAAGpB,OAInD8U,GAAWD,KAGZC,IACED,GAAWC,UAAkBD,UAC/BC,WAAmBtC,EAAIiD,aAExBjD,EAAIiD,UAAaX,GAAWA,UAAmB,I,CAAA,GD3X7C,SAAmBtC,EAAKoB,EAAUJ,EAAU1C,EAAO4E,OACrDtU,MAECA,KAAKoS,EACC,aAANpS,GAA0B,QAANA,GAAiBA,KAAKwS,GAC7CrB,GAAYC,EAAKpR,EAAG,KAAMoS,EAASpS,GAAI0P,OAIpC1P,KAAKwS,EAEN8B,GAAiC,mBAAf9B,EAASxS,IACvB,aAANA,GACM,QAANA,GACM,UAANA,GACM,YAANA,GACAoS,EAASpS,KAAOwS,EAASxS,IAEzBmR,GAAYC,EAAKpR,EAAGwS,EAASxS,GAAIoS,EAASpS,GAAI0P,E,CC8W/C6E,CAAUnD,EAAKoB,EAAUJ,EAAU1C,EAAOE,GAGtC8D,EACHxB,MAAqB,WAErBlS,EAAIkS,EAASnS,MAAMgN,SACnBsC,EACC+B,EACA7O,MAAMC,QAAQxC,GAAKA,EAAI,CAACA,GACxBkS,EACAnD,EACAU,EACAC,GAAsB,kBAAbiE,EACThE,EACAb,EACAa,EACGA,EAAkB,GAClBZ,OAAsBlB,EAAckB,EAAU,GACjDa,GAIwB,MAArBD,MACE3P,EAAI2P,EAAkB1P,OAAQD,KACN,MAAxB2P,EAAkB3P,IAAY0M,EAAWiD,EAAkB3P,IAM7D4P,IAEH,UAAW4C,aACVxS,EAAIwS,EAAS5T,SAKboB,IAAMoS,EAASxT,OACfoB,IAAMoR,EAAIxS,OACI,aAAb+U,IAA4B3T,IAE9BmR,GAAYC,EAAK,QAASpR,EAAGoS,EAASxT,UAGtC,YAAa4T,aACZxS,EAAIwS,EAASgC,UACdxU,IAAMoR,EAAIoD,SAEVrD,GAAYC,EAAK,UAAWpR,EAAGoS,EAASoC,Y,CAAS,OAK7CpD,C,CA5NWqD,CACf1F,MACAmD,EACAnD,EACAU,EACAC,EACAC,EACAb,EACAc,IAIGY,EAAMvE,EAAQyI,SAASlE,EAAI0B,E,CAC/B,MAAO7N,GACR6N,MAAqB,MAEjBtC,GAAoC,MAArBD,KAClBuC,MAAgBlD,EAChBkD,QAAwBtC,EACxBD,EAAkBA,EAAkB5B,QAAQiB,IAAW,MAIxD/C,MAAoB5H,EAAG6N,EAAUnD,E,EAS5B,SAASK,GAAWN,EAAa6F,GACnC1I,OAAiBA,MAAgB0I,EAAM7F,GAE3CA,EAAYF,MAAK,SAAAP,OAGfS,EAAcT,MACdA,MAAqB,GACrBS,EAAYF,MAAK,SAAAgG,GAEhBA,EAAGtR,KAAK+K,E,IAER,MAAOhK,GACR4H,MAAoB5H,EAAGgK,M,KAwL1B,SAAgBkC,GAASvD,EAAKpO,EAAOyO,OAEjB,mBAAPL,EAAmBA,EAAIpO,GAC7BoO,EAAI6H,QAAUjW,C,CAClB,MAAOyF,GACR4H,MAAoB5H,EAAGgJ,E,EAYzB,SAAgBiD,GAAQjD,EAAOyH,EAAaC,GAA5C,IACKC,EAoBMhV,KAnBNiM,EAAQqE,SAASrE,EAAQqE,QAAQjD,IAEhC2H,EAAI3H,EAAML,OACTgI,EAAEH,SAAWG,EAAEH,UAAYxH,OAAYkD,GAASyE,EAAG,KAAMF,IAGjC,OAAzBE,EAAI3H,OAA2B,IAC/B2H,EAAEC,yBAEJD,EAAEC,sB,CACD,MAAO5Q,GACR4H,MAAoB5H,EAAGyQ,E,CAIzBE,EAAE7G,KAAO6G,MAAe,I,CAAA,GAGpBA,EAAI3H,UACCrN,EAAI,EAAGA,EAAIgV,EAAE/U,OAAQD,IACzBgV,EAAEhV,IACLsQ,GAAQ0E,EAAEhV,GAAI8U,EAAkC,mBAAdzH,EAAM5F,MAKtCsN,GAA4B,MAAd1H,OAAoBX,EAAWW,OAIlDA,MAAaA,Y,CAId,SAASyF,GAAS/S,EAAOyG,EAAOH,UACxBlH,KAAKuC,YAAY3B,EAAOsG,E,CCpfhC,SAAgBwM,GAAOxF,EAAO4B,EAAWiG,GAAzC,IAMKtF,EAOAb,EAUAD,EAtBA7C,MAAeA,KAAcoB,EAAO4B,GAYpCF,GAPAa,EAAqC,mBAAhBsF,GAQtB,KACCA,GAAeA,OAA0BjG,MAQzCH,EAAc,GAClBI,GACCD,EARD5B,IACGuC,GAAesF,GACjBjG,OACanC,EAAca,EAAU,KAAM,CAACN,IAS5C0B,GAAYzC,EACZA,WACA2C,EAAUE,iBACTS,GAAesF,EACb,CAACA,GACDnG,EACA,KACAE,EAAUkG,WACV5R,EAAMD,KAAK2L,EAAUiF,YACrB,KACHpF,GACCc,GAAesF,EACbA,EACAnG,EACAA,MACAE,EAAUkG,WACbvF,GAIDR,GAAWN,EAAazB,E,CPrCZ9J,EAAQgJ,EAAUhJ,MCfzB0I,EAAU,CAAAuB,IOJT,SAAqB3O,EAAOwO,WAE9BwB,EAAW3E,EAAMkL,EAEb/H,EAAQA,UACVwB,EAAYxB,SAAsBwB,aAErC3E,EAAO2E,EAAUnN,cAE4B,MAAjCwI,EAAKmL,2BAChBxG,EAAUyG,SAASpL,EAAKmL,yBAAyBxW,IACjDuW,EAAUvG,OAGwB,MAA/BA,EAAU0G,oBACb1G,EAAU0G,kBAAkB1W,GAC5BuW,EAAUvG,OAIPuG,SACKvG,MAA0BA,C,CAElC,MAAOxK,GACRxF,EAAQwF,C,CAAAA,MAKLxF,C,GNjCHqN,EAAU,ECuBd0B,EAAUjN,UAAU2U,SAAW,SAASE,EAAQC,OAE3CC,EAEHA,EADsB,MAAnBvW,UAA2BA,WAAoBA,KAAKqH,MACnDrH,SAEAA,SAAkBsN,EAAO,GAAItN,KAAKqH,OAGlB,mBAAVgP,IAGVA,EAASA,EAAO/I,EAAO,GAAIiJ,GAAIvW,KAAKY,QAGjCyV,GACH/I,EAAOiJ,EAAGF,GAIG,MAAVA,GAEArW,WACCsW,GAAUtW,SAAsBoK,KAAKkM,GACzCrH,EAAcjP,M,EAUhByO,EAAUjN,UAAUgV,YAAc,SAASF,GACtCtW,uBAKCsW,GAAUtW,SAAsBoK,KAAKkM,GACzCrH,EAAcjP,M,EAchByO,EAAUjN,UAAUkS,OAASlF,EAyFzBxB,EAAgB,GAQdC,EACa,mBAAXrN,QACJA,QAAQ4B,UAAU3B,KAAK4W,KAAK7W,QAAQV,WACpCwX,WA2CJvH,MAAyB,EM5NzB,IAAIpC,GAAU,EAqBd,SAASiB,GAAY1F,EAAM1H,EAAOtB,EAAKqX,EAAUC,OAK/C/I,EACAhN,EAFGiN,EAAkB,OAGjBjN,KAAKD,EACA,OAALC,EACHgN,EAAMjN,EAAMC,GAEZiN,EAAgBjN,GAAKD,EAAMC,OAIvBqN,EAAQ,CACb5F,OACA1H,MAAOkN,EACPxO,MACAuO,UACW,KAAAM,GACF,KAAAC,IACD,EAAAC,IACF,KAAAC,SAAA,EACIjO,IACE,KAAAkO,IACA,KACZhM,mBAAalC,MACA0M,GACb4J,WACAC,aAKmB,mBAATtO,IAAwBuF,EAAMvF,EAAKyF,kBACxClN,KAAKgN,WACEC,EAAgBjN,KAC1BiN,EAAgBjN,GAAKgN,EAAIhN,WAIxBiM,EAAQoB,OAAOpB,EAAQoB,MAAMA,GAC1BA,C,KCpDR2I,GAAe,CAAEC,IAhBjB,SAAaxX,EAAaG,GACxB,IACEsX,OAAOC,aAAa,cAAkBnU,OAAJvD,IAAS2X,KAAKC,UAAUzX,EAC5C,CAAd,MAAOC,GAAO,C,EAaIyX,IAVtB,SAAa7X,GACX,IACE,IAAMG,EAAQsX,OAAOC,aAAa,cAAkBnU,OAAJvD,IAEhD,GAAIG,EACF,OAAOwX,KAAKG,MAAM3X,EAEN,CAAd,MAAOC,GAAO,C,GCbZ2X,GAAQ,IAAIC,IACZC,GAAW,CACf,CAAEC,EAAG,GAAIC,MAAO,MACb,CAADD,EAAG,GAAIC,MAAO,MACb,CAADD,EAAG,KAAMC,MAAO,UACR,CAARD,EAAG,GAAIC,MAAO,MACb,CAADD,EAAG,KAAMC,MAAO,SACV,CAAND,EAAG,GAAIC,MAAO,MACb,CAADD,EAAG,GAAIC,MAAO,MACb,CAADD,EAAG,EAAGC,MAAO,MACZ,CAADD,EAAG,EAAGC,MAAO,SACN,CAAPD,EAAG,EAAGC,MAAO,MACZ,CAADD,EAAG,EAAGC,MAAO,QACT,CAAJD,EAAG,EAAGC,MAAO,OAmBjB,SAASC,GAAYD,GACnB,GAAIJ,GAAMM,IAAIF,GACZ,OAAOJ,GAAMF,IAAIM,GAGnB,IAAMG,EAAYC,GAAiBJ,GAGnC,OAFAJ,GAAMP,IAAIW,EAAOG,GAEVA,C,CAIT,IAAMC,GAAmB,WACvB,IAAIC,EAAM,KACV,IACOC,UAAUC,UAAUC,SAAS,WAChCH,EAAMpD,SACH/G,cAAc,UACduK,WAAW,KAAM,CAAEC,oBAAoB,IAEtC,CAAN,MAAAjT,GAAM,CAGR,IAAK4S,EACH,OAAO,W,OAAM,C,EAGf,IACMM,EAAe,GACfC,EAAWC,KAAKC,MAAMC,MAQ5B,OALAV,EAAIW,KAAOJ,EAAW,uBACtBP,EAAIY,aAAe,MACnBZ,EAAIa,OAAOC,MAAQR,GACnBN,EAAIa,OAAOE,OARW,GAUf,SAACC,GACNhB,EAAIiB,UAAU,EAAG,EAAGX,GAXA,IAcpBN,EAAIkB,UAAY,UAChBlB,EAAImB,SAASH,EAAS,EAAG,IAGzBhB,EAAIkB,UAAY,UAChBlB,EAAImB,SAASH,EAASV,EAAc,IAOpC,IALA,IAAM7I,EAAIuI,EAAIoB,aAAa,EAAG,EAAGd,EArBb,IAqB0CtD,KACxDqE,EAAQ5J,EAAEzO,OACZD,EAAI,EAGDA,EAAIsY,IAAU5J,EAAE1O,EAAI,GAAIA,GAAK,GAGpC,GAAIA,GAAKsY,EACP,OAAO,EAKT,IAAMC,EAAIhB,EAAgBvX,EAAK,EAAKuX,EAC9BiB,EAAIf,KAAKC,MAAM1X,EAAI,EAAIuX,GACvB5I,EAAIsI,EAAIoB,aAAaE,EAAGC,EAAG,EAAG,GAAGvE,KAEvC,OAAIvF,EAAE1O,KAAO2O,EAAE,IAAMD,EAAE1O,EAAI,KAAO2O,EAAE,MAMhCsI,EAAIwB,YAAYR,GAASF,OAASR,E,EA5DjB,GAqEzBmB,GAAe,CAAEC,cAjGjB,W,IACOC,GAAkB,EAAlBC,GAAkB,EAAlBC,OAAkBtZ,E,IAAvB,QAAKuZ,EAAAC,EAAsBtC,GAAQ/T,OAAAC,cAA9BgW,GAAAG,EAAAC,EAAkBnQ,QAAA/J,MAAlB8Z,GAAkB,EAAc,CAAhC,IAAAK,EAAAF,EAAkBna,MAAV+X,EAACsC,EAADtC,EACX,GAAIE,GADeoC,EAALrC,OAEZ,OAAOD,C,WAFNkC,GAAkB,EAAlBC,EAAkBvZ,C,aAAlBqZ,GAAkB,MAAlBI,EAAkBE,QAAlBF,EAAkBE,Q,YAAlBL,E,MAAAC,C,IAgGyBK,eAzFhC,WACE,OAAItC,GAAY,O,GCtBZuC,GAAW,CACf,KACA,WACA,gBACA,aACA,WACA,+BACA,cACA,MACA,SACA,eACA,WACA,QACA,MACA,aACA,SAGEC,GAAoB,K,OAwExBC,GAAe,CAAEC,IAtEjB,SAAa3C,GACXyC,KAAUA,GAAQrD,GAAMM,IAAI,eAAiB,IAE7C,IAAMkD,EAAU5C,EAAM6C,IAAM7C,EACvB4C,IAELH,GAAMG,KAAaH,GAAMG,GAAW,GACpCH,GAAMG,IAAY,EAElBxD,GAAMC,IAAI,OAAQuD,GAClBxD,GAAMC,IAAI,aAAcoD,I,EA4DJ/C,IAzDtB,SAAaoD,G,IAAEC,EAAFD,EAAEC,gBAAiBC,EAAnBF,EAAmBE,QAC9B,IAAKD,EAAiB,MAAO,GAE7BN,KAAUA,GAAQrD,GAAMM,IAAI,eAC5B,IAAIuD,EAAW,GAEf,IAAKR,GAAO,CAGV,IAAK,IAAIrZ,KAFTqZ,GAAQ,GAEMD,GAAS7V,MAAM,EAAGqW,GAAU,CACxC,IAAMJ,EAAUJ,GAASpZ,GAEzBqZ,GAAMG,GAAWI,EAAU5Z,EAC3B6Z,EAAStQ,KAAKiQ,E,CAGhB,OAAOK,C,CAGT,IAAMC,EAAMH,EAAkBC,EACxBG,EAAO/D,GAAMM,IAAI,QAEvB,IAAK,IAAI0D,KAAWX,GAClBQ,EAAStQ,KAAKyQ,GAchB,GAXAH,EAASpL,MAAK,SAACC,EAAGC,GAChB,IAAMsL,EAASZ,GAAM1K,GACfuL,EAASb,GAAM3K,GAErB,OAAIuL,GAAUC,EACLxL,EAAEyL,cAAcxL,GAGlBsL,EAASC,C,IAGdL,EAAS5Z,OAAS6Z,EAAK,CACzB,IAAMM,EAAaP,EAAStW,MAAMuW,GAClCD,EAAWA,EAAStW,MAAM,EAAGuW,G,IAExBlB,GAAa,EAAbC,GAAa,EAAbC,OAAatZ,E,IAAlB,QAAKuZ,EAAAC,EAAiBoB,EAAUzX,OAAAC,cAA3BgW,GAAAG,EAAAC,EAAanQ,QAAA/J,MAAb8Z,GAAa,EAAgB,CAA7B,IAAIyB,EAAJtB,EAAana,MACZyb,GAAaN,UACVV,GAAMgB,E,WAFVxB,GAAa,EAAbC,EAAavZ,C,aAAbqZ,GAAa,MAAbI,EAAaE,QAAbF,EAAaE,Q,YAAbL,E,MAAAC,C,EAKDiB,IAAkC,GAA1BF,EAAS9L,QAAQgM,YACpBV,GAAMQ,EAASA,EAAS5Z,OAAS,IACxC4Z,EAASS,QAAO,EAAI,EAAGP,IAGzB/D,GAAMC,IAAI,aAAcoD,G,CAG1B,OAAOQ,C,EAGkBU,SAAAnB,IC7F3BoB,GAAiBpE,KAAKG,MAAM,wjBCA5B,IAAAkE,GAAe,CACbC,UAAW,CACT9b,OAAO,GAET+b,aAAc,CACZ/b,OAAO,GAETgc,kBAAmB,CACjBhc,MAAO,MAETic,kBAAmB,CACjBjc,MAAO,QAETkc,gBAAiB,CACflc,MAAO,IAETmc,UAAW,CACTnc,MAAO,IAEToc,aAAc,CACZpc,MAAO,GACPqc,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,KAAM,GAAI,KAAM,GAAI,KAEvDC,aAAc,CACZtc,MAAO,IAETuc,MAAO,CACLvc,MAAO,OACPqc,QAAS,CAAC,OAAQ,UAAW,UAE/BG,OAAQ,CACNxc,MAAO,KACPqc,QAAS,CACP,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,OAGJtB,gBAAiB,CACf/a,MAAO,GAETyc,YAAa,CACXzc,MAAO,MACPqc,QAAS,CAAC,MAAO,SAAU,SAE7B9B,eAAgB,CACdva,OAAO,GAET0c,eAAgB,CACd1c,MAAO,MAETgb,QAAS,CACPhb,MAAO,GAET2c,aAAc,CACZ3c,MAAO,MAET4c,gBAAiB,CACf5c,MAAO,SACPqc,QAAS,CAAC,MAAO,SAAU,SAE7BQ,eAAgB,CACd7c,MAAO,SACPqc,QAAS,CAAC,SAAU,SAAU,SAEhChF,IAAK,CACHrX,MAAO,SACPqc,QAAS,CAAC,SAAU,QAAS,WAAY,SAAU,YAErDS,KAAM,CACJ9c,MAAO,EACPqc,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,IAE3BU,iBAAkB,CAChB/c,MAAO,UACPqc,QAAS,CAAC,UAAW,SAAU,SAEjCW,MAAO,CACLhd,MAAO,OACPqc,QAAS,CAAC,OAAQ,QAAS,SAI7BY,WAAY,KACZC,cAAe,KACfC,OAAQ,KACR9H,KAAM,KACN+H,KAAM,KAGNC,YAAa,KACbC,kBAAmB,KACnBC,iBAAkB,KAClBC,eAAgB,KAChBC,cAAe,KAGfC,aAAc,CACZC,YAAY,EACZ3d,OAAO,IC5GA4d,GAAO,KACPC,GAAO,KAEZC,GAAa,G,SACJC,GAAUC,G,OAAVC,GAASvd,MAAAH,KAAAE,U,UAATwd,K,OAAAA,GAAf5d,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,EAAyBH,G,IAKjBI,EACAC,E,sEALFP,GAAWE,GAAI,C,wCACVF,GAAWE,I,uBAGGM,MAAMN,G,cAAvBI,EAAQG,EAAAhW,K,SACK6V,EAASC,O,cAAtBA,EAAIE,EAAAhW,KAEVuV,GAAWE,GAAOK,E,kBACXA,G,6CATe3d,MAAAH,KAAAE,U,CAYxB,IAAI+d,GAAgC,KAEhCC,GAAe,KACfC,IAAc,EAEX,SAASC,GAAKtR,G,IAASuR,GAAAne,UAAAY,OAAA,YAAAZ,UAAA,GAAeA,UAAA,GAAF,IAAXme,OAc9B,OAbAJ,KACGA,GAAU,IAAIre,SAAQ,SAACV,GACtBgf,GAAehf,C,KAGf4N,EACFwR,GAAMxR,GACGuR,IAAWF,IACpBI,QAAQC,KACN,IAAY3b,OAAPwb,EAAO,6FAITJ,E,UAGMK,GAAM1d,G,OAAN6d,GAAKte,MAAAH,KAAAE,U,UAALue,K,OAAAA,GAAf3e,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,EAAqBhd,G,IAGbib,EAAc/E,EAAKmF,EAoBZyC,EACHrE,EACA5C,EA0BC5W,EAED8d,EACAC,EAaDnF,EAAAC,EAAAC,EAAAE,EAAAD,EAAMiF,EAmBXC,EACA9E,EAMA+E,EACAC,EAEIC,EAGEzE,EAAiBC,EAgBjBkC,EAEAuC,EAMJC,EAEItE,EACAuE,EAEAC,EAiDGC,EAAAC,EAAAC,EAAAC,EAAAC,EAAMC,EAMTC,EACCC,EAAAC,EAAAC,EAAAC,EAAAC,EAAM1D,EAID2D,EAMFC,E,qEAjMdhC,IAAc,EAERtC,EAA8Bjb,EAA9Bib,aAAc/E,EAAgBlW,EAAhBkW,IAAKmF,EAAWrb,EAAXqb,OACzBJ,IAAiBA,EAAeP,GAAYO,aAAapc,OACzDqX,IAAQA,EAAMwE,GAAYxE,IAAIrX,OAC9Bwc,IAAWA,EAASX,GAAYW,OAAOxc,OAElC6d,GAAI,C,mBAEkB,mBAAf1c,EAAMkU,KAAmB,C,gCAASlU,EAAMkU,O,gDAASlU,EAAMkU,K,4DACxD0I,GACL,6DAA6E3a,OAAhBgZ,EAAa,KAAOhZ,OAAJiU,EAAI,U,6BAHrFwG,GAAIU,EAAAoC,IAMCC,UAAY,GACjB/C,GAAKgD,QAAU,GAEfhD,GAAKZ,WAAW6D,QAAQ,CACtBjG,GAAI,WACJkG,OAAQ,K,6BAGUlD,GAAKmD,S,kDAAd/B,EAAKV,EAAA0C,GAAAjhB,MACR4a,EAAUiD,GAAKmD,QAAQ/B,GACvBjH,EAAQ6F,GAAKkD,OAAOnG,GAChB,C,uDAEV5C,EAAMgJ,UAAYhJ,EAAMgJ,QAAU,IAClChJ,EAAMgJ,QAAQrW,KAAKsU,G,wBAGrBpB,GAAKqD,mBAAqBrD,GAAKZ,W,wBAE/BY,GAAKZ,WAAaY,GAAKZ,WAAW5Z,QAAO,SAACoM,GAExC,SADmBA,EAAE7K,I,eAQA,mBAAfzD,EAAMic,KAAmB,C,iCAASjc,EAAMic,O,iDAASjc,EAAMic,K,8CACpD,MAAVZ,EAAc,C,qBACX0B,EAAAtC,I,yCACMmC,GACJ,6DAAoE3a,OAAPoZ,EAAO,U,iDAL5EoB,GAAIW,EAAA4C,IAQAhgB,EAAMgc,OAAM,C,6CACAhc,EAAMgc,Q,kDAAX/b,EAACmd,EAAA6C,GAAAphB,MACRoB,EAAIigB,SAASjgB,GACP8d,EAAW/d,EAAMgc,OAAO/b,GACxB+d,EAAehe,EAAMgc,OAAO/b,EAAI,GAEjC8d,EAAS6B,QAAW7B,EAAS6B,OAAO1f,OAAM,C,uDAW/C,IATA6d,EAASrE,KAAOqE,EAASrE,GAAK,UAAgBzX,OAANhC,EAAI,IAC5C8d,EAASta,OAASsa,EAASta,KAAOgZ,GAAKX,WAAWE,QAE9CgC,IAAiBD,EAASO,OAC5BP,EAAShe,OAASie,EAAaje,QAAUie,GAG3CtB,GAAKZ,WAAWtS,KAAKuU,GAEhBlF,GAAW,EAAXC,GAAW,EAAXC,OAAWtZ,E,UAAXwZ,EAAe8E,EAAS6B,OAAMhd,OAAAC,cAA9BgW,GAAAG,EAAAC,EAAWnQ,QAAA/J,MAAX8Z,GAAW,EAALoF,EAANjF,EAAWna,MACd6d,GAAKkD,OAAO3B,EAAMvE,IAAMuE,E,mDADrBnF,GAAW,EAAXC,EAAWqE,EAAA+C,G,4BAAXtH,GAAW,MAAXI,EAAWE,QAAXF,EAAWE,S,sBAAXL,EAAW,C,sBAAXC,E,wFAML/Y,EAAM8b,aACRY,GAAKZ,WAAaY,GAAKqD,mBACpB7d,QAAO,SAACoM,GACP,OAAyC,GAAlCtO,EAAM8b,WAAW9N,QAAQM,EAAEoL,G,IAEnChL,MAAK,SAAC0R,EAAIC,GAIT,OAHWrgB,EAAM8b,WAAW9N,QAAQoS,EAAG1G,IAC5B1Z,EAAM8b,WAAW9N,QAAQqS,EAAG3G,G,KAMzCwE,EAAuB,KACvB9E,EAAiB,KACV,UAAPlD,IACFgI,EAAuBvF,GAAcC,gBACrCQ,EAAiBpZ,EAAMoZ,gBAAkBT,GAAcS,kBAGrD+E,EAAgBzB,GAAKZ,WAAW5b,OAChCke,GAAmB,E,YAChBD,IAAe,C,oBAGD,aAFbE,EAAW3B,GAAKZ,WAAWqC,IAEpBzE,KACLE,EAA6B5Z,EAA7B4Z,gBAAiBC,EAAY7Z,EAAZ6Z,QAEvBD,EACEA,GAAmB,EACfA,EACAc,GAAYd,gBAAgB/a,MAClCgb,IAAYA,EAAUa,GAAYb,QAAQhb,OAE1Cwf,EAASuB,OAASrG,GAAehD,IAAI,CAAEqD,kBAAiBC,aAGrDwE,EAASuB,QAAWvB,EAASuB,OAAO1f,OAAM,C,uBAC7Cwc,GAAKZ,WAAWvB,OAAO4D,EAAe,G,iCAIlCpC,EAAoB/b,EAAlB+b,iBAEAuC,EAAOvC,EAAcsC,EAAS3E,OACvB2E,EAASC,OACpBD,EAASC,KAAOA,GAIhBC,EAAaF,EAASuB,OAAO1f,O,aAC1Bqe,IAAY,C,oBACXtE,EAAUoE,EAASuB,OAAOrB,GAC1BC,EAAQvE,EAAQP,GAAKO,EAAUyC,GAAKkD,OAAO3F,GAE3CwE,EAAS,WACbJ,EAASuB,OAAOrF,OAAOgE,EAAY,E,KAIlCC,GACAxe,EAAMmb,cAAgBnb,EAAMmb,aAAa9D,SAASmH,EAAM9E,KAAI,C,wBAE7D+E,I,uCAIEP,GAAwBM,EAAM8B,QAAUpC,GAAoB,C,wBAC9DO,I,sCAIErF,GAAiC,SAAfiF,EAAS3E,GAAa,C,oBACrC6G,GAAUlJ,SAASmH,EAAM9E,IAAG,C,wBAC/B+E,I,qCAKCD,EAAMgC,OAAM,C,oBACfpC,GAAmB,EACnBI,EAAMgC,OACJ,IACA,CACE,CAAChC,EAAM9E,IAAI,GACX,CAAC8E,EAAM/a,MAAM,GACb,CAAC+a,EAAMiC,UAAU,GACjB,CAACjC,EAAMiB,WAAW,IAEjBiB,KAAI,SAAA/G,G,aAAEgH,EAAOC,EAAA,GAAEC,EAAKD,EAAA,GACnB,GAAKD,EACL,OAAQne,MAAMC,QAAQke,GAAWA,EAAU,CAACA,IACzCD,KAAI,SAACI,GACJ,OAAQD,EAAQC,EAAOD,MAAK,aAAgB,CAACC,IAASJ,KACpD,SAAC/K,G,OAAMA,EAAEjE,a,OAGZqP,M,IAEJA,OACA7e,QAAO,SAACyM,G,OAAMA,GAAKA,EAAEqS,M,IACrBC,KAAK,KAGHvC,GAAc,EAAdC,GAAc,EAAdC,OAAcnf,GADjB+e,EAAMiB,UAAS,C,4BACZZ,EAAkBL,EAAMiB,UAAS7c,OAAAC,Y,YAAjC6b,GAAAI,EAAAD,EAAc/V,QAAA/J,KAAA,C,oBAARggB,EAAND,EAAcjgB,OACb6d,GAAK+C,UAAUV,GAAS,C,0DAC5BrC,GAAK+C,UAAUV,GAAYP,EAAM9E,G,SAF9BgF,GAAc,E,kFAAdC,GAAc,EAAdC,EAAcxB,EAAA8D,I,+BAAdxC,GAAc,MAAdG,EAAc1F,QAAd0F,EAAc1F,S,wBAAdwF,EAAc,C,uBAAdC,E,qEAMHI,EAAY,EACXC,GAAU,EAAVC,GAAU,EAAVC,OAAU1f,E,WAAV2f,EAAcZ,EAAM2C,MAAKve,OAAAC,Y,YAAzBoc,GAAAI,EAAAD,EAAUtW,QAAA/J,KAAA,C,oBAAJ4c,EAAN0D,EAAUxgB,MACJ,C,0DACTmgB,KAEMM,EAAa3D,EAAX2D,UAEN5C,GAAKgD,QAAQJ,GAAUd,EAAM9E,GAC7B8E,EAAMgC,QAAU,IAAWve,OAAPqd,IAGhBC,EACS,GAAbP,EAAiB,GAAK,cAAwB/c,OAAV+c,EAAU,KAChDrD,EAAKyF,WAAa,IAAgBnf,OAAZuc,EAAM9E,GAAG,KAAkBzX,OAAfsd,G,SAZ/BN,GAAU,E,kFAAVC,GAAU,EAAVC,EAAU/B,EAAAiE,I,+BAAVpC,GAAU,MAAVG,EAAUjG,QAAViG,EAAUjG,S,wBAAV+F,EAAU,C,uBAAVC,E,wHAkBPf,GACFkD,GAAY3X,QAGd2T,K,iJA9MkB/d,MAAAH,KAAAE,U,CAiNb,SAASiiB,GAASvhB,EAAOmN,EAAcqU,GAC5CxhB,IAAUA,EAAQ,IAElB,IAAMyhB,EAAS,GACf,IAAK,IAAIC,KAAKvU,EACZsU,EAAOC,GAAKC,GAAQD,EAAG1hB,EAAOmN,EAAcqU,GAG9C,OAAOC,C,CAGF,SAASE,GAAQC,EAAU5hB,EAAOmN,EAAcqU,GACrD,IAAMK,EAAW1U,EAAayU,GAC1B/iB,EACF2iB,GAAYA,EAAQM,aAAaF,KACb,MAAnB5hB,EAAM4hB,IAAwCniB,MAAnBO,EAAM4hB,GAC9B5hB,EAAM4hB,GACN,MAEN,OAAKC,GAKM,MAAThjB,GACAgjB,EAAShjB,OACT6F,EAAOmd,EAAShjB,cAAqB,IAALA,EAAK,YAAZ6F,EAAO7F,MAG9BA,EAD2B,kBAAlBgjB,EAAShjB,MACD,SAATA,EAEAgjB,EAAShjB,MAAM8C,YAAY9C,IAInCgjB,EAASE,WAAaljB,IACxBA,EAAQgjB,EAASE,UAAUljB,KAIlB,MAATA,GACCgjB,EAAS3G,UAA8C,GAAnC2G,EAAS3G,QAAQlN,QAAQnP,MAE9CA,EAAQgjB,EAAShjB,OAGZA,GA1BEA,C,CClRX,IACImjB,GAAO,K,SAkBIC,K,OAAAA,GAAf/iB,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,EAAsBne,G,IAAOoO,EAAEiV,EAAYzE,EAMnCvV,EAUFia,EACAC,EAASC,EAERxJ,EAAAC,EAAAC,EAAAE,EAAAD,EAAMxQ,EAMJkW,EAAAC,EAAAC,EAAAC,EAAAC,EAAMjI,EAEHyL,EAAKC,EAAAjjB,U,qEA3BY2N,EAAAsV,EAAAriB,OAAA,YAAAqiB,EAAA,GAA2BA,EAAA,GAAF,GAAvBL,EAAFjV,EAAEiV,WAAYzE,EAAdxQ,EAAcwQ,OACpC5e,GAAUA,EAAMmiB,OAAO9gB,OAAM,C,wCAAS,M,cAC3CgiB,IAAeA,EAAa,I,SAEtB1E,GAAK,KAAM,CAAEC,OAAQA,GAAU,uB,WAE/BvV,EAASrJ,EACZ6S,cACAD,QAAO,QAAU,OACjBoP,MAAK,WACL3e,QAAO,SAACsgB,EAAMviB,EAAGwiB,GAChB,OAAOD,EAAKxB,QAAUyB,EAAMzU,QAAQwU,IAASviB,C,KAGrCC,OAAM,C,gDAEdiiB,EAAOH,KAASA,GAAOzhB,OAAO2H,OAAOwU,GAAKkD,SAGzC/G,GAAW,EAAXC,GAAW,EAAXC,OAAWtZ,E,UAAXwZ,EAAe/Q,EAAMtF,OAAAC,Y,WAArBgW,GAAAG,EAAAC,EAAWnQ,QAAA/J,KAAA,C,mBAALyJ,EAANwQ,EAAWna,MACTsjB,EAAKjiB,OAAM,C,oDAEhBkiB,EAAU,GACVC,EAAS,GAEJ3D,GAAW,EAAXC,GAAW,EAAXC,OAAWnf,E,UAAXof,EAAesD,EAAIvf,OAAAC,Y,WAAnB6b,GAAAI,EAAAD,EAAW/V,QAAA/J,KAAA,C,oBAAL8X,EAANiI,EAAWjgB,OACH2hB,OAAM,C,2DAEJ,IADP8B,EAAQzL,EAAM2J,OAAOxS,QAAQ,IAAU/L,OAANuG,KACxB,C,uDAEf4Z,EAAQ5Y,KAAKqN,GACbwL,EAAOxL,EAAM6C,MAAQ2I,EAAOxL,EAAM6C,IAAM,GACxC2I,EAAOxL,EAAM6C,KAAO7C,EAAM6C,IAAMlR,EAAQ,EAAI8Z,EAAQ,E,QAPjD5D,GAAW,E,2EAAXC,GAAW,EAAXC,EAAWxB,EAAAoC,G,4BAAXd,GAAW,MAAXG,EAAW1F,QAAX0F,EAAW1F,S,sBAAXwF,EAAW,C,sBAAXC,E,gEAULuD,EAAOC,E,QAhBJvJ,GAAW,E,2EAAXC,GAAW,EAAXC,EAAWqE,EAAAsF,G,4BAAX7J,GAAW,MAAXI,EAAWE,QAAXF,EAAWE,S,sBAAXL,EAAW,C,sBAAXC,E,qEAmBDqJ,EAAQliB,OAAS,GAAC,C,yCACbkiB,G,eAGTA,EAAQ1T,MAAK,SAACC,EAAGC,GACf,IAAMsL,EAASmI,EAAO1T,EAAE+K,IAClBS,EAASkI,EAAOzT,EAAE8K,IAExB,OAAIQ,GAAUC,EACLxL,EAAE+K,GAAGU,cAAcxL,EAAE8K,IAGvBQ,EAASC,C,IAGdiI,EAAQliB,OAASgiB,IACnBE,EAAUA,EAAQ5e,MAAM,EAAG0e,I,kBAGtBE,G,uGAzDMH,GAAM1iB,MAAAH,KAAAE,U,KA4DrBgiB,GAAe,CAAEd,O,SA5DK3hB,G,OAAPojB,GAAM1iB,MAAAH,KAAAE,U,EA4DIiX,IA5EzB,SAAakD,GACX,OAAIA,EAAQC,GACHD,EAIPiD,GAAKkD,OAAOnG,IACZiD,GAAKkD,OAAOlD,GAAKmD,QAAQpG,KACzBiD,GAAKkD,OAAOlD,GAAKgD,QAAQjG,G,EAoEC9P,MAhE9B,WACEqY,GAAO,I,EA+D4BW,iBA/Ef,6CCGTpC,GAAY,CACvB,iBACA,gBACA,cACA,eACA,mBACA,0BACA,oBACA,qB,SCHoBqC,K,OAAAC,GAAKtjB,MAAAH,KAAAE,U,UAALujB,K,OAAAA,GAAf3jB,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,IAAqB8F,EAChBP,EAAAjjB,U,kEADgBwjB,EAAAP,EAAAriB,OAAA,YAAAqiB,EAAA,GAAUA,EAAA,GAAD,E,6BACpBze,EAAGtB,MAAMsgB,GAAQ/gB,S,oDAAtBqb,EAAAsF,GAAA7jB,M,SACF,IAAIG,QAAQ+jB,uB,iEAFAF,GAAKtjB,MAAAH,KAAAE,U,CAMpB,SAAS0jB,GAAanM,G,IAAO5J,EAAA3N,UAAAY,OAAA,YAAAZ,UAAA,GAAsBA,UAAA,GAAF,GAAE2jB,EAAtBhW,EAAE+R,iBAAS,IAAAiE,EAAG,EAACA,EAC3CtH,EACJ9E,EAAMsK,MAAMnC,KAEVA,EAAY,EACLnI,EAAMsK,MAAMnC,IAGjBkE,EAAiB,CACrBxJ,GAAI7C,EAAM6C,GACVjW,KAAMoT,EAAMpT,KACZ6b,OAAQ3D,EAAK2D,OACb6D,QAASxH,EAAKwH,QACd1C,SAAU5J,EAAM4J,SAChBW,WAAYzF,EAAKyF,YAAcvK,EAAMuK,YAmBvC,OAhBIvK,EAAMsK,MAAMjhB,OAAS,IACvBgjB,EAAUvH,KAAOqD,EAAY,GAG3BrD,EAAKkB,MACPqG,EAAUrG,IAAMlB,EAAKkB,KAGnBhG,EAAMgJ,SAAWhJ,EAAMgJ,QAAQ3f,SACjCgjB,EAAUrD,QAAUhJ,EAAMgJ,SAGxBhJ,EAAM4I,WAAa5I,EAAM4I,UAAUvf,SACrCgjB,EAAUzD,UAAY5I,EAAM4I,WAGvByD,C,UAGaE,GAAuBC,G,OAAvBC,GAAsB/jB,MAAAH,KAAAE,U,UAAtBgkB,K,OAAAA,GAAfpkB,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,EAAsCqG,G,IACrCjB,EAMAvL,EACFmI,EAECnG,EAAAC,EAAAC,EAAAE,EAAAD,E,kFATiBsI,GAAYd,OAAO6C,EAAc,CACrDnB,WAAY,EACZzE,OAAQ,2B,WAFJ2E,EAAOhF,EAAAhW,OAIIgb,EAAQliB,OAAM,C,wCAAS,M,OAElC2W,EAAQuL,EAAQ,GAClBpD,EAAY,EAEXnG,GAAQ,EAARC,GAAQ,EAARC,OAAQtZ,E,SAARwZ,EAAYpC,EAAMsK,MAAKve,OAAAC,Y,WAAvBgW,GAAAG,EAAAC,EAAQnQ,QAAA/J,KAAA,C,mBAARia,EAAQna,MACFygB,QAAU+D,EAAY,C,oDAI/BrE,I,QALGnG,GAAQ,E,0EAARC,GAAQ,EAARC,EAAQqE,EAAAoC,G,4BAAR3G,GAAQ,MAARI,EAAQE,QAARF,EAAQE,S,sBAARL,EAAQ,C,sBAARC,E,yFAQEiK,GAAanM,EAAO,CAAEmI,e,6EAlBazf,MAAAH,KAAAE,U,CCrD5C,IAgJAikB,GAAe,CAAEzH,WAhJE,CACjB0H,SAAU,CACRC,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,8tBAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,skCAKd7H,OACE0H,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,umBAIZE,MAAO,CACLN,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,sKAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,saAKdG,MAAO,CACLP,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,u1BAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,49DAKdI,SAAU,CACRR,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,UAC9CF,GAAC,OAAI,CAACG,EAAE,2CACRH,GAAC,OAAI,CAACG,EAAE,qJAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,+TAKdK,OAAQ,CACNT,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,UAC9CF,GAAC,OAAI,CAACG,EAAE,6GACRH,GAAC,OAAI,CAACG,EAAE,whDAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,ouBAKdM,QAAS,CACPV,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,UAC9CF,GAAC,OAAI,CAACG,EAAE,oXACRH,GAAC,OAAI,CAACG,EAAE,ojBAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,8oBAKdO,OAAQ,CACNX,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,UAC9CF,GAAC,OAAI,CAACG,EAAE,kJACRH,GAAC,OAAI,CAACG,EAAE,gLAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,khBAKdQ,OAAQ,CACNZ,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,UAC9CF,GAAC,OAAI,CAACG,EAAE,wSACRH,GAAC,OAAI,CAACG,EAAE,2rBAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,ipBAKdS,QAAS,CACPb,QACEC,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,+4CAGZC,MACEJ,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,c,SAC9CF,GAAC,OAAI,CAACG,EAAE,s5CAoBarD,OAdd,CACb+D,MACEb,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,wGAIZW,OACEd,GAAC,MAAG,CAACC,MAAM,6BAA6BC,QAAQ,Y,SAC9CF,GAAC,OAAI,CAACG,EAAE,0JCxIC,SAAAY,GAAezkB,GAC5B,IAAM0Z,EAAoB1Z,EAApB0Z,GAAIiC,EAAgB3b,EAAhB2b,KAAM9E,EAAU7W,EAAV6W,MAEhB,GAAI7W,EAAMohB,WAAY,CACpB,IAAMsD,EAAU1kB,EAAMohB,WAAWuD,MAAMrD,GAAYqB,kBAE/C+B,IACFhL,EAAKgL,EAAQ,GAETA,EAAQ,KACV/I,EAAO+I,EAAQ,I,CAMrB,GADA7N,IAAUA,EAAQyK,GAAY/K,IAAImD,GAAM1Z,EAAMsf,UACzCzI,EAAO,OAAO7W,EAAM4kB,SAEzB,IAAMC,EAAYhO,EAAMsK,MAAMxF,EAAO,IAAM9E,EAAMsK,MAAM,GAEjD2D,EACJD,EAAUhI,MACI,UAAb7c,EAAMkW,KAAoBlW,EAAM+kB,iBAI7BtlB,EAH6B,mBAAtBO,EAAMkc,YACXlc,EAAMkc,YAAYlc,EAAMkW,IAAK2O,EAAU1B,SACvC,iDAAyElhB,OAAxBjC,EAAMkW,IAAI,gBAA8BjU,OAAhBjC,EAAMkW,IAAI,QAAwBjU,OAAlB4iB,EAAU1B,QAAQ,SAG7G6B,EAC+B,mBAA5BhlB,EAAMmc,kBACTnc,EAAMmc,kBAAkBnc,EAAMkW,KAC9B,iDAAyEjU,OAAxBjC,EAAMkW,IAAI,gBAAwBjU,OAAVjC,EAAMkW,IAAI,sBAEzF,OACEwN,GAAC,OAAI,CAACuB,MAAM,mBAAmB,iBAAgBjlB,EAAMkW,I,SAClD4O,EACCpB,GAAC,MAAG,CACFvS,MAAO,CACL+T,SAAUllB,EAAMmlB,MAAQ,MACxBC,UAAWplB,EAAMmlB,MAAQ,MACzBE,QAAS,gBAEXC,IAAKT,EAAUvF,QAAUuF,EAAUzD,WACnCvE,IAAKiI,IAEQ,UAAb9kB,EAAMkW,IACRwN,GAAC,OAAI,CACHvS,MAAO,CACLoU,SAAUvlB,EAAMmlB,KAChBK,WACE,6I,SAGHX,EAAUvF,SAGboE,GAAC,OAAI,CACHvS,MAAO,CACLkU,QAAS,QACTrN,MAAOhY,EAAMmlB,KACblN,OAAQjY,EAAMmlB,KACdM,gBAAiB,OAAsBxjB,OAAf+iB,EAAe,KACvCU,eAAgB,GACdzjB,OADiB,IAAMya,GAAKiJ,MAAMC,KAAK,MAExC3jB,OADC,IAAMya,GAAKiJ,MAAME,KAClB,KACDC,mBAAoB,GAEf7jB,OADH,KAAQya,GAAKiJ,MAAMC,KAAO,GAAMf,EAAUrM,EAC3C,MAAgDvW,OAA5C,KAAQya,GAAKiJ,MAAME,KAAO,GAAMhB,EAAUpM,EAAE,S,CCpE7D,SAASsN,KACP,GAAuB,oBAAZ/hB,UAA4BA,QAAQC,UAAW,OAAO,EACjE,GAAID,QAAQC,UAAUC,KAAM,OAAO,EACnC,GAAqB,mBAAVC,MAAsB,OAAO,EAExC,IAEE,OADA6hB,KAAKplB,UAAU0C,SAASC,KAAKS,QAAQC,UAAU+hB,KAAM,IAAI,WAAY,MAC9D,C,CACP,MAAO1hB,GACP,OAAO,C,EAIX,SAAS2hB,GAAUC,EAAQC,EAAMC,GAc/B,OAZEH,GADEF,KACU/hB,QAAQC,UAER,SAAmBoiB,EAAQhnB,EAAMinB,GAC3C,IAAI3X,EAAI,CAAC,MACTA,EAAEnF,KAAKjK,MAAMoP,EAAGtP,GAChB,IACIM,EAAW,IADGsM,SAAS4J,KAAKtW,MAAM8mB,EAAQ1X,IAG9C,OADI2X,GAAOhlB,EAAgB3B,EAAU2mB,EAAM1lB,WACpCjB,C,GAIMJ,MAAM,KAAMD,U,CAGhB,SAAAinB,GAAoBF,EAAQhnB,EAAMinB,GAC/C,OAAOL,GAAU1mB,MAAM,KAAMD,U,CC5B/B,SAASknB,GAAgBJ,GACvB,IAAIK,EAAwB,mBAAR/P,IAAqB,IAAIA,SAAQjX,EA8BrD,OA5BA+mB,GAAkB,SAAyBF,GACzC,GAAc,OAAVA,ICTkCnnB,EDSEmnB,GCRqB,IAAxDra,SAAS3I,SAASC,KAAKpE,GAAI6O,QAAQ,kBDQQ,OAAOsY,ECT5C,IAA2BnnB,EDWtC,GAAqB,mBAAVmnB,EACT,MAAM,IAAIzmB,UAAU,sDAGtB,QAAsB,IAAX4mB,EAAwB,CACjC,GAAIA,EAAO1P,IAAIuP,GAAQ,OAAOG,EAAOlQ,IAAI+P,GAEzCG,EAAOvQ,IAAIoQ,EAAOI,E,CAGpB,SAASA,IACP,OAAOH,GAAUD,EAAOhnB,UAAWmF,EAAerF,MAAMuC,Y,CAW1D,OARA+kB,EAAQ9lB,UAAYL,OAAOmB,OAAO4kB,EAAM1lB,UAAW,CACjDe,YAAa,CACX9C,MAAO6nB,EACPtmB,YAAY,EACZE,UAAU,EACVD,cAAc,KAGXiB,EAAeolB,EAASJ,E,EAG1BE,GAAgBJ,E,CAGV,SAAAO,GAA0BL,GACvC,OAAOE,GAAgBF,E,CErCzB,IAKeM,GCRZ,SDQYC,G,wCAAMC,I,MAKP9mB,EAAAV,UAAAY,OAAA,YAAAZ,UAAA,GAAUA,UAAA,GAAF,GAIlB,G,2BAFKU,MAAQA,EAETA,EAAM+mB,QAAU/mB,EAAMiN,IAAK,CAC7B,IAAIA,EAAM,KACJ8Z,EAAS/mB,EAAM+mB,SAAW9Z,EAAMjN,EAAMiN,KAAOjN,EAAMiN,IAAI6H,SAEzD7H,IAAKA,EAAIqH,UAAY,IACrByS,GAAQA,EAAOhW,YAAW9S,EAAA+oB,G,uBAIlCtoB,IAAA,S,MAAA,W,IAAOsB,EAAAV,UAAAY,OAAA,YAAAZ,UAAA,GAAUA,UAAA,GAAF,GACb,IAAK,IAAIoiB,KAAK1hB,EACZZ,KAAK6nB,yBAAyBvF,EAAG,KAAM1hB,EAAM0hB,G,IAIjDhjB,IAAA,2B,MAAA,SAAyBwoB,EAAMC,EAAGC,GAChC,GAAKhoB,KAAK0P,UAAV,CAEA,IAAMjQ,EAAQ8iB,GACZuF,EACErmB,EAAA,GAACqmB,EAAOE,GACVhoB,KAAKuC,YAAY0lB,MACjBjoB,MAGEA,KAAK0P,UAAUsE,0BACjBhU,KAAK0P,UAAUsE,0BAA4BvS,EAAA,GAACqmB,EAAOroB,KAEnDO,KAAK0P,UAAU9O,MAAMknB,GAAQroB,EAC7BO,KAAK0P,UAAU8G,cAbI,C,IAiBvBlX,IAAA,uB,MAAA,WACEU,KAAKkoB,cAAe,EAEhBloB,KAAK0P,WAAa1P,KAAK0P,UAAUyY,YACnCnoB,KAAK0P,UAAUyY,Y,MA7CR7oB,IAAA,qB,IAAX,WACE,OAAO6B,OAAOwB,KAAK3C,KAAKioB,M,OCVzB,CDIiB,oBAAXlR,QAA0BA,OAAO2Q,YACpC3Q,OAAO2Q,YACPvmB,QEHSinB,GDHZ,SCGYC,G,wCAAMC,EACP1nB,G,MAAO2nB,GAAAroB,UAAAY,OAAA,YAAAZ,UAAA,GAAeA,UAAA,GAAF,IAAXqoB,O,gCACb3nB,IAED4nB,YACLZ,EAAKa,aAAaF,G,eAGpBjpB,IAAA,Y,MAAA,WACEU,KAAK0oB,aAAa,CAAEC,KAAM,Q,IAG5BrpB,IAAA,e,MAAA,SAAaipB,GACX,GAAKA,EAAL,CAEA,IAAMxW,EAAQ2C,SAAS/G,cAAc,SACrCoE,EAAM6W,YAAcL,EAEpBvoB,KAAK6oB,WAAWhX,aAAaE,EAAO/R,KAAK6oB,WAAW7S,WALvC,C,ODhBd,CCuBFuR,GApB0CC,KCD3CsB,GAAe,CACbtD,SAAU,GACVlL,GAAI,GACJ4F,OAAQ,GACR8B,WAAY,GACZ+D,KAAM,CACJtmB,MAAO,GACPkjB,UAAW,SAACljB,GAEV,MAAK,KAAK6E,KAAK7E,GAIRA,EAHE,GAASoD,OAANpD,EAAM,K,GAQtBqX,IAAKwE,GAAYxE,IACjByF,KAAMjB,GAAYiB,MCdLwM,GAqBZ,SArBYV,G,wCAAMW,EAGPpoB,G,6BACJA,E,cAGFtB,IAAA,oB,MAAN,W,kBAAAQ,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,IACQhd,E,yEAAAA,EAAQuhB,GAASyF,EAAKhnB,MAAOkoB,GAAUlB,IACvCxF,QAAOwF,EACbhnB,EAAMiN,IAAM,SAAC6B,GACXkY,EAAKlY,UAAYA,C,WAGb0O,K,WACFwJ,EAAKM,aAAY,C,gDAErBe,GAAO3E,GAACe,GAAK7iB,EAAA,GAAK5B,IAASgnB,G,0CAV7B,E,OAcC,CAFFL,GAnByCC,KACxC/lB,EADmBsnB,GACZ,QAAQD,IAoBa,oBAAnBI,gBAAmCA,eAAe/R,IAAI,aAC/D+R,eAAeziB,OAAO,WAAYsiB,IC1BpC,IAGII,GAeAC,GATAC,GAAoB,GAEpBC,GAAgBxc,MAChByc,GAAkBzc,MAClB0c,GAAe1c,EAAQyI,OACvBkU,GAAY3c,MACZ4c,GAAmB5c,EAAQqE,QA+Q/B,SAASwY,SACJja,MAEJ2Z,GAAkB/Z,MAAK,SAACC,EAAGC,UAAMD,UAAkBC,S,IAC5CE,EAAY2Z,GAAkB/d,UAC/BoE,UAEJA,UAAkCzM,QAAQ2mB,IAC1Cla,UAAkCzM,QAAQ4mB,IAC1Cna,UAAoC,E,CACnC,MAAOxK,GACRwK,UAAoC,GACpC5C,MAAoB5H,EAAGwK,M,EAtR1B5C,MAAgB,SAAAoB,GACfib,GAAmB,KACfG,IAAeA,GAAcpb,E,EAGlCpB,MAAkB,SAAAoB,GACbqb,IAAiBA,GAAgBrb,GAGtB,MAET4b,GAHNX,GAAmBjb,WAIf4b,IACHA,MAAsB7mB,QAAQ2mB,IAC9BE,MAAsB7mB,QAAQ4mB,IAC9BC,MAAwB,G,EAI1Bhd,EAAQyI,OAAS,SAAArH,GACZsb,IAAcA,GAAatb,OAEzBgB,EAAIhB,MACNgB,GAAKA,OAAaA,UAA0BpO,SAsSzB,IArSXuoB,GAAkBjf,KAAK8E,IAqSPka,KAAYtc,EAAQ6W,yBAC/CyF,GAAUtc,EAAQ6W,wBAvBpB,SAAwBrN,OAQnByT,EAPEpqB,EAAO,WACZqqB,aAAaC,GACTC,IAASC,qBAAqBJ,GAClCrT,WAAWJ,E,EAEN2T,EAAUvT,WAAW/W,EAhTR,KAmTfuqB,KACHH,EAAMpG,sBAAsBhkB,G,GAcAgqB,KArS7BR,GAAmB,I,EAGpBrc,MAAkB,SAACoB,EAAOyB,GACzBA,EAAYF,MAAK,SAAAC,OAEfA,MAA2BzM,QAAQ2mB,IACnCla,MAA6BA,MAA2B5M,QAAO,SAAA2S,UAC9DA,MAAYoU,GAAapU,E,IAEzB,MAAOvQ,GACRyK,EAAYF,MAAK,SAAAP,GACZA,QAAoBA,MAAqB,G,IAE9CS,EAAc,GACd7C,MAAoB5H,EAAGwK,M,KAIrB+Z,IAAWA,GAAUvb,EAAOyB,E,EAGjC7C,EAAQqE,QAAU,SAAAjD,GACbwb,IAAkBA,GAAiBxb,OAIlCkc,EAFClb,EAAIhB,MACNgB,GAAKA,QAERA,SAAgBjM,SAAQ,SAAAsT,OAEtBqT,GAAcrT,E,CACb,MAAOrR,GACRklB,EAAallB,C,KAGXklB,GAAYtd,MAAoBsd,EAAYlb,O,EA8NlD,IAAIgb,GAA0C,mBAAzBvG,sBA2CrB,SAASiG,GAAcS,OAGhBC,EAAOnB,GACToB,EAAUF,MACQ,mBAAXE,IACVF,aACAE,KAEDpB,GAAmBmB,C,CAOpB,SAAST,GAAaQ,OAGfC,EAAOnB,GACbkB,MAAgBA,OAChBlB,GAAmBmB,C,CC3Wb,SAAShd,GAAO5L,EAAKd,OACtB,IAAIC,KAAKD,EAAOc,EAAIb,GAAKD,EAAMC,UACPa,C,CASvB,SAAS8oB,GAAejb,EAAGC,OAC5B,IAAI3O,KAAK0O,KAAa,aAAN1O,KAAsBA,KAAK2O,GAAI,SAAO,IACtD,IAAI3O,KAAK2O,KAAa,aAAN3O,GAAoB0O,EAAE1O,KAAO2O,EAAE3O,GAAI,SAAO,Q,CCdzD,SAAS4pB,GAAczoB,QACxBpB,MAAQoB,C,EAEdyoB,GAAcjpB,UAAY,IAAIiN,GAENic,wBACxBD,GAAcjpB,UAAUyS,sBAAwB,SAASrT,EAAOyG,UACxDmjB,GAAexqB,KAAKY,MAAOA,IAAU4pB,GAAexqB,KAAKqH,MAAOA,E,ECVxE,IAAIsjB,GAAc7d,MAClBA,MAAgB,SAAAoB,GACXA,EAAM5F,MAAQ4F,EAAM5F,UAAmB4F,EAAML,MAChDK,EAAMtN,MAAMiN,IAAMK,EAAML,IACxBK,EAAML,IAAM,MAET8c,IAAaA,GAAYzc,E,EAIX,oBAAV1K,QACPA,OAAOonB,KACPpnB,OAAOonB,IAAI,qBAkCLC,IC9CFC,GAAgBhe,MACtBA,MAAsB,SAASpN,EAAOqT,EAAUnD,MAC3ClQ,EAAMG,aAEL6P,EACAxB,EAAQ6E,EAEJ7E,EAAQA,UACVwB,EAAYxB,QAAqBwB,aAChB,MAAjBqD,QACHA,MAAgBnD,MAChBmD,MAAqBnD,OAGfF,MAA2BhQ,EAAOqT,GAI5C+X,GAAcprB,EAAOqT,EAAUnD,E,EAGhC,IAAMmb,GAAaje,EAAQqE,QAuE3B,SAAgB6Z,cAEgB,EAAAhrB,KAC1BirB,EAAc,KAAAjrB,KAAAoO,IACQ,I,CAoIrB,SAAS8c,GAAUhd,OAErBwB,EAAYxB,gBACTwB,GAAaA,OAAwBA,MAAqBxB,E,CCjOlE,SAAgBid,UACV/rB,EAAQ,KAAAY,KACRorB,EAAO,I,CDcbte,EAAQqE,QAAU,SAASjD,OAEpBwB,EAAYxB,MACdwB,GAAaA,OAChBA,QAOGA,QAAaxB,QAChBA,EAAM5F,KAAO,MAGVyiB,IAAYA,GAAW7c,E,GAiE5B8c,GAASxpB,UAAY,IAAIiN,OAOa,SAAS4c,EAASC,OACjDC,EAAsBD,MAGtBpc,EAAIlP,KAEW,MAAjBkP,EAAE+b,IACL/b,EAAE+b,EAAc,IAEjB/b,EAAE+b,EAAY7gB,KAAKmhB,OAEbrsB,EAAUgsB,GAAUhc,OAEtBsc,KACEC,EAAa,WACdD,IAEJA,KACAD,MAAiC,KAE7BrsB,EACHA,EAAQwsB,GAERA,I,EAIFH,MAAiCE,MAE3BC,EAAuB,iBACrBxc,MAA2B,IAG7BA,EAAE7H,UAAkB,KACjBskB,EAAiBzc,EAAE7H,UACzB6H,UAAmB,GA5EvB,SAAS0c,EAAe1d,EAAO2d,EAAgBC,UAC1C5d,IACHA,MAAkB,KAClBA,MACCA,OACAA,MAAgBoT,KAAI,SAAAvS,UACnB6c,EAAe7c,EAAO8c,EAAgBC,E,IAGpC5d,OACCA,YAAgC2d,IAC/B3d,OACH4d,EAAeja,aAAa3D,MAAYA,OAEzCA,aACAA,UAA8B4d,IAK1B5d,C,CApBR,CA6EKyd,EACAA,UACAA,U,CAAAA,IAMET,MAFJhc,EAAEiH,SAAS,CAAA9H,IAAea,MAAwB,OAG1Cgc,EAAYhc,EAAE+b,EAAY3f,OACjC4f,EAAU1U,a,GAUPuV,OAAeT,MAChBpc,SAAgC6c,GACpC7c,EAAEiH,SAAS,CAAA9H,IAAea,MAAwBA,UAAmB,KAEtEmc,EAAQxrB,KAAK4rB,EAAYA,E,EAG1BT,GAASxpB,UAAUsU,qBAAuB,WAAA9V,KACpCirB,EAAc,E,EAQpBD,GAASxpB,UAAUkS,OAAS,SAAS9S,EAAOyG,MACvCrH,SAA0B,IAIzBA,aAAuB,KACpB6rB,EAAiBnX,SAAS/G,cAAc,OACxCqe,EAAoBhsB,aAAsB,GAAAisB,IAAAjsB,KAAAksB,IAAAC,IAC1B,GArJzB,SAASC,EAAcle,EAAO2d,EAAgB/b,UACzC5B,IACCA,OAAoBA,YACvBA,aAA+BjL,SAAQ,SAAAopB,GACR,mBAAnBA,OAA+BA,O,IAG3Cne,UAA2B,MAIJ,OADxBA,EAAQZ,GAAO,GAAIY,UAEdA,YAAgC4B,IACnC5B,UAA8B2d,GAE/B3d,MAAmB,MAGpBA,MACCA,OACAA,MAAgBoT,KAAI,SAAAvS,UACnBqd,EAAcrd,EAAO8c,EAAgB/b,E,KAIjC5B,C,CAzBR,CAsJIlO,SACA6rB,EACCG,MAAuCA,M,CAAAA,SAIf,I,CAAA,IAKtBxG,EACLne,OAAoBsG,EAAca,EAAU,KAAM5N,EAAM4kB,iBACrDA,IAAUA,MAAsB,MAE7B,CACN7X,EAAca,EAAU,KAAMnH,MAAmB,KAAOzG,EAAMgN,UAC9D4X,E,EChMF,IAAMtmB,GAAU,SAACotB,EAAMvd,EAAOvB,QACvBA,EAdgB,KAcSA,EAfR,IAqBtB8e,EAAKlB,EAAKhG,OAAOrW,GAQhBud,EAAK1rB,MAAM2rB,cACmB,MAA9BD,EAAK1rB,MAAM2rB,YAAY,KAAcD,EAAKlB,EAAKrF,UAQjDvY,EAAO8e,EAAKltB,EACLoO,GAAM,MACLA,EAAK1M,OAAS,GACpB0M,EAAKlC,KAALkC,MAEGA,EA1CiB,GA0CMA,EA3CL,SA8CtB8e,EAAKltB,EAAQoO,EAAOA,EA5CJ,E,IAmDlB2d,GAAa3pB,UAAY,IAAIiN,OAEO,SAASM,OACtCud,EAAOtsB,KACPwsB,EAAYtB,GAAUoB,OAExB9e,EAAO8e,EAAKlB,EAAKjU,IAAIpI,UACzBvB,EA5DuB,KA8DhB,SAAAif,OACAC,EAAmB,WACnBJ,EAAK1rB,MAAM2rB,aAKf/e,EAAKpD,KAAKqiB,GACVvtB,GAAQotB,EAAMvd,EAAOvB,IAHrBif,G,EAMED,EACHA,EAAUE,GAEVA,G,GAKHvB,GAAa3pB,UAAUkS,OAAS,SAAS9S,QACnCxB,EAAQ,KAAAY,KACRorB,EAAO,IAAI9T,QAEV1J,EAAW0D,EAAa1Q,EAAMgN,UAChChN,EAAM2rB,aAAwC,MAAzB3rB,EAAM2rB,YAAY,IAI1C3e,EAASvC,cAIL,IAAIxK,EAAI+M,EAAS9M,OAAQD,UAYxBuqB,EAAKtU,IAAIlJ,EAAS/M,GAAKb,KAAKZ,EAAQ,CAAC,EAAG,EAAGY,KAAKZ,WAE/CwB,EAAMgN,Q,EAGdud,GAAa3pB,UAAU2S,mBAAqBgX,GAAa3pB,UAAUuS,kBAAoB,eAAA4Y,EAAA3sB,UAOjForB,EAAKnoB,SAAQ,SAACuK,EAAMuB,GACxB7P,GAAQ0oB,EAAM7Y,EAAOvB,E,KAAAA,ICnHVof,GACM,oBAAVppB,QAAyBA,OAAOonB,KAAOpnB,OAAOonB,IAAI,kBAC1D,MAEKiC,+OAEAC,GAA6B,oBAAbpY,SAYtBjG,EAAUjN,UAAUurB,iBAAmB,GASvC,CACC,qBACA,4BACA,uBACC9pB,SAAQ,SAAA3D,GACT6B,OAAOC,eAAeqN,EAAUjN,UAAWlC,EAAK,CAC/C2B,gBACAkW,sBACQnX,KAAK,UAAYV,E,EAEzBwX,aAAIU,GACHrW,OAAOC,eAAepB,KAAMV,EAAK,CAChC2B,gBACAC,YACAzB,MAAO+X,G,OAiCX,IAAIwV,GAAelgB,EAAQgG,MAS3B,SAASma,MAET,SAASC,YACDltB,KAAKmtB,Y,CAGb,SAASC,YACDptB,KAAKqtB,gB,CAfbvgB,EAAQgG,MAAQ,SAAA5N,UACX8nB,KAAc9nB,EAAI8nB,GAAa9nB,IACnCA,EAAEooB,QAAUL,GACZ/nB,EAAEgoB,qBAAuBA,GACzBhoB,EAAEkoB,mBAAqBA,GACfloB,EAAEqoB,YAAcroB,C,EAazB,IAAIsoB,GAAsB,CACzBvsB,gBACAkW,sBACQnX,KAAK6lB,K,GAIV4H,GAAe3gB,EAAQoB,MAC3BpB,EAAQoB,MAAQ,SAAAA,OA1FU5F,EA2FrBA,EAAO4F,EAAM5F,KACb1H,EAAQsN,EAAMtN,MACdkN,EAAkBlN,KAGF,iBAAT0H,EAAmB,KACvBolB,OAAmBplB,EAAKsG,QAAQ,SAGjC,IAAI/N,KAFTiN,EAAkB,GAEJlN,EAAO,KAChBnB,EAAQmB,EAAMC,GAEdisB,IAAgB,aAANjsB,GAA6B,aAATyH,GAInB,UAANzH,GAAiB,iBAAkBD,GAAkB,MAATnB,IAK9C,iBAANoB,GACA,UAAWD,GACI,MAAfA,EAAMnB,MAINoB,EAAI,QACY,aAANA,QAAoBpB,EAM9BA,EAAQ,GACE,iBAAiB6E,KAAKzD,GAChCA,EAAI,aAEJ,6BAA6ByD,KAAKzD,EAAIyH,KAjIhBA,EAkIH1H,EAAM0H,OAjIV,oBAAV9E,QAA4C,UAAA8B,EAAZ9B,wCAGtCc,KAAKgE,IAgIJzH,EAAI,UACM,aAAayD,KAAKzD,GAC5BA,EAAI,YACM,YAAYyD,KAAKzD,GAC3BA,EAAI,aACM,6BAA6ByD,KAAKzD,GAC5CA,EAAIA,EAAEyR,cACIob,GAAoBb,GAAYvoB,KAAKzD,GAC/CA,EAAIA,EAAEwR,mBAAoB,OAAOC,cACb,OAAV7S,IACVA,UAGDqO,EAAgBjN,GAAKpB,E,CAKb,UAAR6I,GACAwF,EAAgB6f,UAChBvqB,MAAMC,QAAQyK,EAAgBrO,SAG9BqO,EAAgBrO,MAAQ6R,EAAa1Q,EAAMgN,UAAU3K,SAAQ,SAAA8L,GAC5DA,EAAMnO,MAAMgtB,aACX9f,EAAgBrO,MAAMmP,QAAQG,EAAMnO,MAAMnB,M,KAKjC,UAAR6I,GAAoD,MAAhCwF,EAAgB+f,eACvC/f,EAAgBrO,MAAQ6R,EAAa1Q,EAAMgN,UAAU3K,SAAQ,SAAA8L,GAE3DA,EAAMnO,MAAMgtB,SADT9f,EAAgB6f,aAElB7f,EAAgB+f,aAAajf,QAAQG,EAAMnO,MAAMnB,OAGjDqO,EAAgB+f,cAAgB9e,EAAMnO,MAAMnB,K,KAKhDyO,EAAMtN,MAAQkN,EAEVlN,EAAMilB,OAASjlB,EAAMktB,YACxBN,GAAoBxsB,WAAa,cAAeJ,EACzB,MAAnBA,EAAMktB,YAAmBhgB,EAAgB+X,MAAQjlB,EAAMktB,WAC3D3sB,OAAOC,eAAe0M,EAAiB,YAAa0f,I,CAItDtf,EAAM6f,SAAWnB,GAEba,IAAcA,GAAavf,E,EAKhC,IAAMqb,GAAkBzc,MACxBA,MAAkB,SAASoB,GACtBqb,IACHA,GAAgBrb,GAEEA,K,EClHpB,IC/FM8f,GAAc,CAClBC,MAAO,UACPC,KAAM,SAGOC,GZVZ,SYUYC,G,wCAAMC,I,wCAIZ3R,WAAaY,GAAKZ,WAAW5Z,QAAO,SAAC6b,GACxC,OAAQA,EAAShe,M,IAGnBinB,EAAKvgB,MAAQ,CACXinB,WAAY1G,EAAKlL,WAAW,GAAGpC,I,eAInChb,IAAA,a,MAAA,SAAWqf,GACT,IAAMO,EAAWP,EAATO,KAER,GAAIA,EAAM,CACR,GAAIA,EAAKqP,IACP,OACEjK,GAAC,OAAI,CACHuB,MAAM,OACN7Q,wBAAyB,CAAEwZ,OAAQtP,EAAKqP,OAK9C,GAAIrP,EAAKzB,IACP,OAAO6G,GAAC,MAAG,CAAC7G,IAAKyB,EAAKzB,K,CAI1B,IAAMd,EACJwH,GAAMzH,WAAWiC,EAASrE,KAAO6J,GAAMzH,WAAWE,OAOpD,OAAOD,EAJe,QAApB3c,KAAKY,MAAMob,MACPgS,GAAYhuB,KAAKY,MAAM6b,OACvBzc,KAAKY,MAAMob,QAEcW,C,IAGjCrd,IAAA,S,MAAA,W,WACMmvB,EAAwB,KAE5B,OACEnK,GAAC,MAAG,CACFhK,GAAG,MACHuL,MAAM,UACN,gBAAe7lB,KAAKY,MAAM8tB,SAC1BC,IAAK3uB,KAAKY,MAAM+tB,I,SAEhBrK,GAAC,MAAG,CAACuB,MAAM,gB,UACR7lB,KAAK0c,WAAW4E,KAAI,SAAC3C,EAAU9d,G,QACxB+tB,EAAQjQ,EAASta,MAAQgZ,GAAKX,WAAWiC,EAASrE,IAClDsT,GACHhG,EAAKhnB,MAAMiuB,WAAalQ,EAASrE,IAAMsN,EAAKvgB,MAAMinB,WAMrD,OAJIV,IACFa,EAAwB5tB,GAIxByjB,GAAC,SAAM,CACL,aAAYsK,EACZ,gBAAehB,QAAYvtB,EAC3BuuB,MAAOA,EACPtmB,KAAK,SACLud,MAAM,6BACNiJ,YAAa,SAAC5pB,G,OAAMA,EAAE6pB,gB,EACtBC,QAAS,WACPC,EAAKruB,MAAMouB,QAAQ,CAAErQ,WAAU9d,K,WAGhC+mB,EAAKsH,WAAWvQ,I,IAKvB2F,GAAC,MAAG,CACFuB,MAAM,MACN9T,MAAO,CACL6G,MAAO,GAAgC/V,OAA7B,IAAM7C,KAAK0c,WAAW5b,OAAO,KACvCquB,QAAkC,MAAzBV,EAAgC,EAAI,EAC7C9L,UACqB,QAAnB3iB,KAAKY,MAAM+tB,IACP,yBAAqD9rB,OAAJ,IAAxB4rB,EAA4B,MACrD,cAA0C5rB,OAAJ,IAAxB4rB,EAA4B,a,OZjG3D,CYUqCW,ICRzBC,GbFZ,SaEYjB,G,wCAAMkB,I,sDACnBhwB,IAAA,wB,MAAA,SAAsBiwB,GACpB,IAAK,IAAIjN,KAAKiN,EACZ,GAAS,YAALjN,GAEAiN,EAAUjN,IAAMtiB,KAAKY,MAAM0hB,GAC7B,OAAO,EAIX,OAAO,C,IAGThjB,IAAA,S,MAAA,WACE,OAAOU,KAAKY,MAAMgN,Q,ObhBnB,CaE8CwhB,ICU3CI,GACW,GAGFC,GdhBZ,ScgBYC,G,wCAAMC,EACP/uB,G,gBAoLZa,EAAA5C,E,gBAAA,qBAAoB,WACM,QAApB+oB,EAAKhnB,MAAM6b,OACfmL,EAAKzR,SAAS,CAAEsG,MAAOmL,EAAKgI,UAAUtK,QAAU,OAAS,S,IAgB3D7jB,EAAA5C,EAAA+oB,GAAA,sBAAqB,SAAC1iB,GACpB,IAAMkd,EAAcwF,EAAKhnB,MAAjBwhB,QAEJld,EAAEvE,QAAUyhB,IACVwF,EAAKvgB,MAAMwoB,WACbjI,EAAKkI,aAGHlI,EAAKhnB,MAAMqc,gBACb2K,EAAKhnB,MAAMqc,eAAe/X,G,IAKhCzD,EAAA5C,EAAA+oB,GAAA,mBAAkB,SAAC1iB,GACZ0iB,EAAKvgB,MAAMwoB,YACX3qB,EAAEvE,OAAOovB,QAAQ,WACpB7qB,EAAE6pB,iBACF7pB,EAAE8qB,2BAEFpI,EAAKkI,c,IAITruB,EAAA5C,EAAA+oB,GAAA,qBAAoB,SAAC1iB,GACd0iB,EAAKvgB,MAAMwoB,WACH,UAAT3qB,EAAE5F,MACJ4F,EAAE6pB,iBACF7pB,EAAE8qB,2BAEFpI,EAAKkI,a,IAwHTruB,EAAA5C,EAAA+oB,GAAA,qBAAoB,WACJA,EAAKqI,cAAcrI,EAAKvgB,MAAM6oB,MAG5CtI,EAAKzR,SAAS,CAAE+Z,IAAK,EAAC,GAAI,I,sBAG5BzuB,EAAA5C,EAAA+oB,GAAA,oBAAoB9nB,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,IACZuS,EAGE1wB,EACF2wB,EACAC,EASAH,EACAI,EAEFC,EAEC9W,EAAAC,EAAAC,EAAAE,EAAAD,EAAInC,E,qEAnBH0Y,EAAQlB,EAAKne,KAAK0f,YAAY9a,QAC1B,C,uDAEJjW,EAAY0wB,EAAV1wB,M,SACoByiB,GAAYd,OAAO3hB,G,UAAzC2wB,EAAapS,EAAAhW,KACbqoB,EAAc,WACbpB,EAAKne,KAAK2f,OAAO/a,UACtBuZ,EAAKne,KAAK2f,OAAO/a,QAAQgb,UAAY,E,EAGlCN,EAAa,C,yCACTnB,EAAK9Y,SAAS,CAAEia,gBAAeF,IAAK,EAAC,GAAI,IAAOG,I,QAQzD,IALMH,EAAMC,EAAMQ,gBAAkBR,EAAM1wB,MAAMqB,OAAS,CAAC,EAAG,GAAK,EAAC,GAAI,IACjEwvB,EAAO,IACRM,QAAUR,EAActvB,OACzByvB,EAAM,KAEL9W,GAAS,EAATC,GAAS,EAATC,OAAStZ,E,UAATwZ,EAAauW,EAAa5sB,OAAAC,cAA1BgW,GAAAG,EAAAC,EAASnQ,QAAA/J,MAAT8Z,GAAS,EAALhC,EAAJmC,EAASna,MACP6wB,EAAKxvB,QAAUyvB,EAAIzvB,QAAUmuB,EAAK4B,gBACrCN,EAAM,IACFO,aAAe,SACnBP,EAAIQ,QAAUT,EAAKxvB,OACnBwvB,EAAKlmB,KAAKmmB,IAGZA,EAAInmB,KAAKqN,G,mDARNiC,GAAS,EAATC,EAASqE,EAAAoC,G,4BAAT3G,GAAS,MAATI,EAASE,QAATF,EAASE,S,sBAATL,EAAS,C,sBAATC,E,gEAWLsV,EAAK+B,cACL/B,EAAK9Y,SAAS,CAAEia,cAAeE,EAAMJ,OAAOG,G,8EAG9C5uB,EAAA5C,EAAA+oB,GAAA,uBAAsB,SAAC1iB,GAErB,IAAMirB,EAAQjrB,EAAE+rB,cAGhB,OAFA/rB,EAAE8qB,2BAEM9qB,EAAE5F,KACR,IAAK,YAGHsoB,EAAKsJ,SAAS,CAAEhsB,IAAGirB,QAAOgB,MAAM,IAChC,MAEF,IAAK,aAGHvJ,EAAKsJ,SAAS,CAAEhsB,IAAGirB,QAAOiB,OAAO,IACjC,MAEF,IAAK,UAGHxJ,EAAKsJ,SAAS,CAAEhsB,IAAGirB,QAAOkB,IAAI,IAC9B,MAEF,IAAK,YAGHzJ,EAAKsJ,SAAS,CAAEhsB,IAAGirB,QAAOmB,MAAM,IAChC,MAEF,IAAK,QACHpsB,EAAE6pB,iBACFnH,EAAK2J,iBAAiB,CAAErsB,IAAGgrB,IAAKtI,EAAKvgB,MAAM6oB,MAC3C,MAEF,IAAK,SACHhrB,EAAE6pB,iBACEnH,EAAKvgB,MAAM+oB,cACbxI,EAAK4J,cAEL5J,EAAK6J,gB,IASbhwB,EAAA5C,EAAA+oB,GAAA,eAAc,WACZ,IAAMuI,EAAQvI,EAAK9W,KAAK0f,YAAY9a,QAC/Bya,IAELA,EAAM1wB,MAAQ,GACd0wB,EAAMuB,QAEN9J,EAAK+J,oB,IAiJPlwB,EAAA5C,EAAA+oB,GAAA,uBAAsB,SAAArN,G,IAAGoE,EAAQpE,EAARoE,SAAU9d,EAAC0Z,EAAD1Z,EACjC+mB,EAAKgK,SAAc,GAAL/wB,EAAS,CAAE0vB,KAAK,GAAO,CAAEjC,WAAY3P,EAASrE,I,IA0B9D7Y,EAAA5C,EAAA+oB,GAAA,aAAY,SAAC1iB,GACX,IACM2sB,EADoB3sB,EAAlB+rB,cACmBa,wB,OAE3BlK,EAAKzR,SAAS,CAAE0Z,UAAWgC,GAAQ/xB,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,IAI3BmU,E,kFAFAvO,GAAM,G,UAENuO,EAAOC,EAAKlhB,KAAKihB,KAAKrc,QACnB,C,gDAETqc,EAAKE,UAAUC,OAAO,UACtBF,EAAKlhB,KAAKqhB,cAAczc,QAAQgc,QAEhCM,EAAKhjB,KAAKwD,iBAAiB,QAASwf,EAAKI,iBAAiB,GAC1DJ,EAAKhjB,KAAKwD,iBAAiB,UAAWwf,EAAKK,mBAAmB,G,+CAtnBhEzK,EAAK0K,UAAY,GAEjB1K,EAAKvgB,MAAQ7E,EAAA,CACX0tB,IAAK,EAAC,GAAI,GACVzV,QAASmN,EAAK2K,mBAAmB3xB,GACjC4xB,YAAa,CAAE,GAAG,IACf5K,EAAK6K,gBAAgB7xB,I,eAI5BtB,IAAA,kB,MAAA,W,IAAgBsB,EAAAV,UAAAY,OAAA,YAAAZ,UAAA,GAAkBA,UAAA,GAAVF,KAAKY,MAC3B,MAAO,CACL2b,KAAM1F,GAAMM,IAAI,SAAWvW,EAAM2b,KACjCE,MAAOzc,KAAK0yB,UAAU9xB,EAAM6b,O,IAIhCnd,IAAA,qB,MAAA,WACEU,KAAK2uB,IAAMtR,GAAKsV,IAAM,MAAQ,MAC9B3yB,KAAK8Q,KAAO,CACVihB,KrC4CE,CAAErc,QAAS,MqC3Cbkd,WrC2CE,CAAEld,QAAS,MqC1Cb+a,OrC0CE,CAAE/a,QAAS,MqCzCb0L,OrCyCE,CAAE1L,QAAS,MqCxCb8a,YrCwCE,CAAE9a,QAAS,MqCvCbmd,erCuCE,CAAEnd,QAAS,MqCtCbyc,crCsCE,CAAEzc,QAAS,OqCnCf1V,KAAK8yB,WAGwB,GAA3B9yB,KAAKY,MAAMuc,cACkB,UAA7Bnd,KAAKY,MAAM0b,iBAEXiC,QAAQC,KACN,sFAGFxe,KAAKY,MAAM0b,eAAiB,S,IAIhChd,IAAA,oB,MAAA,WAKE,GAJAU,KAAK+yB,WAEL/yB,KAAK6oB,WAAa7oB,KAAKgP,KAAKvB,WAExBzN,KAAKY,MAAM2a,UAAW,CACxB,IAAMiV,EAAkBxwB,KAAK8Q,KAArB0f,YACJA,EAAY9a,SACd8a,EAAY9a,QAAQgc,O,KAK1BpyB,IAAA,4B,MAAA,SAA0BiwB,G,WAGxB,IAAK,IAAMyD,KAFXhzB,KAAKizB,YAAcjzB,KAAKizB,UAAY,IAEpB1D,EACdvvB,KAAKizB,UAAUD,GAAKzD,EAAUyD,GAGhChJ,aAAahqB,KAAKkzB,gBAClBlzB,KAAKkzB,eAAiBxc,YAAW,WAC/B,IAAIyc,GAAoB,EAExB,IAAK,IAAM7Q,KAAKsF,EAAKqL,UACnBrL,EAAKhnB,MAAM0hB,GAAKsF,EAAKqL,UAAU3Q,GAErB,WAANA,GAAwB,eAANA,IACpB6Q,GAAoB,UAIjBvL,EAAKqL,UACZ,IAAMA,EAAYrL,EAAK6K,kBAEvB,GAAIU,EACF,OAAOvL,EAAKrd,MAAM0oB,GAGpBrL,EAAKzR,SAAS8c,E,OAIlB3zB,IAAA,uB,MAAA,WACEU,KAAKmoB,Y,IAGD7oB,IAAA,Q,MAAN,W,IAAY2zB,EAAA/yB,UAAAY,OAAA,YAAAZ,UAAA,GAAcA,UAAA,GAAF,G,cAAxBJ,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,kFACQQ,GAAKwJ,EAAKhnB,O,OAEhBgnB,EAAKkL,WACLlL,EAAKwL,YAELxL,EAAKzR,SAAS8c,GAAW,WACvBrL,EAAKyL,oBACLzL,EAAK0L,a,8CART,E,IAYAh0B,IAAA,W,MAAA,WACEoV,SAASlC,iBAAiB,QAASxS,KAAKuzB,oBACxCvzB,KAAKwzB,S,IAGPl0B,IAAA,a,MAAA,W,IAEEuO,EADA6G,SAAS/B,oBAAoB,QAAS3S,KAAKuzB,oBAC7B,QAAd1lB,EAAA7N,KAAK4vB,iBAAS,IAAd/hB,KAAgB8E,oBAAoB,SAAU3S,KAAKyzB,mBACnDzzB,KAAKozB,W,IAGP9zB,IAAA,U,MAAA,WACEU,KAAKqzB,oBACLrzB,KAAKszB,a,IAGPh0B,IAAA,Y,MAAA,W,IAAUuO,EAAA3N,UAAAY,OAAA,YAAAZ,UAAA,GAAoBA,UAAA,GAAF,GAAEwzB,EAApB7lB,EAAE8lB,cAAM,IAAAD,EAAG,GAAEA,EAChBtwB,MAAMC,QAAQswB,KACjBA,EAAS,CAACA,I,IAGPla,GAAc,EAAdC,GAAc,EAAdC,OAActZ,E,IAAnB,QAAKuZ,EAAAC,EAAkB7Z,KAAKsyB,UAAS9uB,OAAAC,cAAhCgW,GAAAG,EAAAC,EAAcnQ,QAAA/J,MAAd8Z,GAAc,EAAoB,CAAlC,IAAMma,EAANha,EAAcna,MACbk0B,EAAO1b,SAAS2b,IACpBA,EAASC,Y,WAFNna,GAAc,EAAdC,EAAcvZ,C,aAAdqZ,GAAc,MAAdI,EAAcE,QAAdF,EAAcE,Q,YAAdL,E,MAAAC,C,EAKL3Z,KAAKsyB,UAAY,GAAGzvB,OAAO8wB,E,IAG7Br0B,IAAA,W,MAAA,W,WACQod,EAAiBY,GAAfZ,WAER1c,KAAK8Q,KAAK4L,WAAa,IAAIpF,IAE3B,IAAMwc,EAASxW,GAAKZ,WAAW4E,KAAI,SAAC3C,G,OAAaA,EAASrE,E,IAAIuH,KAAK,KAC/D7hB,KAAK8zB,QAAU9zB,KAAK8zB,QAAUA,GAChC9zB,KAAK8Q,KAAK2f,OAAO/a,UAAY1V,KAAK8Q,KAAK2f,OAAO/a,QAAQgb,UAAY,GAEpE1wB,KAAK8zB,OAASA,EAEd9zB,KAAKswB,KAAO,GACZtwB,KAAKswB,KAAKM,QAAU,EAEpB,IAAMmD,EAAS,SAACtN,EAAM9H,GACpB,IAAM4R,EAAM,GACZA,EAAIO,aAAenS,EAASrE,GAC5BiW,EAAIQ,QAAUtK,EAAK3lB,OACnB8mB,EAAK0I,KAAKlmB,KAAKmmB,GAEf,IAAMyD,EAAWpM,EAAK0I,KAAKxvB,OAAS,EAC9BmzB,EAASD,EAAWxE,GAA4B,GrCxFpD,CAAE9Z,QAAS,MqC6Fb,OAJAue,EAAOC,MAAQF,EACfC,EAAOE,SAAWvM,EAAK0I,KAAKM,QAAU,EACtCnK,EAAKrc,KAAK6pB,GAEH1D,C,EAGJ9W,GAAY,EAAZC,GAAY,EAAZC,OAAYtZ,E,IAAjB,QAAKuZ,EAAAC,EAAgB6C,EAAUlZ,OAAAC,cAA1BgW,GAAAG,EAAAC,EAAYnQ,QAAA/J,MAAZ8Z,GAAY,EAAgB,CAA5B,IAAIwF,EAAJrF,EAAYna,MACT20B,EAAO,GACTC,EAAMN,EAAOK,EAAMnV,GAElBK,GAAS,EAATC,GAAS,EAATC,OAASnf,E,IAAd,QAAKqf,EAAAD,EAAaR,EAASuB,OAAMhd,OAAAC,cAA5B6b,GAAAI,EAAAD,EAAS/V,QAAA/J,MAAT2f,GAAS,EAAqB,CAA9B,IAAI7H,EAAJiI,EAASjgB,MACR40B,EAAIvzB,QAAUd,KAAK6wB,eACrBwD,EAAMN,EAAOK,EAAMnV,IAGrBjf,KAAKswB,KAAKM,SAAW,EACrByD,EAAIjqB,KAAKqN,E,WANN8H,GAAS,EAATC,EAASpf,C,aAATkf,GAAS,MAATG,EAAS1F,QAAT0F,EAAS1F,Q,YAATwF,E,MAAAC,C,EASLxf,KAAK8Q,KAAK4L,WAAW5F,IAAImI,EAAS3E,GAAI,CAAE9E,KrC7GtC,CAAEE,QAAS,MqC6G8C+Q,KAAA2N,G,WAbxD1a,GAAY,EAAZC,EAAYvZ,C,aAAZqZ,GAAY,MAAZI,EAAYE,QAAZF,EAAYE,Q,YAAZL,E,MAAAC,C,MAsBPra,IAAA,Y,MAAA,SAAUmd,GACR,GAAa,QAATA,EAAiB,OAAOA,EAE5B,IAAKzc,KAAK4vB,UAAW,CAEnB,GADA5vB,KAAK4vB,UAAY0E,WAAW,gCACxBt0B,KAAK4vB,UAAU2E,MAAMhP,MAAK,QAAU,MAAO,QAE/CvlB,KAAK4vB,UAAUpd,iBAAiB,SAAUxS,KAAKyzB,kB,CAGjD,OAAOzzB,KAAK4vB,UAAUtK,QAAU,OAAS,O,IAqC3ChmB,IAAA,qB,MAAA,W,IAAmBsB,EAAAV,UAAAY,OAAA,YAAAZ,UAAA,GAAkBA,UAAA,GAAVF,KAAKY,M,OAC9B,GAAKA,EAAM4a,aAAX,CACA,IAAQ4G,EAA6BxhB,EAA7BwhB,QAASzG,EAAoB/a,EAApB+a,gBAEX6Y,EAAmB,WACvB,IAAM5b,EAAYwJ,EAAQ0P,wBAAlBlZ,MACR,OAAON,KAAKC,MAAMK,EAAQ+C,E,EAGtBiY,EAAW,IAAIa,gBAAe,W,QAClCC,EAAKtB,UAAU,CAAEO,OAAQC,IACzBc,EAAKve,SAAS,CAAEsE,QAAS+Z,MAAsB,W,QAC7CG,EAAK7B,WACL6B,EAAKne,aAAY,WACfoR,EAAKyL,oBACLzL,EAAK0L,a,UAQX,OAHAM,EAASJ,QAAQpR,GACjBpiB,KAAKsyB,UAAUloB,KAAKwpB,GAEbY,GAtBkB,C,IAyB3Bl1B,IAAA,a,MAAA,WACE,OAAOU,KAAKqH,MAAMoT,SAAWza,KAAKY,MAAM6Z,O,IAG1Cnb,IAAA,gB,MAAA,SAAcib,G,IAAAiH,EAAQjd,EAARgW,EAAQ,GAAP1Y,EAAD2f,EAAQ,GAAHoT,EAALpT,EAAQ,GACd8O,EAAOtwB,KAAKqH,MAAM+oB,eAAiBpwB,KAAKswB,KACxC7Y,EAAQ6Y,EAAKzuB,IAAOyuB,EAAKzuB,GAAI+yB,GAEnC,GAAKnd,EACL,OAAOyK,GAAY/K,IAAIM,E,IAGzBnY,IAAA,oB,MAAA,WACE,IAAMszB,EAAa5yB,KAAK8Q,KAAK8hB,WAAWld,QACxC,GAAKkd,EAAL,CAEA,IAAMiC,EAAoB,IAAIvd,IAOxBwd,EAAkB,CACtBtf,KAAMxV,KAAK8Q,KAAK2f,OAAO/a,QACvBqf,UAAW,CAAC,EAAK,IAGbnB,EAAW,IAAIoB,sBAAqB,SAACC,G,IACpCxb,GAAW,EAAXC,GAAW,EAAXC,OAAWtZ,E,IAAhB,QAAKuZ,EAAAC,EAAeob,EAAOzxB,OAAAC,cAAtBgW,GAAAG,EAAAC,EAAWnQ,QAAA/J,MAAX8Z,GAAW,EAAa,CAAxB,IAAM3P,EAAN8P,EAAWna,MACR6a,EAAKxQ,EAAMnJ,OAAOu0B,QAAQ5a,GAChCua,EAAkB/d,IAAIwD,EAAIxQ,EAAMqrB,kB,WAF7Bzb,GAAW,EAAXC,EAAWvZ,C,aAAXqZ,GAAW,MAAXI,EAAWE,QAAXF,EAAWE,Q,YAAXL,E,MAAAC,C,EAKL,IAjB0B2U,EAiBpB8G,EAAU1wB,EAAGmwB,GACdhV,GAAiB,EAAjBC,GAAiB,EAAjBC,OAAiB1f,E,IAAtB,QAAK4f,EAAAD,EAAqBoV,EAAM5xB,OAAAC,cAA3Boc,GAAAI,EAAAD,EAAiBtW,QAAA/J,MAAjBkgB,GAAiB,GAAjB,IAAA/F,EAAAvV,EAAA0b,EAAiBxgB,MAAA,GAAV41B,EAAEvb,EAAA,GACZ,GADmBA,EAAA,GACR,EAnBawU,EAoBH+G,IAnBLzC,EAAWvrB,MAAMinB,YACjCsE,EAAWzc,SAAS,CAAEmY,eAmBpB,K,YAHCxO,GAAiB,EAAjBC,EAAiBrZ,C,aAAjBmZ,GAAiB,MAAjBG,EAAiBjG,QAAjBiG,EAAiBjG,Q,YAAjB+F,E,MAAAC,C,KAMJ+U,GAEEQ,GAAc,EAAdC,GAAc,EAAdC,OAAcn1B,E,IAAnB,QAAKo1B,EAAAC,EAAkB11B,KAAK8Q,KAAK4L,WAAW5T,SAAQtF,OAAAC,cAA/C6xB,GAAAG,EAAAC,EAAchsB,QAAA/J,MAAd21B,GAAc,GAAd,IAAM9f,EAANigB,EAAch2B,MAAN+V,KACXoe,EAASJ,QAAQhe,EAAKE,Q,WADnB6f,GAAc,EAAdC,EAAcp1B,C,aAAdk1B,GAAc,MAAdI,EAAc3b,QAAd2b,EAAc3b,Q,YAAdwb,E,MAAAC,C,EAILx1B,KAAKsyB,UAAUloB,KAAKwpB,EAjCH,C,IAoCnBt0B,IAAA,c,MAAA,W,WACQkzB,EAAchwB,EAAA,GAAKxC,KAAKqH,MAAMmrB,aAE9BoB,EAAW,IAAIoB,sBACnB,SAACC,G,IACMxb,GAAW,EAAXC,GAAW,EAAXC,OAAWtZ,E,IAAhB,QAAKuZ,EAAAC,EAAeob,EAAOzxB,OAAAC,cAAtBgW,GAAAG,EAAAC,EAAWnQ,QAAA/J,MAAX8Z,GAAW,EAAa,CAAxB,IAAM3P,EAAN8P,EAAWna,MACRy0B,EAAQpT,SAAShX,EAAMnJ,OAAOu0B,QAAQhB,OAExCpqB,EAAM6rB,eACRnD,EAAY0B,IAAS,SAEd1B,EAAY0B,E,WANlBxa,GAAW,EAAXC,EAAWvZ,C,aAAXqZ,GAAW,MAAXI,EAAWE,QAAXF,EAAWE,Q,YAAXL,E,MAAAC,C,EAULiO,EAAKzR,SAAS,CAAEqc,e,GAElB,CACEhd,KAAMxV,KAAK8Q,KAAK2f,OAAO/a,QACvBkgB,WAAY,GAEF/yB,OADR7C,KAAKY,MAAM+a,iBAAmB6T,GAA4B,GAC3D,WAAgE3sB,OAAvD7C,KAAKY,MAAM+a,gBAAkB6T,GAA0B,QAIhEqG,GAAc,EAAdC,GAAc,EAAdC,OAAc11B,E,IAAnB,QAAK21B,EAAAC,EAAkBj2B,KAAK8Q,KAAK4L,WAAW5T,SAAQtF,OAAAC,cAA/CoyB,GAAAG,EAAAC,EAAcvsB,QAAA/J,MAAdk2B,GAAc,EAAmC,CAAjD,IAAMpP,EAANuP,EAAcv2B,MAANgnB,KACNyP,GAAS,EAATC,GAAS,EAATC,OAAS/1B,E,IAAd,QAAKg2B,EAAAC,EAAa7P,EAAIjjB,OAAAC,cAAjByyB,GAAAG,EAAAC,EAAS5sB,QAAA/J,MAATu2B,GAAS,GAAT,IAAM3F,EAAN8F,EAAS52B,MACR8wB,EAAI7a,SACNke,EAASJ,QAAQjD,EAAI7a,Q,WAFpBygB,GAAS,EAATC,EAASh2B,C,aAAT81B,GAAS,MAATI,EAASvc,QAATuc,EAASvc,Q,YAAToc,E,MAAAC,C,aADFN,GAAc,EAAdC,EAAc31B,C,aAAdy1B,GAAc,MAAdI,EAAclc,QAAdkc,EAAclc,Q,YAAd+b,E,MAAAC,C,EAQL/1B,KAAKsyB,UAAUloB,KAAKwpB,E,IAGtBt0B,IAAA,iB,MAAA,SAAe4F,GACbA,EAAE6pB,gB,IAwGJzvB,IAAA,gB,MAAA,WACE,IAAM6wB,EAAQnwB,KAAK8Q,KAAK0f,YAAY9a,QAC/Bya,GAELA,EAAMoG,M,IAGRj3B,IAAA,W,MAAA,SAASib,G,IAAErV,EAAFqV,EAAErV,EAAGirB,EAAL5V,EAAK4V,MAAOgB,EAAZ5W,EAAY4W,KAAMC,EAAlB7W,EAAkB6W,MAAOC,EAAzB9W,EAAyB8W,GAAIC,EAA7B/W,EAA6B+W,K,OAC9BhB,EAAOtwB,KAAKqH,MAAM+oB,eAAiBpwB,KAAKswB,KAC9C,GAAKA,EAAKxvB,OAAV,CAEA,IAAe01B,EAAcjyB,EAAdvE,KAAKqH,MAAM6oB,IAAG,GAAxBruB,EAAU20B,EAAc,GAApB5B,EAAM4B,EAAc,GAEvBtG,EAAM,WACV,GAAU,GAANruB,GACQ,GAAN+yB,IAAY1vB,EAAEuxB,SAAWtF,GAAQE,GACnC,OAAO,KAIX,IAAU,GAANxvB,EACF,OACGqD,EAAEuxB,SACFrF,IAASE,GACVnB,EAAMQ,gBAAkBR,EAAM1wB,MAAMqB,OAK/B,KAHE,CAAC,EAAG,GAMf,GAAIqwB,GAAQC,EAAO,CACjB,IAAIb,EAAMD,EAAKzuB,GACT60B,EAAYvF,GAAO,EAAK,EAG9B,IAAKZ,EADLqE,GAAM8B,GACQ,CAIZ,KAFAnG,EAAMD,EADNzuB,GAAM60B,IAOJ,OAHA70B,EAAKsvB,EAAO,EAAIb,EAAKxvB,OAAS,EAC9B8zB,EAAKzD,EAAO,EAAIb,EAAKzuB,GAAIf,OAAS,EAE3B,CAACe,EAAI+yB,GAGdA,EAAKzD,EAAOZ,EAAIzvB,OAAS,EAAI,C,CAG/B,MAAO,CAACe,EAAI+yB,E,CAGd,GAAIvD,GAAMC,EAAM,CAEd,IAAMqF,EAAMrG,EADZzuB,GAAMwvB,GAAK,EAAK,GAGhB,OAAKsF,GAOAA,EAAI/B,KACPA,EAAK+B,EAAI71B,OAAS,GAGb,CAACe,EAAI+yB,KAVV/yB,EAAKwvB,EAAK,EAAIf,EAAKxvB,OAAS,EAC5B8zB,EAAKvD,EAAK,EAAIf,EAAKzuB,GAAIf,OAAS,EAEzB,CAACe,EAAI+yB,G,EAjDN,GA4DR1E,GACFhrB,EAAE6pB,iBASJ/uB,KAAKmW,SAAS,CAAE+Z,MAAK0G,UAAU,IAAQ,WACrChP,EAAKgK,SAAS,CAAErB,IAAKL,EAAI,I,KARrBlwB,KAAKqH,MAAM6oB,IAAI,IAAK,GACtBlwB,KAAKmW,SAAS,CAAE+Z,IAAK,EAAC,GAAI,IApEZ,C,IA+EpB5wB,IAAA,W,MAAA,SAASib,G,IAAE+T,EAAF/T,EAAE+T,WAAYiC,EAAdhW,EAAcgW,IACfD,EAAOtwB,KAAKqH,MAAM+oB,eAAiBpwB,KAAKswB,KAC9C,GAAKA,EAAKxvB,OAAV,CAEA,IAAM2vB,EAASzwB,KAAK8Q,KAAK2f,OAAO/a,QAC1BmhB,EAAapG,EAAOqB,wBAEtBpB,EAAY,EAMhB,GAJIH,GAAO,IACTjC,EAAagC,EAAKC,GAAKO,cAGrBxC,EAKFoC,GAHE1wB,KAAK8Q,KAAKwd,IAAetuB,KAAK8Q,KAAK4L,WAAWvF,IAAImX,GAAY9Y,MACvCE,QAAQoc,wBAERgF,KAAOD,EAAWC,IAAMrG,EAAOC,WAAa,EAGvE,GAAIH,GAAO,EACT,GAAKA,EAEE,CACL,IACMwG,EAASrG,EADEJ,EAAKC,GAAKQ,QACW/wB,KAAKY,MAAM+a,gBAC3Cqb,EACJD,EACA/2B,KAAKY,MAAM+a,gBACkB,IAA7B3b,KAAKY,MAAM+a,gBAEb,GAAIob,EAAStG,EAAOC,UAClBA,EAAYqG,MACP,MAAIC,EAASvG,EAAOC,UAAYmG,EAAWhe,QAGhD,OAFA6X,EAAYsG,EAASH,EAAWhe,MAEhC,C,MAdF6X,EAAY,EAmBhB1wB,KAAKgxB,cACLP,EAAOC,UAAYA,CAzCD,C,IA4CpBpxB,IAAA,c,MAAA,W,WACEU,KAAKi3B,gBAAiB,EACtBjN,aAAahqB,KAAKk3B,kBAClBl3B,KAAKk3B,iBAAmBxgB,YAAW,kBAC1BkR,EAAKqP,c,GACX,I,IAOL33B,IAAA,kB,MAAA,SAAgB4wB,GACVlwB,KAAKi3B,gBAAkBj3B,KAAKqH,MAAMwoB,WACtC7vB,KAAKmW,SAAS,CAAE+Z,IAAKA,GAAO,EAAC,GAAI,GAAK0G,UAAU,G,IAGlDt3B,IAAA,mB,MAAA,SAAiBib,G,IAAErV,EAAFqV,EAAErV,EAAGuS,EAAL8C,EAAK9C,MAAOyY,EAAZ3V,EAAY2V,IAC3B,GAAKlwB,KAAKY,MAAMsc,iBAEXzF,GAASyY,IACZzY,EAAQzX,KAAKiwB,cAAcC,IAGzBzY,GAAO,CACT,IAAMqM,EAAYF,GAAanM,EAAO,CAAEmI,UAAW5f,KAAKqH,MAAMkV,KAAO,IAEjEvc,KAAKY,MAAM4Z,iBACbL,GAAeC,IAAI0J,EAAW9jB,KAAKY,OAGrCZ,KAAKY,MAAMsc,cAAc4G,EAAW5e,E,KAuBxC5F,IAAA,a,MAAA,WACOU,KAAKqH,MAAMwoB,YAChB7vB,KAAKmW,SAAS,CAAE0Z,UAAW,KAAMsH,SAAU,OAE3Cn3B,KAAKgP,KAAK2D,oBAAoB,QAAS3S,KAAKoyB,iBAC5CpyB,KAAKgP,KAAK2D,oBAAoB,UAAW3S,KAAKqyB,mB,IAGhD/yB,IAAA,sB,MAAA,SAAoB63B,GAClBn3B,KAAKmW,SAAS,CAAEghB,Y,IAGlB73B,IAAA,kB,MAAA,SAAgBid,GACdvc,KAAKgxB,cACLhxB,KAAK8vB,aAEL9vB,KAAKmW,SAAS,CAAEoG,OAAM4a,SAAU,OAChCtgB,GAAMC,IAAI,OAAQyF,E,IAGpBjd,IAAA,Y,MAAA,WACE,OACEglB,GAAC6J,GAAU,CAETtgB,IAAK7N,KAAK8Q,KAAK8hB,WACf5W,MAAOhc,KAAKY,MAAMob,MAClBS,MAAOzc,KAAKqH,MAAMoV,MAClBkS,IAAK3uB,KAAK2uB,IACVE,YAAa7uB,KAAKqH,MAAM+oB,cACxB1B,SAAU1uB,KAAKY,MAAMsb,YACrB8S,QAAShvB,KAAKo3B,qBAPTp3B,KAAK8zB,O,IAYhBx0B,IAAA,gB,MAAA,WACE,IAAMmY,EAAQzX,KAAKiwB,cAAcjwB,KAAKqH,MAAM6oB,KACtCmH,EACJr3B,KAAKqH,MAAM+oB,gBAAkBpwB,KAAKqH,MAAM+oB,cAActvB,OAExD,OACEwjB,GAAC,MAAG,CACFhK,GAAG,UACHuL,MAAM,mBACN8I,IAAK3uB,KAAK2uB,IACV,gBAAe3uB,KAAKY,MAAMyb,gB,UAE1BiI,GAAC,MAAG,CAACuB,MAAM,6B,UACTvB,GAAC,MAAG,CACFuB,MAAM,yCACN9T,MAAO,CACL8G,OAAQ7Y,KAAKY,MAAM+a,gBACnBwK,SAAUnmB,KAAKY,MAAM+a,iB,SAGvB2I,GAACe,GAAK,CACJ5N,MAAOA,EACP6C,GACE+c,EACIr3B,KAAKY,MAAMub,gBAAkB,MAC7Bnc,KAAKY,MAAMwb,eACoB,OAA9Bpc,KAAKY,MAAMyb,gBACR,aACA,YAEVvF,IAAK9W,KAAKY,MAAMkW,IAChBiP,KAAM/lB,KAAKY,MAAM+a,gBACjBY,KAAMvc,KAAKqH,MAAM8vB,UAAYn3B,KAAKqH,MAAMkV,KACxCoJ,aAAa,EACb5I,kBAAmB/c,KAAKY,MAAMmc,sBAIlCuH,GAAC,MAAG,CAACuB,MAAO,UAAsBhjB,OAAZ7C,KAAK2uB,IAAI,I,SAE3BrK,GAAC,MADF7M,GAAS4f,EACJ,CAACxR,MAAO,WAAgChjB,OAArB7C,KAAK2uB,IAAI,GAAG,WAAqB9rB,OAAZ7C,KAAK2uB,IAAI,I,UACnDrK,GAAC,MAAG,CAACuB,MAAM,yB,SACRpO,EAAQA,EAAMpT,KAAOgZ,GAAKia,sBAE7BhT,GAAC,MAAG,CAACuB,MAAM,oC,SACRpO,EAAQA,EAAMsK,MAAM,GAAGC,WAAa3E,GAAKka,wBAI1C,CAAC1R,MAAM,8B,SAA+BxI,GAAKma,aAKnD/f,GAC+B,WAA/BzX,KAAKY,MAAM4b,kBACXxc,KAAKy3B,yB,IAKbn4B,IAAA,oB,MAAA,SAAkBmY,EAAO8C,G,IrB5uBDhL,EAAQC,EqB4uBL0gB,EAAF3V,EAAE2V,IAAKiE,EAAP5Z,EAAO4Z,SAAU7D,EAAjB/V,EAAiB+V,K,OAClCvK,EAAO/lB,KAAKY,MAAM+a,gBAClBY,EAAOvc,KAAKqH,MAAM8vB,UAAYn3B,KAAKqH,MAAMkV,KAEzC2D,GADYzI,EAAMsK,MAAMxF,EAAO,IAAM9E,EAAMsK,MAAM,IAC9B7B,OACnB0N,GrBjvBgBre,EqBivBKvP,KAAKqH,MAAM6oB,IrBjvBR1gB,EqBivBa0gB,ErB/uB3C9sB,MAAMC,QAAQkM,IACdnM,MAAMC,QAAQmM,IACdD,EAAEzO,SAAW0O,EAAE1O,QACfyO,EAAEmoB,OAAM,SAACC,EAAKzD,G,OAAUyD,GAAOnoB,EAAE0kB,E,KqB6uB3B50B,EAAM4wB,EAAIrtB,OAAO4U,EAAM6C,IAAIuH,KAAK,IAEtC,OACEyC,GAAC+K,GAAmB,CAAiBzB,WAAUrR,OAAMwJ,O,SACnDzB,GAAC,SAAM,CACL,aAAYpE,EACZ,gBAAe0N,QAAYvtB,EAC3B,gBAAe8zB,EACf,eAAc7D,EAAKM,QACnB,gBAAe5wB,KAAKqH,MAAMuvB,SAC1BhI,MAAqC,QAA9B5uB,KAAKY,MAAMyb,gBAA4B5E,EAAMpT,UAAOhE,EAC3DiI,KAAK,SACLud,MAAM,+BACN+R,SAAS,KACT5I,QAAS,SAAC9pB,G,OAAM0iB,EAAK2J,iBAAiB,CAAErsB,IAAGuS,S,EAC3CogB,aAAc,W,OAAMjQ,EAAKkQ,gBAAgB5H,E,EACzC6H,aAAc,W,OAAMnQ,EAAKkQ,iB,EACzB/lB,MAAO,CACL6G,MAAO5Y,KAAKY,MAAM+a,gBAClB9C,OAAQ7Y,KAAKY,MAAM+a,gBACnBwK,SAAUnmB,KAAKY,MAAMgb,UACrBoc,WAAY,G,UAGd1T,GAAC,MAAG,CACF,cAAY,OACZuB,MAAM,aACN9T,MAAO,CACLkmB,aAAcj4B,KAAKY,MAAM8a,kBACzBwc,gBAAiBl4B,KAAKY,MAAM6a,kBACxBzb,KAAKY,MAAM6a,mBACR0Y,EAAW,GAAKn0B,KAAKY,MAAM6a,kBAAkB3a,aAEhDT,KAGRikB,GAACe,GAAK,CACJ5N,MAAOA,EACPX,IAAK9W,KAAKY,MAAMkW,IAChBiP,KAAM/lB,KAAKY,MAAMgb,UACjBW,KAAMA,EACNoJ,aAAa,EACb5I,kBAAmB/c,KAAKY,MAAMmc,wBAvCVzd,E,IA8C9BA,IAAA,e,MAAA,WACE,IAAM64B,EAC0B,QAA9Bn4B,KAAKY,MAAMyb,iBACoB,UAA/Brc,KAAKY,MAAM4b,iBAEb,OACE8H,GAAC,MAAG,C,UACFA,GAAC,MAAG,CAACuB,MAAM,WACXvB,GAAC,MAAG,CAACuB,MAAM,mB,UACTvB,GAAC,MAAG,CAACuB,MAAM,4B,UACTvB,GAAC,QAAK,CACJhc,KAAK,SACLuF,IAAK7N,KAAK8Q,KAAK0f,YACf4H,YAAa/a,GAAK+D,OAClB4N,QAAShvB,KAAKq4B,kBACdC,QAASt4B,KAAK2xB,kBACd4G,UAAWv4B,KAAKw4B,oBAChBC,aAAa,QAEfnU,GAAC,OAAI,CAACuB,MAAM,kB,SAAmB1B,GAAM/C,OAAO+D,QAC3CnlB,KAAKqH,MAAM+oB,eACV9L,GAAC,SAAM,CACLsK,MAAM,QACN,aAAW,QACXtmB,KAAK,SACLud,MAAM,mBACNmJ,QAAShvB,KAAKwxB,YACd1C,YAAa9uB,KAAK+uB,e,SAEjB5K,GAAM/C,OAAOgE,YAKnB+S,GAAkBn4B,KAAKy3B,4B,IAMhCn4B,IAAA,sB,MAAA,W,WACQ8wB,EAAoBpwB,KAAKqH,MAAvB+oB,cACR,OAAKA,EAGH9L,GAAC,MAAG,CAACuB,MAAM,WAAWhY,IAAK7N,KAAK8Q,KAAKsQ,O,UACnCkD,GAAC,MAAG,CAACuB,MAAO,8BAA0ChjB,OAAZ7C,KAAK2uB,IAAI,I,SAChDtR,GAAKX,WAAW0E,SAEnBkD,GAAC,MAAG,C,SACA8L,EAActvB,OAOdsvB,EAAc9O,KAAI,SAACiP,EAAK1vB,G,QACtB,OACEyjB,GAAC,MAAG,CAACuB,MAAM,O,SACR0K,EAAIjP,KAAI,SAAC7J,EAAOihB,GACf,OAAOC,EAAKC,kBAAkBnhB,EAAO,CACnCyY,IAAK,CAACrvB,EAAG63B,GACTvE,SAAUtzB,EAAI83B,EAAK/3B,MAAM6Z,QAAUie,EAAK,EACxCpI,KAAMF,G,SAbhB9L,GAAC,MAAG,CAACuB,MAAO,uBAAmChjB,OAAZ7C,KAAK2uB,IAAI,I,SACzC3uB,KAAKY,MAAMoc,kBACVsH,GAAC,IAAC,CAAC0K,QAAShvB,KAAKY,MAAMoc,iB,SAAmBK,GAAKwb,oBAXhC,I,IAkC7Bv5B,IAAA,mB,MAAA,W,WACQod,EAAiBY,GAAfZ,WACFoc,IAAW94B,KAAKqH,MAAM+oB,cACtB3V,EAAUza,KAAK6wB,aAErB,OACEvM,GAAC,MAAG,CACFvS,MAAO,CACLgnB,WAAYD,EAAS,cAAWz4B,EAChC4lB,QAAS6S,EAAS,YAASz4B,EAC3BwY,OAAQ,Q,SAGT6D,EAAW4E,KAAI,SAAC3C,G,QACQqa,EAAAC,EAAKnoB,KAAK4L,WAAWvF,IAAIwH,EAASrE,IAAjD9E,EAAewjB,EAAfxjB,KAAMiR,EAASuS,EAATvS,KAEd,OACEnC,GAAC,MAAG,CACF,UAAS3F,EAAShe,OAASge,EAAShe,OAAO2Z,GAAKqE,EAASrE,GACzDuL,MAAM,WACNhY,IAAK2H,E,UAEL8O,GAAC,MAAG,CAACuB,MAAO,8BAA0ChjB,OAAZo2B,EAAKtK,IAAI,I,SAChDhQ,EAASta,MAAQgZ,GAAKX,WAAWiC,EAASrE,MAE7CgK,GAAC,MAAG,CACFuB,MAAM,WACN9T,MAAO,CACL8G,OAAQ4N,EAAK3lB,OAASm4B,EAAKr4B,MAAM+a,iB,SAGlC8K,EAAKnF,KAAI,SAACiP,EAAK1vB,G,IAeZq4B,E,IAdIC,EACJ5I,EAAI2D,MAAS3D,EAAI2D,MAAQ1E,GACrB4J,EAAUC,EAAKhyB,MAAMmrB,YAAY2G,GACjCtrB,EAAM,YAAa0iB,EAAMA,OAAMlwB,EAErC,IAAK+4B,IAAYvrB,EACf,OAAO,KAGT,IAAMyrB,EAAQz4B,EAAI4Z,EACZ8e,EAAMD,EAAQ7e,EACdC,EAAWiE,EAAS6B,OAAOpc,MAAMk1B,EAAOC,GAM9C,OAJI7e,EAAS5Z,OAAS2Z,IACpBye,EAAAxe,GAAStQ,KAATjK,MAAA+4B,EAAcx0B,EAAG,IAAItB,MAAMqX,EAAUC,EAAS5Z,UAI9CwjB,GAAC,MAAG,CAEF,aAAYiM,EAAI2D,MAChBrmB,IAAKA,EACLgY,MAAM,WACN9T,MAAO,CAAE+kB,IAAKj2B,EAAIw4B,EAAKz4B,MAAM+a,iB,SAE5Byd,GACC1e,EAAS4G,KAAI,SAACjH,EAASqe,GACrB,IAAKre,EACH,OACEiK,GAAC,MAAG,CACFvS,MAAO,CACL6G,MAAOgP,EAAKhnB,MAAM+a,gBAClB9C,OAAQ+O,EAAKhnB,MAAM+a,mBAM3B,IAAMlE,EAAQyK,GAAY/K,IAAIkD,GAE9B,OAAOuN,EAAKgR,kBAAkBnhB,EAAO,CACnCyY,IAAK,CAACK,EAAI2D,MAAOwE,GACjBvE,SAAU5D,EAAI4D,SAAWuE,EACzBpI,KAAM1I,EAAK0I,M,KAxBZC,EAAI2D,M,iBAsC7B50B,IAAA,uB,MAAA,WACE,MAAmC,QAA/BU,KAAKY,MAAM4b,iBACN,KAIP8H,GAAC,MAAG,CACFuB,MAAM,yCACN9T,MAAO,CACL2c,SAAU,WACV9V,MAAO5Y,KAAKY,MAAM+a,gBAClB9C,OAAQ7Y,KAAKY,MAAM+a,iB,SAGrB2I,GAAC,SAAM,CACLhc,KAAK,SACLuF,IAAK7N,KAAK8Q,KAAK+hB,eACfhN,MAAM,0DACN,gBAAe7lB,KAAKqH,MAAMwoB,UAAY,QAAKxvB,EAC3C,aAAYgd,GAAK0E,MAAMyX,OACvB5K,MAAOvR,GAAK0E,MAAMyX,OAClBxK,QAAShvB,KAAKy5B,UACd1nB,MAAO,CACL6G,MAAO5Y,KAAKY,MAAMgb,UAClB/C,OAAQ7Y,KAAKY,MAAMgb,W,SAGrB0I,GAAC,OAAI,CAACuB,MAAO,uBAAuChjB,OAAhB7C,KAAKqH,MAAMkV,W,IAMvDjd,IAAA,mB,MAAA,WACE,IAAMmY,EAAQzX,KAAKiwB,cAAcjwB,KAAKqH,MAAM6oB,KAG5C,OACE5L,GAAC,MAAG,CAAC,YAAU,SAASuB,MAAM,U,SAHfpO,EAAQA,EAAMpT,KAAO,I,IASxC/E,IAAA,c,MAAA,W,WAEQo6B,EADiB15B,KAAK8Q,KAAK+hB,eAAend,QACNoc,wBACpC6H,EAAW35B,KAAKgP,KAAK8iB,wBAErBpD,EAAW,GAkBjB,MAhBgB,OAAZ1uB,KAAK2uB,IACPD,EAAS0C,MAAQuI,EAASvI,MAAQsI,EAAmBtI,MAAQ,EAE7D1C,EAASyC,KAAOuI,EAAmBvI,KAAOwI,EAASxI,KAAO,EAI5B,UAA9BnxB,KAAKY,MAAMyb,iBACoB,WAA/Brc,KAAKY,MAAM4b,iBAEXkS,EAASkL,OAASD,EAASC,OAASF,EAAmB5C,IAAM,GAE7DpI,EAASoI,IAAM4C,EAAmBE,OAASD,EAAS7C,IAAM,EAC1DpI,EAASkL,OAAS,QAIlBtV,GAAC,MAAG,CACFzW,IAAK7N,KAAK8Q,KAAKihB,KACf8H,KAAK,aACLlL,IAAK3uB,KAAK2uB,IACV,aAAYtR,GAAK0E,MAAMyX,OACvB3T,MAAM,cACN,gBAAe6I,EAASoI,IAAM,MAAQ,SACtC/kB,MAAO2c,E,SAELhqB,EAAGtB,MAAM,GAAGT,QAAQ2e,KAAI,SAACzgB,G,QACnB0b,EAAO1b,EAAI,EACXwU,EAAUuS,EAAKvgB,MAAMkV,MAAQA,EAEnC,OACE+H,GAAC,MAAG,C,UACFA,GAAC,QAAK,CACJhc,KAAK,QACLjE,KAAK,YACL5E,MAAO8c,EACP,aAAYc,GAAK0E,MAAMxF,GACvB1O,IAAKwH,EAAUuS,EAAK9W,KAAKqhB,cAAgB,KACzC2H,eAAgBzkB,EAChB0kB,SAAU,W,OAAMC,EAAKC,oBAAoB1d,E,EACzCgc,UAAW,SAACrzB,GAEE,SAAVA,EAAEg1B,MACQ,SAAVh1B,EAAEg1B,MACQ,OAAVh1B,EAAEg1B,OAEFh1B,EAAE6pB,iBACFiL,EAAKG,gBAAgB5d,G,IAK3B+H,GAAC,SAAM,CACL,cAAY,OACZsT,SAAS,KACT5I,QAAS,W,OAAMgL,EAAKG,gBAAgB5d,E,EACpCsb,aAAc,W,OAAMmC,EAAKC,oBAAoB1d,E,EAC7Cwb,aAAc,W,OAAMiC,EAAKC,qB,EACzBpU,MAAM,oC,UAENvB,GAAC,OAAI,CAACuB,MAAO,uBAA4BhjB,OAAL0Z,KACpC+H,GAAC,OAAI,CAACuB,MAAM,kB,SAAmBxI,GAAK0E,MAAMxF,U,SASxDjd,IAAA,S,MAAA,WACE,IAAM86B,EAAYp6B,KAAKY,MAAM6Z,QAAUza,KAAKY,MAAM+a,gBAElD,OACE2I,GAAC,UAAO,CACNhK,GAAG,OACHuL,MAAM,mBACN8I,IAAK3uB,KAAK2uB,IACV5c,MAAO,CACL6G,MAAO5Y,KAAKY,MAAM4a,aACd,OACA,QAAkB3Y,OAAVu3B,EAAU,kDAExB,iBAAgBp6B,KAAKY,MAAMkW,IAC3B,aAAY9W,KAAKqH,MAAMoV,MACvB,YAAWzc,KAAKqH,MAAMwoB,UAAY,QAAKxvB,E,UAER,OAA9BL,KAAKY,MAAMyb,iBAA4Brc,KAAKq6B,gBAClB,OAA1Br6B,KAAKY,MAAMsb,aAAwBlc,KAAKs6B,YACX,UAA7Bt6B,KAAKY,MAAM0b,gBACVgI,GAAC,MAAG,CAACuB,MAAM,a,SAAc7lB,KAAKu6B,iBAGhCjW,GAAC,MAAG,CAACzW,IAAK7N,KAAK8Q,KAAK2f,OAAQ5K,MAAM,8B,SAChCvB,GAAC,MAAG,CACFvS,MAAO,CACL6G,MAAO5Y,KAAKY,MAAM4a,aAAe,OAAS4e,EAC1CvhB,OAAQ,Q,UAGoB,UAA7B7Y,KAAKY,MAAM0b,gBAA8Btc,KAAKu6B,eAC9Cv6B,KAAKw6B,sBACLx6B,KAAKy6B,wBAIiB,UAA1Bz6B,KAAKY,MAAMsb,aAA2Blc,KAAKs6B,YACb,UAA9Bt6B,KAAKY,MAAMyb,iBAA+Brc,KAAKq6B,gBAC/Cr6B,KAAKqH,MAAMwoB,WAAa7vB,KAAK06B,cAC7B16B,KAAK26B,qB,Od1mCX,CcgBiCC,GCRrBC,GAqBZ,SArBYC,G,wCAAMC,EAGPn6B,G,6BACJA,EAAO,CAAE2nB,OAAQ5K,EAAAqd,K,cAGnB17B,IAAA,oB,MAAN,W,kBAAAQ,EAAA6d,EAAAlR,GAAAzB,MAAA,SAAA4S,I,IACQhd,E,yEAAAA,EAAQuhB,GAASyF,EAAKhnB,MAAO0a,GAAWsM,IACxCxF,QAAOwF,EACbhnB,EAAMiN,IAAM,SAAC6B,GACXkY,EAAKlY,UAAYA,C,WAGb0O,GAAKxd,G,WACPgnB,EAAKM,aAAY,C,gDAErBe,GAAO3E,GAACmL,GAAMjtB,EAAA,GAAK5B,IAAWgnB,EAAKiB,Y,0CAVrC,E,OAcC,CArBwCT,IACzC3mB,EADmBo5B,GACZ,QAAQvf,IAqBW,oBAAnB4N,gBACNA,eAAe/R,IAAI,oBAEpB+R,eAAeziB,OAAO,kBAAmBo0B,I,UCjC3CG,GAAiB,0gWCCjBjkB,OAAOkkB,UAAYC,C", "sources": ["node_modules/@swc/helpers/src/_assert_this_initialized.mjs", "node_modules/@swc/helpers/src/_async_to_generator.mjs", "node_modules/@swc/helpers/src/_class_call_check.mjs", "node_modules/@swc/helpers/src/_create_class.mjs", "node_modules/@swc/helpers/src/_define_property.mjs", "node_modules/@swc/helpers/src/_set_prototype_of.mjs", "node_modules/@swc/helpers/src/_inherits.mjs", "node_modules/@swc/helpers/src/_object_spread.mjs", "node_modules/@swc/helpers/src/_array_with_holes.mjs", "node_modules/@swc/helpers/src/_iterable_to_array.mjs", "node_modules/@swc/helpers/src/_non_iterable_rest.mjs", "node_modules/@swc/helpers/src/_array_like_to_array.mjs", "node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs", "node_modules/@swc/helpers/src/_sliced_to_array.mjs", "node_modules/@swc/helpers/src/_array_without_holes.mjs", "node_modules/@swc/helpers/src/_non_iterable_spread.mjs", "node_modules/@swc/helpers/src/_to_consumable_array.mjs", "node_modules/@swc/helpers/src/_is_native_reflect_construct.mjs", "node_modules/@swc/helpers/src/_get_prototype_of.mjs", "node_modules/@swc/helpers/src/_type_of.mjs", "node_modules/@swc/helpers/src/_possible_constructor_return.mjs", "node_modules/@swc/helpers/src/_create_super.mjs", "node_modules/regenerator-runtime/runtime.js", "node_modules/preact/src/constants.js", "node_modules/preact/src/util.js", "node_modules/preact/src/options.js", "node_modules/preact/src/create-element.js", "node_modules/preact/src/component.js", "node_modules/preact/src/diff/children.js", "node_modules/preact/src/diff/props.js", "node_modules/preact/src/diff/index.js", "node_modules/preact/src/render.js", "node_modules/preact/src/diff/catch-error.js", "node_modules/preact/jsx-runtime/src/index.js", "packages/emoji-mart/src/helpers/store.ts", "packages/emoji-mart/src/helpers/native-support.ts", "packages/emoji-mart/src/helpers/frequently-used.ts", "packages/emoji-mart-data/i18n/en.json", "packages/emoji-mart/src/components/Picker/PickerProps.ts", "packages/emoji-mart/src/config.ts", "packages/emoji-mart/src/helpers/search-index.ts", "packages/emoji-mart/src/helpers/index.ts", "packages/emoji-mart/src/utils.ts", "packages/emoji-mart/src/icons.tsx", "packages/emoji-mart/src/components/Emoji/Emoji.tsx", "node_modules/@swc/helpers/src/_construct.mjs", "node_modules/@swc/helpers/src/_wrap_native_super.mjs", "node_modules/@swc/helpers/src/_is_native_function.mjs", "packages/emoji-mart/src/components/HTMLElement/HTMLElement.ts", "<<jsx-config-pragma.js>>", "packages/emoji-mart/src/components/HTMLElement/ShadowElement.ts", "packages/emoji-mart/src/components/Emoji/EmojiProps.ts", "packages/emoji-mart/src/components/Emoji/EmojiElement.jsx", "node_modules/preact/hooks/src/index.js", "node_modules/preact/compat/src/util.js", "node_modules/preact/compat/src/PureComponent.js", "node_modules/preact/compat/src/forwardRef.js", "node_modules/preact/compat/src/suspense.js", "node_modules/preact/compat/src/suspense-list.js", "node_modules/preact/compat/src/render.js", "node_modules/preact/compat/src/index.js", "packages/emoji-mart/src/components/Navigation/Navigation.tsx", "packages/emoji-mart/src/components/HOCs/PureInlineComponent.ts", "packages/emoji-mart/src/components/Picker/Picker.tsx", "packages/emoji-mart/src/components/Picker/PickerElement.tsx", "node_modules/@parcel/runtime-js/lib/bundles/runtime-645d004d79b88736.js", "packages/emoji-mart/src/browser.js"], "sourcesContent": ["export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n", "function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\n\nexport default function _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n\n      _next(undefined);\n    });\n  };\n}\n", "export default function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nexport default function _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n", "export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n", "function setPrototypeOf(o, p) {\n  setPrototypeOf = Object.setPrototypeOf || function setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return setPrototypeOf(o, p);\n}\n\nexport default function _setPrototypeOf(o, p) {\n  return setPrototypeOf(o, p);\n}\n", "import setPrototypeOf from './_set_prototype_of.mjs';\n\nexport default function _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n", "import defineProperty from './_define_property.mjs';\n\nexport default function _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n\n    ownKeys.forEach(function (key) {\n      defineProperty(target, key, source[key]);\n    });\n  }\n\n  return target;\n}", "export default function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n", "export default function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n", "export default function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n", "export default function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\n", "import _arrayLikeToArray from './_array_like_to_array.mjs';\n\nexport default function _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(n);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))\n    return _arrayLikeToArray(o, minLen);\n}\n", "import arrayWithHoles from './_array_with_holes.mjs';\nimport iterableToArrayLimit from './_iterable_to_array.mjs';\nimport nonIterableRest from './_non_iterable_rest.mjs';\nimport unsupportedIterableToArray from './_unsupported_iterable_to_array.mjs';\n\nexport default function _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\n", "import _arrayLikeToArray from './_array_like_to_array.mjs';\n\nexport default function _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n", "export default function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n", "import arrayWithoutHoles from './_array_without_holes.mjs';\nimport iterableToArray from './_iterable_to_array.mjs';\nimport nonIterableSpread from './_non_iterable_spread.mjs';\nimport unsupportedIterableToArray from './_unsupported_iterable_to_array.mjs';\n\nexport default function _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\n", "export default function _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () { }));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}", "function getPrototypeOf(o) {\n  getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return getPrototypeOf(o);\n}\n\nexport default function _getPrototypeOf(o) {\n  return getPrototypeOf(o);\n}", "export default function _typeof(obj) {\n    \"@swc/helpers - typeof\";\n    return obj && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n", "import assertThisInitialized from './_assert_this_initialized.mjs';\nimport _typeof from './_type_of.mjs';\n\nexport default function _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}\n", "import _isNativeReflectConstruct from \"./_is_native_reflect_construct.mjs\";\nimport _getPrototypeOf from \"./_get_prototype_of.mjs\";\nimport _possibleConstructorReturn from './_possible_constructor_return.mjs';\n\nexport default function _createSuper(Derived) {\n    var hasNativeReflectConstruct = _isNativeReflectConstruct();\n    return function _createSuperInternal() {\n        var Super = _getPrototypeOf(Derived),\n            result;\n        if (hasNativeReflectConstruct) {\n            var NewTarget = _getPrototypeOf(this).constructor;\n            result = Reflect.construct(Super, arguments, NewTarget);\n        } else {\n            result = Super.apply(this, arguments);\n        }\n        return _possibleConstructorReturn(this, result);\n    };\n}", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  define(Gp, \"constructor\", GeneratorFunctionPrototype);\n  define(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction);\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "export const EMPTY_OBJ = {};\r\nexport const EMPTY_ARR = [];\r\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\r\n", "import { EMPTY_ARR } from \"./constants\";\r\n\r\n/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\t// @ts-ignore We change the type of `obj` to be `O & P`\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Remove a child node from its parent if attached. This is a workaround for\r\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\r\n * is smaller than including a dedicated polyfill.\r\n * @param {Node} node The node to remove\r\n */\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n\r\nexport const slice = EMPTY_ARR.slice;\r\n", "import { _catchError } from './diff/catch-error';\r\n\r\n/**\r\n * The `option` object can potentially contain callback functions\r\n * that are called during various stages of our renderer. This is the\r\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\r\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\r\n * for a full list of available option hooks (most editors/IDEs allow you to\r\n * ctrl+click or cmd+click on mac the type definition below).\r\n * @type {import('./internal').Options}\r\n */\r\nconst options = {\r\n\t_catchError\r\n};\r\n\r\nexport default options;\r\n", "import { slice } from './util';\r\nimport options from './options';\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * Create an virtual node (used for JSX)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * constructor for this virtual node\r\n * @param {object | null | undefined} [props] The properties of the virtual node\r\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createElement(type, props, children) {\r\n\tlet normalizedProps = {},\r\n\t\tkey,\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'key') key = props[i];\r\n\t\telse if (i == 'ref') ref = props[i];\r\n\t\telse normalizedProps[i] = props[i];\r\n\t}\r\n\r\n\tif (arguments.length > 2) {\r\n\t\tnormalizedProps.children =\r\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\r\n\t}\r\n\r\n\t// If a Component VNode, check for and apply defaultProps\r\n\t// Note: type may be undefined in development, must never error here.\r\n\tif (typeof type == 'function' && type.defaultProps != null) {\r\n\t\tfor (i in type.defaultProps) {\r\n\t\t\tif (normalizedProps[i] === undefined) {\r\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn createVNode(type, normalizedProps, key, ref, null);\r\n}\r\n\r\n/**\r\n * Create a VNode (used internally by Preact)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * Constructor for this virtual node\r\n * @param {object | string | number | null} props The properties of this virtual node.\r\n * If this virtual node represents a text node, this is the text of the node (string or number).\r\n * @param {string | number | null} key The key for this virtual node, used when\r\n * diffing it against its children\r\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\r\n * receive a reference to its created child\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createVNode(type, props, key, ref, original) {\r\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\r\n\t// Do not inline into createElement and coerceToVNode!\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\r\n\t\t// be set to dom.nextSibling which can return `null` and it is important\r\n\t\t// to be able to distinguish between an uninitialized _nextDom and\r\n\t\t// a _nextDom that has been set to `null`\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: original == null ? ++vnodeId : original\r\n\t};\r\n\r\n\t// Only invoke the vnode hook if this was *not* a direct copy:\r\n\tif (original == null && options.vnode != null) options.vnode(vnode);\r\n\r\n\treturn vnode;\r\n}\r\n\r\nexport function createRef() {\r\n\treturn { current: null };\r\n}\r\n\r\nexport function Fragment(props) {\r\n\treturn props.children;\r\n}\r\n\r\n/**\r\n * Check if a the argument is a valid Preact VNode.\r\n * @param {*} vnode\r\n * @returns {vnode is import('./internal').VNode}\r\n */\r\nexport const isValidElement = vnode =>\r\n\tvnode != null && vnode.constructor === undefined;\r\n", "import { assign } from './util';\r\nimport { diff, commitRoot } from './diff/index';\r\nimport options from './options';\r\nimport { Fragment } from './create-element';\r\n\r\n/**\r\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\r\n * trigger rendering\r\n * @param {object} props The initial component props\r\n * @param {object} context The initial context from parent components'\r\n * getChildContext\r\n */\r\nexport function Component(props, context) {\r\n\tthis.props = props;\r\n\tthis.context = context;\r\n}\r\n\r\n/**\r\n * Update component state and schedule a re-render.\r\n * @this {import('./internal').Component}\r\n * @param {object | ((s: object, p: object) => object)} update A hash of state\r\n * properties to update with new values or a function that given the current\r\n * state and props returns a new partial state\r\n * @param {() => void} [callback] A function to be called once component state is\r\n * updated\r\n */\r\nComponent.prototype.setState = function(update, callback) {\r\n\t// only clone state when copying to nextState the first time.\r\n\tlet s;\r\n\tif (this._nextState != null && this._nextState !== this.state) {\r\n\t\ts = this._nextState;\r\n\t} else {\r\n\t\ts = this._nextState = assign({}, this.state);\r\n\t}\r\n\r\n\tif (typeof update == 'function') {\r\n\t\t// Some libraries like `immer` mark the current state as readonly,\r\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\r\n\t\tupdate = update(assign({}, s), this.props);\r\n\t}\r\n\r\n\tif (update) {\r\n\t\tassign(s, update);\r\n\t}\r\n\r\n\t// Skip update if updater function returned null\r\n\tif (update == null) return;\r\n\r\n\tif (this._vnode) {\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Immediately perform a synchronous re-render of the component\r\n * @this {import('./internal').Component}\r\n * @param {() => void} [callback] A function to be called after component is\r\n * re-rendered\r\n */\r\nComponent.prototype.forceUpdate = function(callback) {\r\n\tif (this._vnode) {\r\n\t\t// Set render mode so that we can differentiate where the render request\r\n\t\t// is coming from. We need this because forceUpdate should never call\r\n\t\t// shouldComponentUpdate\r\n\t\tthis._force = true;\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\r\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\r\n * @param {object} props Props (eg: JSX attributes) received from parent\r\n * element/component\r\n * @param {object} state The component's current state\r\n * @param {object} context Context object, as returned by the nearest\r\n * ancestor's `getChildContext()`\r\n * @returns {import('./index').ComponentChildren | void}\r\n */\r\nComponent.prototype.render = Fragment;\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n * @param {number | null} [childIndex]\r\n */\r\nexport function getDomSibling(vnode, childIndex) {\r\n\tif (childIndex == null) {\r\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\r\n\t\treturn vnode._parent\r\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\r\n\t\t\t: null;\r\n\t}\r\n\r\n\tlet sibling;\r\n\tfor (; childIndex < vnode._children.length; childIndex++) {\r\n\t\tsibling = vnode._children[childIndex];\r\n\r\n\t\tif (sibling != null && sibling._dom != null) {\r\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\r\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\r\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\r\n\t\t\treturn sibling._dom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we get here, we have not found a DOM node in this vnode's children.\r\n\t// We must resume from this vnode's sibling (in it's parent _children array)\r\n\t// Only climb up and search the parent if we aren't searching through a DOM\r\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\r\n\t// the search)\r\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\r\n}\r\n\r\n/**\r\n * Trigger in-place re-rendering of a component.\r\n * @param {import('./internal').Component} component The component to rerender\r\n */\r\nfunction renderComponent(component) {\r\n\tlet vnode = component._vnode,\r\n\t\toldDom = vnode._dom,\r\n\t\tparentDom = component._parentDom;\r\n\r\n\tif (parentDom) {\r\n\t\tlet commitQueue = [];\r\n\t\tconst oldVNode = assign({}, vnode);\r\n\t\toldVNode._original = vnode._original + 1;\r\n\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tvnode,\r\n\t\t\toldVNode,\r\n\t\t\tcomponent._globalContext,\r\n\t\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t\tvnode._hydrating != null ? [oldDom] : null,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom,\r\n\t\t\tvnode._hydrating\r\n\t\t);\r\n\t\tcommitRoot(commitQueue, vnode);\r\n\r\n\t\tif (vnode._dom != oldDom) {\r\n\t\t\tupdateParentDomPointers(vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n */\r\nfunction updateParentDomPointers(vnode) {\r\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\r\n\t\tvnode._dom = vnode._component.base = null;\r\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\r\n\t\t\tlet child = vnode._children[i];\r\n\t\t\tif (child != null && child._dom != null) {\r\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn updateParentDomPointers(vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * The render queue\r\n * @type {Array<import('./internal').Component>}\r\n */\r\nlet rerenderQueue = [];\r\n\r\n/**\r\n * Asynchronously schedule a callback\r\n * @type {(cb: () => void) => void}\r\n */\r\n/* istanbul ignore next */\r\n// Note the following line isn't tree-shaken by rollup cuz of rollup/rollup#2566\r\nconst defer =\r\n\ttypeof Promise == 'function'\r\n\t\t? Promise.prototype.then.bind(Promise.resolve())\r\n\t\t: setTimeout;\r\n\r\n/*\r\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\r\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\r\n * do, and when their effects will be applied. See the links below for some further reading on designing\r\n * asynchronous APIs.\r\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\r\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\r\n */\r\n\r\nlet prevDebounce;\r\n\r\n/**\r\n * Enqueue a rerender of a component\r\n * @param {import('./internal').Component} c The component to rerender\r\n */\r\nexport function enqueueRender(c) {\r\n\tif (\r\n\t\t(!c._dirty &&\r\n\t\t\t(c._dirty = true) &&\r\n\t\t\trerenderQueue.push(c) &&\r\n\t\t\t!process._rerenderCount++) ||\r\n\t\tprevDebounce !== options.debounceRendering\r\n\t) {\r\n\t\tprevDebounce = options.debounceRendering;\r\n\t\t(prevDebounce || defer)(process);\r\n\t}\r\n}\r\n\r\n/** Flush the render queue by rerendering all queued components */\r\nfunction process() {\r\n\tlet queue;\r\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\r\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\t\trerenderQueue = [];\r\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\r\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\r\n\t\tqueue.some(c => {\r\n\t\t\tif (c._dirty) renderComponent(c);\r\n\t\t});\r\n\t}\r\n}\r\nprocess._rerenderCount = 0;\r\n", "import { diff, unmount, applyRef } from './index';\r\nimport { createVNode, Fragment } from '../create-element';\r\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\r\nimport { getDomSibling } from '../component';\r\n\r\n/**\r\n * Diff the children of a virtual node\r\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\r\n * children are being diffed\r\n * @param {import('../internal').ComponentChildren[]} renderResult\r\n * @param {import('../internal').VNode} newParentVNode The new virtual\r\n * node whose children should be diff'ed against oldParentVNode\r\n * @param {import('../internal').VNode} oldParentVNode The old virtual\r\n * node whose children should be diff'ed against newParentVNode\r\n * @param {object} globalContext The current context object - modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n */\r\nexport function diffChildren(\r\n\tparentDom,\r\n\trenderResult,\r\n\tnewParentVNode,\r\n\toldParentVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\r\n\r\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\r\n\t// as EMPTY_OBJ._children should be `undefined`.\r\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\r\n\r\n\tlet oldChildrenLength = oldChildren.length;\r\n\r\n\tnewParentVNode._children = [];\r\n\tfor (i = 0; i < renderResult.length; i++) {\r\n\t\tchildVNode = renderResult[i];\r\n\r\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\r\n\t\t\tchildVNode = newParentVNode._children[i] = null;\r\n\t\t}\r\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\r\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\r\n\t\t// it's own DOM & etc. pointers\r\n\t\telse if (\r\n\t\t\ttypeof childVNode == 'string' ||\r\n\t\t\ttypeof childVNode == 'number' ||\r\n\t\t\t// eslint-disable-next-line valid-typeof\r\n\t\t\ttypeof childVNode == 'bigint'\r\n\t\t) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode\r\n\t\t\t);\r\n\t\t} else if (Array.isArray(childVNode)) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tFragment,\r\n\t\t\t\t{ children: childVNode },\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull\r\n\t\t\t);\r\n\t\t} else if (childVNode._depth > 0) {\r\n\t\t\t// VNode is already in use, clone it. This can happen in the following\r\n\t\t\t// scenario:\r\n\t\t\t//   const reuse = <div />\r\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tchildVNode.type,\r\n\t\t\t\tchildVNode.props,\r\n\t\t\t\tchildVNode.key,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode._original\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\r\n\t\t}\r\n\r\n\t\t// Terser removes the `continue` here and wraps the loop body\r\n\t\t// in a `if (childVNode) { ... } condition\r\n\t\tif (childVNode == null) {\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tchildVNode._parent = newParentVNode;\r\n\t\tchildVNode._depth = newParentVNode._depth + 1;\r\n\r\n\t\t// Check if we find a corresponding element in oldChildren.\r\n\t\t// If found, delete the array item by setting to `undefined`.\r\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\r\n\t\t// (holes).\r\n\t\toldVNode = oldChildren[i];\r\n\r\n\t\tif (\r\n\t\t\toldVNode === null ||\r\n\t\t\t(oldVNode &&\r\n\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\tchildVNode.type === oldVNode.type)\r\n\t\t) {\r\n\t\t\toldChildren[i] = undefined;\r\n\t\t} else {\r\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\r\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\r\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\r\n\t\t\t\toldVNode = oldChildren[j];\r\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\r\n\t\t\t\t// We always match by type (in either case).\r\n\t\t\t\tif (\r\n\t\t\t\t\toldVNode &&\r\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\t\tchildVNode.type === oldVNode.type\r\n\t\t\t\t) {\r\n\t\t\t\t\toldChildren[j] = undefined;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\toldVNode = null;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\toldVNode = oldVNode || EMPTY_OBJ;\r\n\r\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tchildVNode,\r\n\t\t\toldVNode,\r\n\t\t\tglobalContext,\r\n\t\t\tisSvg,\r\n\t\t\texcessDomChildren,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom,\r\n\t\t\tisHydrating\r\n\t\t);\r\n\r\n\t\tnewDom = childVNode._dom;\r\n\r\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\r\n\t\t\tif (!refs) refs = [];\r\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\r\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\r\n\t\t}\r\n\r\n\t\tif (newDom != null) {\r\n\t\t\tif (firstChildDom == null) {\r\n\t\t\t\tfirstChildDom = newDom;\r\n\t\t\t}\r\n\r\n\t\t\tif (\r\n\t\t\t\ttypeof childVNode.type == 'function' &&\r\n\t\t\t\tchildVNode._children === oldVNode._children\r\n\t\t\t) {\r\n\t\t\t\tchildVNode._nextDom = oldDom = reorderChildren(\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldDom,\r\n\t\t\t\t\tparentDom\r\n\t\t\t\t);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldVNode,\r\n\t\t\t\t\toldChildren,\r\n\t\t\t\t\tnewDom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tif (typeof newParentVNode.type == 'function') {\r\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\r\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\r\n\t\t\t\t//\r\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\r\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\r\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\r\n\t\t\t\t// node's nextSibling.\r\n\t\t\t\tnewParentVNode._nextDom = oldDom;\r\n\t\t\t}\r\n\t\t} else if (\r\n\t\t\toldDom &&\r\n\t\t\toldVNode._dom == oldDom &&\r\n\t\t\toldDom.parentNode != parentDom\r\n\t\t) {\r\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\r\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\r\n\t\t\toldDom = getDomSibling(oldVNode);\r\n\t\t}\r\n\t}\r\n\r\n\tnewParentVNode._dom = firstChildDom;\r\n\r\n\t// Remove remaining oldChildren if there are any.\r\n\tfor (i = oldChildrenLength; i--; ) {\r\n\t\tif (oldChildren[i] != null) {\r\n\t\t\tif (\r\n\t\t\t\ttypeof newParentVNode.type == 'function' &&\r\n\t\t\t\toldChildren[i]._dom != null &&\r\n\t\t\t\toldChildren[i]._dom == newParentVNode._nextDom\r\n\t\t\t) {\r\n\t\t\t\t// If the newParentVNode.__nextDom points to a dom node that is about to\r\n\t\t\t\t// be unmounted, then get the next sibling of that vnode and set\r\n\t\t\t\t// _nextDom to it\r\n\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldParentVNode, i + 1);\r\n\t\t\t}\r\n\r\n\t\t\tunmount(oldChildren[i], oldChildren[i]);\r\n\t\t}\r\n\t}\r\n\r\n\t// Set refs only after unmount\r\n\tif (refs) {\r\n\t\tfor (i = 0; i < refs.length; i++) {\r\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction reorderChildren(childVNode, oldDom, parentDom) {\r\n\t// Note: VNodes in nested suspended trees may be missing _children.\r\n\tlet c = childVNode._children;\r\n\tlet tmp = 0;\r\n\tfor (; c && tmp < c.length; tmp++) {\r\n\t\tlet vnode = c[tmp];\r\n\t\tif (vnode) {\r\n\t\t\t// We typically enter this code path on sCU bailout, where we copy\r\n\t\t\t// oldVNode._children to newVNode._children. If that is the case, we need\r\n\t\t\t// to update the old children's _parent pointer to point to the newVNode\r\n\t\t\t// (childVNode here).\r\n\t\t\tvnode._parent = childVNode;\r\n\r\n\t\t\tif (typeof vnode.type == 'function') {\r\n\t\t\t\toldDom = reorderChildren(vnode, oldDom, parentDom);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tc,\r\n\t\t\t\t\tvnode._dom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n\r\n/**\r\n * Flatten and loop through the children of a virtual node\r\n * @param {import('../index').ComponentChildren} children The unflattened\r\n * children of a virtual node\r\n * @returns {import('../internal').VNode[]}\r\n */\r\nexport function toChildArray(children, out) {\r\n\tout = out || [];\r\n\tif (children == null || typeof children == 'boolean') {\r\n\t} else if (Array.isArray(children)) {\r\n\t\tchildren.some(child => {\r\n\t\t\ttoChildArray(child, out);\r\n\t\t});\r\n\t} else {\r\n\t\tout.push(children);\r\n\t}\r\n\treturn out;\r\n}\r\n\r\nfunction placeChild(\r\n\tparentDom,\r\n\tchildVNode,\r\n\toldVNode,\r\n\toldChildren,\r\n\tnewDom,\r\n\toldDom\r\n) {\r\n\tlet nextDom;\r\n\tif (childVNode._nextDom !== undefined) {\r\n\t\t// Only Fragments or components that return Fragment like VNodes will\r\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\r\n\t\t// of last DOM child of this child VNode\r\n\t\tnextDom = childVNode._nextDom;\r\n\r\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\r\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\r\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\r\n\t\t// can clean up the property\r\n\t\tchildVNode._nextDom = undefined;\r\n\t} else if (\r\n\t\toldVNode == null ||\r\n\t\tnewDom != oldDom ||\r\n\t\tnewDom.parentNode == null\r\n\t) {\r\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\r\n\t\t\tparentDom.appendChild(newDom);\r\n\t\t\tnextDom = null;\r\n\t\t} else {\r\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\r\n\t\t\tfor (\r\n\t\t\t\tlet sibDom = oldDom, j = 0;\r\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\r\n\t\t\t\tj += 2\r\n\t\t\t) {\r\n\t\t\t\tif (sibDom == newDom) {\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tparentDom.insertBefore(newDom, oldDom);\r\n\t\t\tnextDom = oldDom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\r\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\r\n\t// See more detail in create-element.js:createVNode\r\n\tif (nextDom !== undefined) {\r\n\t\toldDom = nextDom;\r\n\t} else {\r\n\t\toldDom = newDom.nextSibling;\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n", "import { IS_NON_DIMENSIONAL } from '../constants';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff the old and new properties of a VNode and apply changes to the DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to apply\r\n * changes to\r\n * @param {object} newProps The new props\r\n * @param {object} oldProps The old props\r\n * @param {boolean} isSvg Whether or not this node is an SVG node\r\n * @param {boolean} hydrate Whether or not we are in hydration mode\r\n */\r\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\r\n\tlet i;\r\n\r\n\tfor (i in oldProps) {\r\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\r\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n\r\n\tfor (i in newProps) {\r\n\t\tif (\r\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\r\n\t\t\ti !== 'children' &&\r\n\t\t\ti !== 'key' &&\r\n\t\t\ti !== 'value' &&\r\n\t\t\ti !== 'checked' &&\r\n\t\t\toldProps[i] !== newProps[i]\r\n\t\t) {\r\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction setStyle(style, key, value) {\r\n\tif (key[0] === '-') {\r\n\t\tstyle.setProperty(key, value);\r\n\t} else if (value == null) {\r\n\t\tstyle[key] = '';\r\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\r\n\t\tstyle[key] = value;\r\n\t} else {\r\n\t\tstyle[key] = value + 'px';\r\n\t}\r\n}\r\n\r\n/**\r\n * Set a property value on a DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to modify\r\n * @param {string} name The name of the property to set\r\n * @param {*} value The value to set the property to\r\n * @param {*} oldValue The old value the property had\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\r\n */\r\nexport function setProperty(dom, name, value, oldValue, isSvg) {\r\n\tlet useCapture;\r\n\r\n\to: if (name === 'style') {\r\n\t\tif (typeof value == 'string') {\r\n\t\t\tdom.style.cssText = value;\r\n\t\t} else {\r\n\t\t\tif (typeof oldValue == 'string') {\r\n\t\t\t\tdom.style.cssText = oldValue = '';\r\n\t\t\t}\r\n\r\n\t\t\tif (oldValue) {\r\n\t\t\t\tfor (name in oldValue) {\r\n\t\t\t\t\tif (!(value && name in value)) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (value) {\r\n\t\t\t\tfor (name in value) {\r\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\r\n\telse if (name[0] === 'o' && name[1] === 'n') {\r\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\r\n\r\n\t\t// Infer correct casing for DOM built-in events:\r\n\t\tif (name.toLowerCase() in dom) name = name.toLowerCase().slice(2);\r\n\t\telse name = name.slice(2);\r\n\r\n\t\tif (!dom._listeners) dom._listeners = {};\r\n\t\tdom._listeners[name + useCapture] = value;\r\n\r\n\t\tif (value) {\r\n\t\t\tif (!oldValue) {\r\n\t\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\t\tdom.addEventListener(name, handler, useCapture);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\tdom.removeEventListener(name, handler, useCapture);\r\n\t\t}\r\n\t} else if (name !== 'dangerouslySetInnerHTML') {\r\n\t\tif (isSvg) {\r\n\t\t\t// Normalize incorrect prop usage for SVG:\r\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\r\n\t\t\t// - className --> class\r\n\t\t\tname = name.replace(/xlink[H:h]/, 'h').replace(/sName$/, 's');\r\n\t\t} else if (\r\n\t\t\tname !== 'href' &&\r\n\t\t\tname !== 'list' &&\r\n\t\t\tname !== 'form' &&\r\n\t\t\t// Default value in browsers is `-1` and an empty string is\r\n\t\t\t// cast to `0` instead\r\n\t\t\tname !== 'tabIndex' &&\r\n\t\t\tname !== 'download' &&\r\n\t\t\tname in dom\r\n\t\t) {\r\n\t\t\ttry {\r\n\t\t\t\tdom[name] = value == null ? '' : value;\r\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\r\n\t\t\t\tbreak o;\r\n\t\t\t} catch (e) {}\r\n\t\t}\r\n\r\n\t\t// ARIA-attributes have a different notion of boolean values.\r\n\t\t// The value `false` is different from the attribute not\r\n\t\t// existing on the DOM, so we can't remove it. For non-boolean\r\n\t\t// ARIA-attributes we could treat false as a removal, but the\r\n\t\t// amount of exceptions would cost us too many bytes. On top of\r\n\t\t// that other VDOM frameworks also always stringify `false`.\r\n\r\n\t\tif (typeof value === 'function') {\r\n\t\t\t// never serialize functions as attribute values\r\n\t\t} else if (\r\n\t\t\tvalue != null &&\r\n\t\t\t(value !== false || (name[0] === 'a' && name[1] === 'r'))\r\n\t\t) {\r\n\t\t\tdom.setAttribute(name, value);\r\n\t\t} else {\r\n\t\t\tdom.removeAttribute(name);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Proxy an event to hooked event handlers\r\n * @param {Event} e The event object from the browser\r\n * @private\r\n */\r\nfunction eventProxy(e) {\r\n\tthis._listeners[e.type + false](options.event ? options.event(e) : e);\r\n}\r\n\r\nfunction eventProxyCapture(e) {\r\n\tthis._listeners[e.type + true](options.event ? options.event(e) : e);\r\n}\r\n", "import { EMPTY_OBJ } from '../constants';\r\nimport { Component, getDomSibling } from '../component';\r\nimport { Fragment } from '../create-element';\r\nimport { diffChildren } from './children';\r\nimport { diffProps, setProperty } from './props';\r\nimport { assign, removeNode, slice } from '../util';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff two virtual nodes and apply proper changes to the DOM\r\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object. Modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this element is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} [isHydrating] Whether or not we are in hydration\r\n */\r\nexport function diff(\r\n\tparentDom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet tmp,\r\n\t\tnewType = newVNode.type;\r\n\r\n\t// When passing through createElement it assigns the object\r\n\t// constructor as undefined. This to prevent JSON-injection.\r\n\tif (newVNode.constructor !== undefined) return null;\r\n\r\n\t// If the previous diff bailed out, resume creating/hydrating.\r\n\tif (oldVNode._hydrating != null) {\r\n\t\tisHydrating = oldVNode._hydrating;\r\n\t\toldDom = newVNode._dom = oldVNode._dom;\r\n\t\t// if we resume, we want the tree to be \"unlocked\"\r\n\t\tnewVNode._hydrating = null;\r\n\t\texcessDomChildren = [oldDom];\r\n\t}\r\n\r\n\tif ((tmp = options._diff)) tmp(newVNode);\r\n\r\n\ttry {\r\n\t\touter: if (typeof newType == 'function') {\r\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\r\n\t\t\tlet newProps = newVNode.props;\r\n\r\n\t\t\t// Necessary for createContext api. Setting this property will pass\r\n\t\t\t// the context value as `this.context` just for this component.\r\n\t\t\ttmp = newType.contextType;\r\n\t\t\tlet provider = tmp && globalContext[tmp._id];\r\n\t\t\tlet componentContext = tmp\r\n\t\t\t\t? provider\r\n\t\t\t\t\t? provider.props.value\r\n\t\t\t\t\t: tmp._defaultValue\r\n\t\t\t\t: globalContext;\r\n\r\n\t\t\t// Get component and set it to `c`\r\n\t\t\tif (oldVNode._component) {\r\n\t\t\t\tc = newVNode._component = oldVNode._component;\r\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\r\n\t\t\t} else {\r\n\t\t\t\t// Instantiate the new component\r\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\r\n\t\t\t\t\t// @ts-ignore The check above verifies that newType is suppose to be constructed\r\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// @ts-ignore Trust me, Component implements the interface we want\r\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\r\n\t\t\t\t\tc.constructor = newType;\r\n\t\t\t\t\tc.render = doRender;\r\n\t\t\t\t}\r\n\t\t\t\tif (provider) provider.sub(c);\r\n\r\n\t\t\t\tc.props = newProps;\r\n\t\t\t\tif (!c.state) c.state = {};\r\n\t\t\t\tc.context = componentContext;\r\n\t\t\t\tc._globalContext = globalContext;\r\n\t\t\t\tisNew = c._dirty = true;\r\n\t\t\t\tc._renderCallbacks = [];\r\n\t\t\t}\r\n\r\n\t\t\t// Invoke getDerivedStateFromProps\r\n\t\t\tif (c._nextState == null) {\r\n\t\t\t\tc._nextState = c.state;\r\n\t\t\t}\r\n\t\t\tif (newType.getDerivedStateFromProps != null) {\r\n\t\t\t\tif (c._nextState == c.state) {\r\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tassign(\r\n\t\t\t\t\tc._nextState,\r\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\toldProps = c.props;\r\n\t\t\toldState = c.state;\r\n\r\n\t\t\t// Invoke pre-render lifecycle methods\r\n\t\t\tif (isNew) {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tc.componentWillMount != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillMount();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidMount != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tnewProps !== oldProps &&\r\n\t\t\t\t\tc.componentWillReceiveProps != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (\r\n\t\t\t\t\t(!c._force &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate(\r\n\t\t\t\t\t\t\tnewProps,\r\n\t\t\t\t\t\t\tc._nextState,\r\n\t\t\t\t\t\t\tcomponentContext\r\n\t\t\t\t\t\t) === false) ||\r\n\t\t\t\t\tnewVNode._original === oldVNode._original\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.props = newProps;\r\n\t\t\t\t\tc.state = c._nextState;\r\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\r\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\r\n\t\t\t\t\tc._vnode = newVNode;\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t\tnewVNode._children.forEach(vnode => {\r\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\t\t\tcommitQueue.push(c);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentWillUpdate != null) {\r\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidUpdate != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(() => {\r\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tc.context = componentContext;\r\n\t\t\tc.props = newProps;\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif ((tmp = options._render)) tmp(newVNode);\r\n\r\n\t\t\tc._dirty = false;\r\n\t\t\tc._vnode = newVNode;\r\n\t\t\tc._parentDom = parentDom;\r\n\r\n\t\t\ttmp = c.render(c.props, c.state, c.context);\r\n\r\n\t\t\t// Handle setState called in render, see #2553\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif (c.getChildContext != null) {\r\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\r\n\t\t\t}\r\n\r\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\r\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\r\n\t\t\t}\r\n\r\n\t\t\tlet isTopLevelFragment =\r\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\r\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\r\n\r\n\t\t\tdiffChildren(\r\n\t\t\t\tparentDom,\r\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\toldDom,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\tc.base = newVNode._dom;\r\n\r\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\r\n\t\t\tnewVNode._hydrating = null;\r\n\r\n\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\tcommitQueue.push(c);\r\n\t\t\t}\r\n\r\n\t\t\tif (clearProcessingException) {\r\n\t\t\t\tc._pendingError = c._processingException = null;\r\n\t\t\t}\r\n\r\n\t\t\tc._force = false;\r\n\t\t} else if (\r\n\t\t\texcessDomChildren == null &&\r\n\t\t\tnewVNode._original === oldVNode._original\r\n\t\t) {\r\n\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t} else {\r\n\t\t\tnewVNode._dom = diffElementNodes(\r\n\t\t\t\toldVNode._dom,\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\r\n\t} catch (e) {\r\n\t\tnewVNode._original = null;\r\n\t\t// if hydrating or creating initial tree, bailout preserves DOM:\r\n\t\tif (isHydrating || excessDomChildren != null) {\r\n\t\t\tnewVNode._dom = oldDom;\r\n\t\t\tnewVNode._hydrating = !!isHydrating;\r\n\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\r\n\t\t\t// ^ could possibly be simplified to:\r\n\t\t\t// excessDomChildren.length = 0;\r\n\t\t}\r\n\t\toptions._catchError(e, newVNode, oldVNode);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').VNode} root\r\n */\r\nexport function commitRoot(commitQueue, root) {\r\n\tif (options._commit) options._commit(root, commitQueue);\r\n\r\n\tcommitQueue.some(c => {\r\n\t\ttry {\r\n\t\t\t// @ts-ignore Reuse the commitQueue variable here so the type changes\r\n\t\t\tcommitQueue = c._renderCallbacks;\r\n\t\t\tc._renderCallbacks = [];\r\n\t\t\tcommitQueue.some(cb => {\r\n\t\t\t\t// @ts-ignore See above ts-ignore on commitQueue\r\n\t\t\t\tcb.call(c);\r\n\t\t\t});\r\n\t\t} catch (e) {\r\n\t\t\toptions._catchError(e, c._vnode);\r\n\t\t}\r\n\t});\r\n}\r\n\r\n/**\r\n * Diff two virtual nodes representing DOM element\r\n * @param {import('../internal').PreactElement} dom The DOM element representing\r\n * the virtual nodes being diffed\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {*} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n * @returns {import('../internal').PreactElement}\r\n */\r\nfunction diffElementNodes(\r\n\tdom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\tisHydrating\r\n) {\r\n\tlet oldProps = oldVNode.props;\r\n\tlet newProps = newVNode.props;\r\n\tlet nodeType = newVNode.type;\r\n\tlet i = 0;\r\n\r\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\r\n\tif (nodeType === 'svg') isSvg = true;\r\n\r\n\tif (excessDomChildren != null) {\r\n\t\tfor (; i < excessDomChildren.length; i++) {\r\n\t\t\tconst child = excessDomChildren[i];\r\n\r\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\r\n\t\t\t// argument matches an element in excessDomChildren, remove it from\r\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\r\n\t\t\tif (\r\n\t\t\t\tchild &&\r\n\t\t\t\t'setAttribute' in child === !!nodeType &&\r\n\t\t\t\t(nodeType ? child.localName === nodeType : child.nodeType === 3)\r\n\t\t\t) {\r\n\t\t\t\tdom = child;\r\n\t\t\t\texcessDomChildren[i] = null;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (dom == null) {\r\n\t\tif (nodeType === null) {\r\n\t\t\t// @ts-ignore createTextNode returns Text, we expect PreactElement\r\n\t\t\treturn document.createTextNode(newProps);\r\n\t\t}\r\n\r\n\t\tif (isSvg) {\r\n\t\t\tdom = document.createElementNS(\r\n\t\t\t\t'http://www.w3.org/2000/svg',\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tdom = document.createElement(\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType,\r\n\t\t\t\tnewProps.is && newProps\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// we created a new parent, so none of the previously attached children can be reused:\r\n\t\texcessDomChildren = null;\r\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\r\n\t\tisHydrating = false;\r\n\t}\r\n\r\n\tif (nodeType === null) {\r\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\r\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\r\n\t\t\tdom.data = newProps;\r\n\t\t}\r\n\t} else {\r\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\r\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\r\n\r\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\r\n\r\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\r\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\r\n\r\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\r\n\t\t// @TODO we should warn in debug mode when props don't match here.\r\n\t\tif (!isHydrating) {\r\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\r\n\t\t\t// we should read the existing DOM attributes to diff them\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\toldProps = {};\r\n\t\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\r\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (newHtml || oldHtml) {\r\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\r\n\t\t\t\tif (\r\n\t\t\t\t\t!newHtml ||\r\n\t\t\t\t\t((!oldHtml || newHtml.__html != oldHtml.__html) &&\r\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML)\r\n\t\t\t\t) {\r\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\r\n\r\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\r\n\t\tif (newHtml) {\r\n\t\t\tnewVNode._children = [];\r\n\t\t} else {\r\n\t\t\ti = newVNode.props.children;\r\n\t\t\tdiffChildren(\r\n\t\t\t\tdom,\r\n\t\t\t\tArray.isArray(i) ? i : [i],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg && nodeType !== 'foreignObject',\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\texcessDomChildren\r\n\t\t\t\t\t? excessDomChildren[0]\r\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\t// Remove children that are not part of any vnode.\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\r\n\t\t\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// (as above, don't diff props during hydration)\r\n\t\tif (!isHydrating) {\r\n\t\t\tif (\r\n\t\t\t\t'value' in newProps &&\r\n\t\t\t\t(i = newProps.value) !== undefined &&\r\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\r\n\t\t\t\t// despite the attribute not being present. When the attribute\r\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\r\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\r\n\t\t\t\t(i !== oldProps.value ||\r\n\t\t\t\t\ti !== dom.value ||\r\n\t\t\t\t\t(nodeType === 'progress' && !i))\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\r\n\t\t\t}\r\n\t\t\tif (\r\n\t\t\t\t'checked' in newProps &&\r\n\t\t\t\t(i = newProps.checked) !== undefined &&\r\n\t\t\t\ti !== dom.checked\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn dom;\r\n}\r\n\r\n/**\r\n * Invoke or update a ref, depending on whether it is a function or object ref.\r\n * @param {object|function} ref\r\n * @param {any} value\r\n * @param {import('../internal').VNode} vnode\r\n */\r\nexport function applyRef(ref, value, vnode) {\r\n\ttry {\r\n\t\tif (typeof ref == 'function') ref(value);\r\n\t\telse ref.current = value;\r\n\t} catch (e) {\r\n\t\toptions._catchError(e, vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * Unmount a virtual node from the tree and apply DOM changes\r\n * @param {import('../internal').VNode} vnode The virtual node to unmount\r\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\r\n * initiated the unmount\r\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\r\n * current element is already detached from the DOM.\r\n */\r\nexport function unmount(vnode, parentVNode, skipRemove) {\r\n\tlet r;\r\n\tif (options.unmount) options.unmount(vnode);\r\n\r\n\tif ((r = vnode.ref)) {\r\n\t\tif (!r.current || r.current === vnode._dom) applyRef(r, null, parentVNode);\r\n\t}\r\n\r\n\tif ((r = vnode._component) != null) {\r\n\t\tif (r.componentWillUnmount) {\r\n\t\t\ttry {\r\n\t\t\t\tr.componentWillUnmount();\r\n\t\t\t} catch (e) {\r\n\t\t\t\toptions._catchError(e, parentVNode);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tr.base = r._parentDom = null;\r\n\t}\r\n\r\n\tif ((r = vnode._children)) {\r\n\t\tfor (let i = 0; i < r.length; i++) {\r\n\t\t\tif (r[i]) {\r\n\t\t\t\tunmount(r[i], parentVNode, typeof vnode.type != 'function');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (!skipRemove && vnode._dom != null) removeNode(vnode._dom);\r\n\r\n\t// Must be set to `undefined` to properly clean up `_nextDom`\r\n\t// for which `null` is a valid value. See comment in `create-element.js`\r\n\tvnode._dom = vnode._nextDom = undefined;\r\n}\r\n\r\n/** The `.render()` method for a PFC backing instance. */\r\nfunction doRender(props, state, context) {\r\n\treturn this.constructor(props, context);\r\n}\r\n", "import { EMPTY_OBJ } from './constants';\r\nimport { commitRoot, diff } from './diff/index';\r\nimport { createElement, Fragment } from './create-element';\r\nimport options from './options';\r\nimport { slice } from './util';\r\n\r\n/**\r\n * Render a Preact virtual node into a DOM element\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * render into\r\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\r\n * existing DOM tree rooted at `replaceNode`\r\n */\r\nexport function render(vnode, parentDom, replaceNode) {\r\n\tif (options._root) options._root(vnode, parentDom);\r\n\r\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\r\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\r\n\t// element..\r\n\tlet isHydrating = typeof replaceNode === 'function';\r\n\r\n\t// To be able to support calling `render()` multiple times on the same\r\n\t// DOM node, we need to obtain a reference to the previous tree. We do\r\n\t// this by assigning a new `_children` property to DOM nodes which points\r\n\t// to the last rendered tree. By default this property is not present, which\r\n\t// means that we are mounting a new tree for the first time.\r\n\tlet oldVNode = isHydrating\r\n\t\t? null\r\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\r\n\r\n\tvnode = (\r\n\t\t(!isHydrating && replaceNode) ||\r\n\t\tparentDom\r\n\t)._children = createElement(Fragment, null, [vnode]);\r\n\r\n\t// List of effects that need to be called after diffing.\r\n\tlet commitQueue = [];\r\n\tdiff(\r\n\t\tparentDom,\r\n\t\t// Determine the new vnode tree and store it on the DOM element on\r\n\t\t// our custom `_children` property.\r\n\t\tvnode,\r\n\t\toldVNode || EMPTY_OBJ,\r\n\t\tEMPTY_OBJ,\r\n\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? [replaceNode]\r\n\t\t\t: oldVNode\r\n\t\t\t? null\r\n\t\t\t: parentDom.firstChild\r\n\t\t\t? slice.call(parentDom.childNodes)\r\n\t\t\t: null,\r\n\t\tcommitQueue,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? replaceNode\r\n\t\t\t: oldVNode\r\n\t\t\t? oldVNode._dom\r\n\t\t\t: parentDom.firstChild,\r\n\t\tisHydrating\r\n\t);\r\n\r\n\t// Flush all queued effects\r\n\tcommitRoot(commitQueue, vnode);\r\n}\r\n\r\n/**\r\n * Update an existing DOM element with data from a Preact virtual node\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * update\r\n */\r\nexport function hydrate(vnode, parentDom) {\r\n\trender(vnode, parentDom, hydrate);\r\n}\r\n", "/**\r\n * Find the closest error boundary to a thrown error and call it\r\n * @param {object} error The thrown value\r\n * @param {import('../internal').VNode} vnode The vnode that threw\r\n * the error that was caught (except for unmounting when this parameter\r\n * is the highest parent that was being unmounted)\r\n */\r\nexport function _catchError(error, vnode) {\r\n\t/** @type {import('../internal').Component} */\r\n\tlet component, ctor, handled;\r\n\r\n\tfor (; (vnode = vnode._parent); ) {\r\n\t\tif ((component = vnode._component) && !component._processingException) {\r\n\t\t\ttry {\r\n\t\t\t\tctor = component.constructor;\r\n\r\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\r\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (component.componentDidCatch != null) {\r\n\t\t\t\t\tcomponent.componentDidCatch(error);\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\r\n\t\t\t\tif (handled) {\r\n\t\t\t\t\treturn (component._pendingError = component);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\terror = e;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tthrow error;\r\n}\r\n", "import { options, Fragment } from 'preact';\r\n\r\n/** @typedef {import('preact').VNode} VNode */\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * @fileoverview\r\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\r\n * - jsx(type, props, key)\r\n * - jsxs(type, props, key)\r\n * - jsxDEV(type, props, key, __source, __self)\r\n *\r\n * The implementation of createVNode here is optimized for performance.\r\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\r\n */\r\n\r\n/**\r\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\r\n * @param {VNode['type']} type\r\n * @param {VNode['props']} props\r\n * @param {VNode['key']} [key]\r\n * @param {string} [__source]\r\n * @param {string} [__self]\r\n */\r\nfunction createVNode(type, props, key, __source, __self) {\r\n\t// We'll want to preserve `ref` in props to get rid of the need for\r\n\t// forwardRef components in the future, but that should happen via\r\n\t// a separate PR.\r\n\tlet normalizedProps = {},\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'ref') {\r\n\t\t\tref = props[i];\r\n\t\t} else {\r\n\t\t\tnormalizedProps[i] = props[i];\r\n\t\t}\r\n\t}\r\n\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops: normalizedProps,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: --vnodeId,\r\n\t\t__source,\r\n\t\t__self\r\n\t};\r\n\r\n\t// If a Component VNode, check for and apply defaultProps.\r\n\t// Note: `type` is often a String, and can be `undefined` in development.\r\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\r\n\t\tfor (i in ref)\r\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\r\n\t\t\t\tnormalizedProps[i] = ref[i];\r\n\t\t\t}\r\n\t}\r\n\r\n\tif (options.vnode) options.vnode(vnode);\r\n\treturn vnode;\r\n}\r\n\r\nexport {\r\n\tcreateVNode as jsx,\r\n\tcreateVNode as jsxs,\r\n\tcreateVNode as jsxDEV,\r\n\tFragment\r\n};\r\n", "function set(key: string, value: string) {\n  try {\n    window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value)\n  } catch (error) {}\n}\n\nfunction get(key: string): any {\n  try {\n    const value = window.localStorage[`emoji-mart.${key}`]\n\n    if (value) {\n      return JSON.parse(value)\n    }\n  } catch (error) {}\n}\n\nexport default { set, get }\n", "const CACHE = new Map()\nconst VERSIONS = [\n  { v: 15, emoji: '🫨' },\n  { v: 14, emoji: '🫠' },\n  { v: 13.1, emoji: '😶‍🌫️' },\n  { v: 13, emoji: '🥸' },\n  { v: 12.1, emoji: '🧑‍🦰' },\n  { v: 12, emoji: '🥱' },\n  { v: 11, emoji: '🥰' },\n  { v: 5, emoji: '🤩' },\n  { v: 4, emoji: '👱‍♀️' },\n  { v: 3, emoji: '🤣' },\n  { v: 2, emoji: '👋🏻' },\n  { v: 1, emoji: '🙃' },\n]\n\nfunction latestVersion() {\n  for (const { v, emoji } of VERSIONS) {\n    if (isSupported(emoji)) {\n      return v\n    }\n  }\n}\n\nfunction noCountryFlags() {\n  if (isSupported('🇨🇦')) {\n    return false\n  }\n\n  return true\n}\n\nfunction isSupported(emoji) {\n  if (CACHE.has(emoji)) {\n    return CACHE.get(emoji)\n  }\n\n  const supported = isEmojiSupported(emoji)\n  CACHE.set(emoji, supported)\n\n  return supported\n}\n\n// https://github.com/koala-interactive/is-emoji-supported\nconst isEmojiSupported = (() => {\n  let ctx = null\n  try {\n    if (!navigator.userAgent.includes('jsdom')) {\n      ctx = document\n        .createElement('canvas')\n        .getContext('2d', { willReadFrequently: true })\n    }\n  } catch {}\n\n  // Not in browser env\n  if (!ctx) {\n    return () => false\n  }\n\n  const CANVAS_HEIGHT = 25\n  const CANVAS_WIDTH = 20\n  const textSize = Math.floor(CANVAS_HEIGHT / 2)\n\n  // Initialize convas context\n  ctx.font = textSize + 'px Arial, Sans-Serif'\n  ctx.textBaseline = 'top'\n  ctx.canvas.width = CANVAS_WIDTH * 2\n  ctx.canvas.height = CANVAS_HEIGHT\n\n  return (unicode) => {\n    ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT)\n\n    // Draw in red on the left\n    ctx.fillStyle = '#FF0000'\n    ctx.fillText(unicode, 0, 22)\n\n    // Draw in blue on right\n    ctx.fillStyle = '#0000FF'\n    ctx.fillText(unicode, CANVAS_WIDTH, 22)\n\n    const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data\n    const count = a.length\n    let i = 0\n\n    // Search the first visible pixel\n    for (; i < count && !a[i + 3]; i += 4);\n\n    // No visible pixel\n    if (i >= count) {\n      return false\n    }\n\n    // Emoji has immutable color, so we check the color of the emoji in two different colors\n    // the result show be the same.\n    const x = CANVAS_WIDTH + ((i / 4) % CANVAS_WIDTH)\n    const y = Math.floor(i / 4 / CANVAS_WIDTH)\n    const b = ctx.getImageData(x, y, 1, 1).data\n\n    if (a[i] !== b[0] || a[i + 2] !== b[2]) {\n      return false\n    }\n\n    // Some emojis are a contraction of different ones, so if it's not\n    // supported, it will show multiple characters\n    if (ctx.measureText(unicode).width >= CANVAS_WIDTH) {\n      return false\n    }\n\n    // Supported\n    return true\n  }\n})()\n\nexport default { latestVersion, noCountryFlags }\n", "// @ts-nocheck\nimport { Store } from '../helpers'\n\nconst DEFAULTS = [\n  '+1',\n  'grinning',\n  'kissing_heart',\n  'heart_eyes',\n  'laughing',\n  'stuck_out_tongue_winking_eye',\n  'sweat_smile',\n  'joy',\n  'scream',\n  'disappointed',\n  'unamused',\n  'weary',\n  'sob',\n  'sunglasses',\n  'heart',\n]\n\nlet Index: any | null = null\n\nfunction add(emoji: { id: string }) {\n  Index || (Index = Store.get('frequently') || {})\n\n  const emojiId = emoji.id || emoji\n  if (!emojiId) return\n\n  Index[emojiId] || (Index[emojiId] = 0)\n  Index[emojiId] += 1\n\n  Store.set('last', emojiId)\n  Store.set('frequently', Index)\n}\n\nfunction get({ maxFrequentRows, perLine }) {\n  if (!maxFrequentRows) return []\n\n  Index || (Index = Store.get('frequently'))\n  let emojiIds = []\n\n  if (!Index) {\n    Index = {}\n\n    for (let i in DEFAULTS.slice(0, perLine)) {\n      const emojiId = DEFAULTS[i]\n\n      Index[emojiId] = perLine - i\n      emojiIds.push(emojiId)\n    }\n\n    return emojiIds\n  }\n\n  const max = maxFrequentRows * perLine\n  const last = Store.get('last')\n\n  for (let emojiId in Index) {\n    emojiIds.push(emojiId)\n  }\n\n  emojiIds.sort((a, b) => {\n    const aScore = Index[b]\n    const bScore = Index[a]\n\n    if (aScore == bScore) {\n      return a.localeCompare(b)\n    }\n\n    return aScore - bScore\n  })\n\n  if (emojiIds.length > max) {\n    const removedIds = emojiIds.slice(max)\n    emojiIds = emojiIds.slice(0, max)\n\n    for (let removedId of removedIds) {\n      if (removedId == last) continue\n      delete Index[removedId]\n    }\n\n    if (last && emojiIds.indexOf(last) == -1) {\n      delete Index[emojiIds[emojiIds.length - 1]]\n      emojiIds.splice(-1, 1, last)\n    }\n\n    Store.set('frequently', Index)\n  }\n\n  return emojiIds\n}\n\nexport default { add, get, DEFAULTS }\n", "{\n  \"search\": \"Search\",\n  \"search_no_results_1\": \"Oh no!\",\n  \"search_no_results_2\": \"That emoji couldn’t be found\",\n  \"pick\": \"Pick an emoji…\",\n  \"add_custom\": \"Add custom emoji\",\n  \"categories\": {\n    \"activity\": \"Activity\",\n    \"custom\": \"Custom\",\n    \"flags\": \"Flags\",\n    \"foods\": \"Food & Drink\",\n    \"frequent\": \"Frequently used\",\n    \"nature\": \"Animals & Nature\",\n    \"objects\": \"Objects\",\n    \"people\": \"Smileys & People\",\n    \"places\": \"Travel & Places\",\n    \"search\": \"Search Results\",\n    \"symbols\": \"Symbols\"\n  },\n  \"skins\": {\n    \"choose\": \"Choose default skin tone\",\n    \"1\": \"Default\",\n    \"2\": \"Light\",\n    \"3\": \"Medium-Light\",\n    \"4\": \"Medium\",\n    \"5\": \"Medium-Dark\",\n    \"6\": \"Dark\"\n  }\n}\n", "export default {\n  autoFocus: {\n    value: false,\n  },\n  dynamicWidth: {\n    value: false,\n  },\n  emojiButtonColors: {\n    value: null,\n  },\n  emojiButtonRadius: {\n    value: '100%',\n  },\n  emojiButtonSize: {\n    value: 36,\n  },\n  emojiSize: {\n    value: 24,\n  },\n  emojiVersion: {\n    value: 15,\n    choices: [1, 2, 3, 4, 5, 11, 12, 12.1, 13, 13.1, 14, 15],\n  },\n  exceptEmojis: {\n    value: [],\n  },\n  icons: {\n    value: 'auto',\n    choices: ['auto', 'outline', 'solid'],\n  },\n  locale: {\n    value: 'en',\n    choices: [\n      'en',\n      'ar',\n      'be',\n      'cs',\n      'de',\n      'es',\n      'fa',\n      'fi',\n      'fr',\n      'hi',\n      'it',\n      'ja',\n      'ko',\n      'nl',\n      'pl',\n      'pt',\n      'ru',\n      'sa',\n      'tr',\n      'uk',\n      'vi',\n      'zh',\n    ],\n  },\n  maxFrequentRows: {\n    value: 4,\n  },\n  navPosition: {\n    value: 'top',\n    choices: ['top', 'bottom', 'none'],\n  },\n  noCountryFlags: {\n    value: false,\n  },\n  noResultsEmoji: {\n    value: null,\n  },\n  perLine: {\n    value: 9,\n  },\n  previewEmoji: {\n    value: null,\n  },\n  previewPosition: {\n    value: 'bottom',\n    choices: ['top', 'bottom', 'none'],\n  },\n  searchPosition: {\n    value: 'sticky',\n    choices: ['sticky', 'static', 'none'],\n  },\n  set: {\n    value: 'native',\n    choices: ['native', 'apple', 'facebook', 'google', 'twitter'],\n  },\n  skin: {\n    value: 1,\n    choices: [1, 2, 3, 4, 5, 6],\n  },\n  skinTonePosition: {\n    value: 'preview',\n    choices: ['preview', 'search', 'none'],\n  },\n  theme: {\n    value: 'auto',\n    choices: ['auto', 'light', 'dark'],\n  },\n\n  // Data\n  categories: null,\n  categoryIcons: null,\n  custom: null,\n  data: null,\n  i18n: null,\n\n  // Callbacks\n  getImageURL: null,\n  getSpritesheetURL: null,\n  onAddCustomEmoji: null,\n  onClickOutside: null,\n  onEmojiSelect: null,\n\n  // Deprecated\n  stickySearch: {\n    deprecated: true,\n    value: true,\n  },\n}\n", "// @ts-nocheck\nimport i18n_en from '@emoji-mart/data/i18n/en.json'\nimport PickerProps from './components/Picker/PickerProps'\nimport {\n  FrequentlyUsed,\n  NativeSupport,\n  SafeFlags,\n  SearchIndex,\n} from './helpers'\n\nexport let I18n = null\nexport let Data = null\n\nconst fetchCache = {}\nasync function fetchJSON(src) {\n  if (fetchCache[src]) {\n    return fetchCache[src]\n  }\n\n  const response = await fetch(src)\n  const json = await response.json()\n\n  fetchCache[src] = json\n  return json\n}\n\nlet promise: Promise<void> | null = null\nlet initiated = false\nlet initCallback = null\nlet initialized = false\n\nexport function init(options, { caller } = {}) {\n  promise ||\n    (promise = new Promise((resolve) => {\n      initCallback = resolve\n    }))\n\n  if (options) {\n    _init(options)\n  } else if (caller && !initialized) {\n    console.warn(\n      `\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`,\n    )\n  }\n\n  return promise\n}\n\nasync function _init(props) {\n  initialized = true\n\n  let { emojiVersion, set, locale } = props\n  emojiVersion || (emojiVersion = PickerProps.emojiVersion.value)\n  set || (set = PickerProps.set.value)\n  locale || (locale = PickerProps.locale.value)\n\n  if (!Data) {\n    Data =\n      (typeof props.data === 'function' ? await props.data() : props.data) ||\n      (await fetchJSON(\n        `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`,\n      ))\n\n    Data.emoticons = {}\n    Data.natives = {}\n\n    Data.categories.unshift({\n      id: 'frequent',\n      emojis: [],\n    })\n\n    for (const alias in Data.aliases) {\n      const emojiId = Data.aliases[alias]\n      const emoji = Data.emojis[emojiId]\n      if (!emoji) continue\n\n      emoji.aliases || (emoji.aliases = [])\n      emoji.aliases.push(alias)\n    }\n\n    Data.originalCategories = Data.categories\n  } else {\n    Data.categories = Data.categories.filter((c) => {\n      const isCustom = !!c.name\n      if (!isCustom) return true\n\n      return false\n    })\n  }\n\n  I18n =\n    (typeof props.i18n === 'function' ? await props.i18n() : props.i18n) ||\n    (locale == 'en'\n      ? i18n_en\n      : await fetchJSON(\n          `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`,\n        ))\n\n  if (props.custom) {\n    for (let i in props.custom) {\n      i = parseInt(i)\n      const category = props.custom[i]\n      const prevCategory = props.custom[i - 1]\n\n      if (!category.emojis || !category.emojis.length) continue\n\n      category.id || (category.id = `custom_${i + 1}`)\n      category.name || (category.name = I18n.categories.custom)\n\n      if (prevCategory && !category.icon) {\n        category.target = prevCategory.target || prevCategory\n      }\n\n      Data.categories.push(category)\n\n      for (const emoji of category.emojis) {\n        Data.emojis[emoji.id] = emoji\n      }\n    }\n  }\n\n  if (props.categories) {\n    Data.categories = Data.originalCategories\n      .filter((c) => {\n        return props.categories.indexOf(c.id) != -1\n      })\n      .sort((c1, c2) => {\n        const i1 = props.categories.indexOf(c1.id)\n        const i2 = props.categories.indexOf(c2.id)\n\n        return i1 - i2\n      })\n  }\n\n  let latestVersionSupport = null\n  let noCountryFlags = null\n  if (set == 'native') {\n    latestVersionSupport = NativeSupport.latestVersion()\n    noCountryFlags = props.noCountryFlags || NativeSupport.noCountryFlags()\n  }\n\n  let categoryIndex = Data.categories.length\n  let resetSearchIndex = false\n  while (categoryIndex--) {\n    const category = Data.categories[categoryIndex]\n\n    if (category.id == 'frequent') {\n      let { maxFrequentRows, perLine } = props\n\n      maxFrequentRows =\n        maxFrequentRows >= 0\n          ? maxFrequentRows\n          : PickerProps.maxFrequentRows.value\n      perLine || (perLine = PickerProps.perLine.value)\n\n      category.emojis = FrequentlyUsed.get({ maxFrequentRows, perLine })\n    }\n\n    if (!category.emojis || !category.emojis.length) {\n      Data.categories.splice(categoryIndex, 1)\n      continue\n    }\n\n    const { categoryIcons } = props\n    if (categoryIcons) {\n      const icon = categoryIcons[category.id]\n      if (icon && !category.icon) {\n        category.icon = icon\n      }\n    }\n\n    let emojiIndex = category.emojis.length\n    while (emojiIndex--) {\n      const emojiId = category.emojis[emojiIndex]\n      const emoji = emojiId.id ? emojiId : Data.emojis[emojiId]\n\n      const ignore = () => {\n        category.emojis.splice(emojiIndex, 1)\n      }\n\n      if (\n        !emoji ||\n        (props.exceptEmojis && props.exceptEmojis.includes(emoji.id))\n      ) {\n        ignore()\n        continue\n      }\n\n      if (latestVersionSupport && emoji.version > latestVersionSupport) {\n        ignore()\n        continue\n      }\n\n      if (noCountryFlags && category.id == 'flags') {\n        if (!SafeFlags.includes(emoji.id)) {\n          ignore()\n          continue\n        }\n      }\n\n      if (!emoji.search) {\n        resetSearchIndex = true\n        emoji.search =\n          ',' +\n          [\n            [emoji.id, false],\n            [emoji.name, true],\n            [emoji.keywords, false],\n            [emoji.emoticons, false],\n          ]\n            .map(([strings, split]) => {\n              if (!strings) return\n              return (Array.isArray(strings) ? strings : [strings])\n                .map((string) => {\n                  return (split ? string.split(/[-|_|\\s]+/) : [string]).map(\n                    (s) => s.toLowerCase(),\n                  )\n                })\n                .flat()\n            })\n            .flat()\n            .filter((a) => a && a.trim())\n            .join(',')\n\n        if (emoji.emoticons) {\n          for (const emoticon of emoji.emoticons) {\n            if (Data.emoticons[emoticon]) continue\n            Data.emoticons[emoticon] = emoji.id\n          }\n        }\n\n        let skinIndex = 0\n        for (const skin of emoji.skins) {\n          if (!skin) continue\n          skinIndex++\n\n          const { native } = skin\n          if (native) {\n            Data.natives[native] = emoji.id\n            emoji.search += `,${native}`\n          }\n\n          const skinShortcodes =\n            skinIndex == 1 ? '' : `:skin-tone-${skinIndex}:`\n          skin.shortcodes = `:${emoji.id}:${skinShortcodes}`\n        }\n      }\n    }\n  }\n\n  if (resetSearchIndex) {\n    SearchIndex.reset()\n  }\n\n  initCallback()\n}\n\nexport function getProps(props, defaultProps, element) {\n  props || (props = {})\n\n  const _props = {}\n  for (let k in defaultProps) {\n    _props[k] = getProp(k, props, defaultProps, element)\n  }\n\n  return _props\n}\n\nexport function getProp(propName, props, defaultProps, element) {\n  const defaults = defaultProps[propName]\n  let value =\n    (element && element.getAttribute(propName)) ||\n    (props[propName] != null && props[propName] != undefined\n      ? props[propName]\n      : null)\n\n  if (!defaults) {\n    return value\n  }\n\n  if (\n    value != null &&\n    defaults.value &&\n    typeof defaults.value != typeof value\n  ) {\n    if (typeof defaults.value == 'boolean') {\n      value = value == 'false' ? false : true\n    } else {\n      value = defaults.value.constructor(value)\n    }\n  }\n\n  if (defaults.transform && value) {\n    value = defaults.transform(value)\n  }\n\n  if (\n    value == null ||\n    (defaults.choices && defaults.choices.indexOf(value) == -1)\n  ) {\n    value = defaults.value\n  }\n\n  return value\n}\n", "// @ts-nocheck\nimport { init, Data } from '../config'\n\nconst SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/\nlet Pool = null\n\nfunction get(emojiId) {\n  if (emojiId.id) {\n    return emojiId\n  }\n\n  return (\n    Data.emojis[emojiId] ||\n    Data.emojis[Data.aliases[emojiId]] ||\n    Data.emojis[Data.natives[emojiId]]\n  )\n}\n\nfunction reset() {\n  Pool = null\n}\n\nasync function search(value, { maxResults, caller } = {}) {\n  if (!value || !value.trim().length) return null\n  maxResults || (maxResults = 90)\n\n  await init(null, { caller: caller || 'SearchIndex.search' })\n\n  const values = value\n    .toLowerCase()\n    .replace(/(\\w)-/, '$1 ')\n    .split(/[\\s|,]+/)\n    .filter((word, i, words) => {\n      return word.trim() && words.indexOf(word) == i\n    })\n\n  if (!values.length) return\n\n  let pool = Pool || (Pool = Object.values(Data.emojis))\n  let results, scores\n\n  for (const value of values) {\n    if (!pool.length) break\n\n    results = []\n    scores = {}\n\n    for (const emoji of pool) {\n      if (!emoji.search) continue\n      const score = emoji.search.indexOf(`,${value}`)\n      if (score == -1) continue\n\n      results.push(emoji)\n      scores[emoji.id] || (scores[emoji.id] = 0)\n      scores[emoji.id] += emoji.id == value ? 0 : score + 1\n    }\n\n    pool = results\n  }\n\n  if (results.length < 2) {\n    return results\n  }\n\n  results.sort((a, b) => {\n    const aScore = scores[a.id]\n    const bScore = scores[b.id]\n\n    if (aScore == bScore) {\n      return a.id.localeCompare(b.id)\n    }\n\n    return aScore - bScore\n  })\n\n  if (results.length > maxResults) {\n    results = results.slice(0, maxResults)\n  }\n\n  return results\n}\n\nexport default { search, get, reset, SHORTCODES_REGEX }\n", "export { default as Store } from './store'\n\nexport { default as NativeSupport } from './native-support'\nexport { default as FrequentlyUsed } from './frequently-used'\nexport { default as SearchIndex } from './search-index'\n\nexport const SafeFlags = [\n  'checkered_flag',\n  'crossed_flags',\n  'pirate_flag',\n  'rainbow-flag',\n  'transgender_flag',\n  'triangular_flag_on_post',\n  'waving_black_flag',\n  'waving_white_flag',\n]\n", "import { SearchIndex } from './helpers'\n\nexport function deepEqual(a: any, b: any): boolean {\n  return (\n    Array.isArray(a) &&\n    Array.isArray(b) &&\n    a.length === b.length &&\n    a.every((val, index) => val == b[index])\n  )\n}\n\nexport async function sleep(frames = 1) {\n  for (let _ in [...Array(frames).keys()]) {\n    await new Promise(requestAnimationFrame)\n  }\n}\n\nexport function getEmojiData(emoji, { skinIndex = 0 } = {}) {\n  const skin =\n    emoji.skins[skinIndex] ||\n    (() => {\n      skinIndex = 0\n      return emoji.skins[skinIndex]\n    })()\n\n  const emojiData: any = {\n    id: emoji.id,\n    name: emoji.name,\n    native: skin.native,\n    unified: skin.unified,\n    keywords: emoji.keywords,\n    shortcodes: skin.shortcodes || emoji.shortcodes,\n  }\n\n  if (emoji.skins.length > 1) {\n    emojiData.skin = skinIndex + 1\n  }\n\n  if (skin.src) {\n    emojiData.src = skin.src\n  }\n\n  if (emoji.aliases && emoji.aliases.length) {\n    emojiData.aliases = emoji.aliases\n  }\n\n  if (emoji.emoticons && emoji.emoticons.length) {\n    emojiData.emoticons = emoji.emoticons\n  }\n\n  return emojiData\n}\n\nexport async function getEmojiDataFromNative(nativeString) {\n  const results = await SearchIndex.search(nativeString, {\n    maxResults: 1,\n    caller: 'getEmojiDataFromNative',\n  })\n  if (!results || !results.length) return null\n\n  const emoji = results[0]\n  let skinIndex = 0\n\n  for (let skin of emoji.skins) {\n    if (skin.native == nativeString) {\n      break\n    }\n\n    skinIndex++\n  }\n\n  return getEmojiData(emoji, { skinIndex })\n}\n", "const categories = {\n  activity: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\" />\n      </svg>\n    ),\n  },\n\n  custom: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\">\n      <path d=\"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\" />\n    </svg>\n  ),\n\n  flags: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\" />\n      </svg>\n    ),\n  },\n\n  foods: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\" />\n      </svg>\n    ),\n  },\n\n  frequent: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\" />\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\" />\n      </svg>\n    ),\n  },\n\n  nature: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\" />\n        <path d=\"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\">\n        <path d=\"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\" />\n      </svg>\n    ),\n  },\n\n  objects: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\" />\n        <path d=\"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\">\n        <path d=\"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\" />\n      </svg>\n    ),\n  },\n\n  people: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n        <path d=\"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\" />\n      </svg>\n    ),\n  },\n\n  places: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\" />\n        <path d=\"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\" />\n      </svg>\n    ),\n  },\n\n  symbols: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\" />\n      </svg>\n    ),\n  },\n}\n\nconst search = {\n  loupe: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\" />\n    </svg>\n  ),\n\n  delete: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\" />\n    </svg>\n  ),\n}\n\nexport default { categories, search }\n", "import { Data } from '../../config'\nimport { SearchIndex } from '../../helpers'\n\nexport default function Emoji(props) {\n  let { id, skin, emoji } = props\n\n  if (props.shortcodes) {\n    const matches = props.shortcodes.match(SearchIndex.SHORTCODES_REGEX)\n\n    if (matches) {\n      id = matches[1]\n\n      if (matches[2]) {\n        skin = matches[2]\n      }\n    }\n  }\n\n  emoji || (emoji = SearchIndex.get(id || props.native))\n  if (!emoji) return props.fallback\n\n  const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n\n  const imageSrc =\n    emojiSkin.src ||\n    (props.set != 'native' && !props.spritesheet\n      ? typeof props.getImageURL === 'function'\n        ? props.getImageURL(props.set, emojiSkin.unified)\n        : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png`\n      : undefined)\n\n  const spritesheetSrc =\n    typeof props.getSpritesheetURL === 'function'\n      ? props.getSpritesheetURL(props.set)\n      : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`\n\n  return (\n    <span class=\"emoji-mart-emoji\" data-emoji-set={props.set}>\n      {imageSrc ? (\n        <img\n          style={{\n            maxWidth: props.size || '1em',\n            maxHeight: props.size || '1em',\n            display: 'inline-block',\n          }}\n          alt={emojiSkin.native || emojiSkin.shortcodes}\n          src={imageSrc}\n        />\n      ) : props.set == 'native' ? (\n        <span\n          style={{\n            fontSize: props.size,\n            fontFamily:\n              '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"',\n          }}\n        >\n          {emojiSkin.native}\n        </span>\n      ) : (\n        <span\n          style={{\n            display: 'block',\n            width: props.size,\n            height: props.size,\n            backgroundImage: `url(${spritesheetSrc})`,\n            backgroundSize: `${100 * Data.sheet.cols}% ${\n              100 * Data.sheet.rows\n            }%`,\n            backgroundPosition: `${\n              (100 / (Data.sheet.cols - 1)) * emojiSkin.x\n            }% ${(100 / (Data.sheet.rows - 1)) * emojiSkin.y}%`,\n          }}\n        ></span>\n      )}\n    </span>\n  )\n}\n", "import _setPrototypeOf from \"./_set_prototype_of.mjs\";\n\nfunction isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Date.prototype.toString.call(Reflect.construct(Date, [], function () { }));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction construct(Parent, args, Class) {\n  if (isNativeReflectConstruct()) {\n    construct = Reflect.construct;\n  } else {\n    construct = function construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return construct.apply(null, arguments);\n}\n\nexport default function _construct(Parent, args, Class) {\n  return construct.apply(null, arguments);\n}\n", "import construct from './_construct.mjs';\nimport isNativeFunction from './_is_native_function.mjs';\nimport getPrototypeOf from './_get_prototype_of.mjs';\nimport setPrototypeOf from './_set_prototype_of.mjs';\n\nfunction wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n\n  wrapNativeSuper = function wrapNativeSuper(Class) {\n    if (Class === null || !isNativeFunction(Class)) return Class;\n\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n\n      _cache.set(Class, Wrapper);\n    }\n\n    function Wrapper() {\n      return construct(Class, arguments, getPrototypeOf(this).constructor);\n    }\n\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return setPrototypeOf(Wrapper, Class);\n  };\n\n  return wrapNativeSuper(Class);\n}\n\nexport default function _wrapNativeSuper(Class) {\n  return wrapNativeSuper(Class);\n}\n", "export default function _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\n", "// @ts-nocheck\nimport { getProp } from '../../config'\n\nconst WindowHTMLElement =\n  typeof window !== 'undefined' && window.HTMLElement\n    ? window.HTMLElement\n    : Object\n\nexport default class HTMLElement extends WindowHTMLElement {\n  static get observedAttributes() {\n    return Object.keys(this.Props)\n  }\n\n  constructor(props = {}) {\n    super()\n    this.props = props\n\n    if (props.parent || props.ref) {\n      let ref = null\n      const parent = props.parent || (ref = props.ref && props.ref.current)\n\n      if (ref) ref.innerHTML = ''\n      if (parent) parent.appendChild(this)\n    }\n  }\n\n  update(props = {}) {\n    for (let k in props) {\n      this.attributeChangedCallback(k, null, props[k])\n    }\n  }\n\n  attributeChangedCallback(attr, _, newValue) {\n    if (!this.component) return\n\n    const value = getProp(\n      attr,\n      { [attr]: newValue },\n      this.constructor.Props,\n      this,\n    )\n\n    if (this.component.componentWillReceiveProps) {\n      this.component.componentWillReceiveProps({ [attr]: value })\n    } else {\n      this.component.props[attr] = value\n      this.component.forceUpdate()\n    }\n  }\n\n  disconnectedCallback() {\n    this.disconnected = true\n\n    if (this.component && this.component.unregister) {\n      this.component.unregister()\n    }\n  }\n}\n", null, "// @ts-nocheck\nimport { HTMLElement } from '.'\n\nexport default class ShadowElement extends HTMLElement {\n  constructor(props, { styles } = {}) {\n    super(props)\n\n    this.setShadow()\n    this.injectStyles(styles)\n  }\n\n  setShadow() {\n    this.attachShadow({ mode: 'open' })\n  }\n\n  injectStyles(styles) {\n    if (!styles) return\n\n    const style = document.createElement('style')\n    style.textContent = styles\n\n    this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild)\n  }\n}\n", "import PickerProps from '../Picker/PickerProps'\n\nexport default {\n  fallback: '',\n  id: '',\n  native: '',\n  shortcodes: '',\n  size: {\n    value: '',\n    transform: (value) => {\n      // If the value is a number, then we assume it’s a pixel value.\n      if (!/\\D/.test(value)) {\n        return `${value}px`\n      }\n\n      return value\n    },\n  },\n\n  // Shared\n  set: PickerProps.set,\n  skin: PickerProps.skin,\n}\n", "import { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { HTMLElement } from '../HTMLElement'\nimport { Emoji } from '.'\nimport EmojiProps from './EmojiProps'\n\nexport default class EmojiElement extends HTMLElement {\n  static Props = EmojiProps\n\n  constructor(props) {\n    super(props)\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, EmojiProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init()\n    if (this.disconnected) return\n\n    render(<Emoji {...props} />, this)\n  }\n}\n\nif (typeof customElements !== 'undefined' && !customElements.get('em-emoji')) {\n  customElements.define('em-emoji', EmojiElement)\n}\n", "import { options } from 'preact';\r\n\r\n/** @type {number} */\r\nlet currentIndex;\r\n\r\n/** @type {import('./internal').Component} */\r\nlet currentComponent;\r\n\r\n/** @type {number} */\r\nlet currentHook = 0;\r\n\r\n/** @type {Array<import('./internal').Component>} */\r\nlet afterPaintEffects = [];\r\n\r\nlet oldBeforeDiff = options._diff;\r\nlet oldBeforeRender = options._render;\r\nlet oldAfterDiff = options.diffed;\r\nlet oldCommit = options._commit;\r\nlet oldBeforeUnmount = options.unmount;\r\n\r\nconst RAF_TIMEOUT = 100;\r\nlet prevRaf;\r\n\r\noptions._diff = vnode => {\r\n\tcurrentComponent = null;\r\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\r\n};\r\n\r\noptions._render = vnode => {\r\n\tif (oldBeforeRender) oldBeforeRender(vnode);\r\n\r\n\tcurrentComponent = vnode._component;\r\n\tcurrentIndex = 0;\r\n\r\n\tconst hooks = currentComponent.__hooks;\r\n\tif (hooks) {\r\n\t\thooks._pendingEffects.forEach(invokeCleanup);\r\n\t\thooks._pendingEffects.forEach(invokeEffect);\r\n\t\thooks._pendingEffects = [];\r\n\t}\r\n};\r\n\r\noptions.diffed = vnode => {\r\n\tif (oldAfterDiff) oldAfterDiff(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks && c.__hooks._pendingEffects.length) {\r\n\t\tafterPaint(afterPaintEffects.push(c));\r\n\t}\r\n\tcurrentComponent = null;\r\n};\r\n\r\noptions._commit = (vnode, commitQueue) => {\r\n\tcommitQueue.some(component => {\r\n\t\ttry {\r\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\r\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\r\n\t\t\t\tcb._value ? invokeEffect(cb) : true\r\n\t\t\t);\r\n\t\t} catch (e) {\r\n\t\t\tcommitQueue.some(c => {\r\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\r\n\t\t\t});\r\n\t\t\tcommitQueue = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t});\r\n\r\n\tif (oldCommit) oldCommit(vnode, commitQueue);\r\n};\r\n\r\noptions.unmount = vnode => {\r\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks) {\r\n\t\tlet hasErrored;\r\n\t\tc.__hooks._list.forEach(s => {\r\n\t\t\ttry {\r\n\t\t\t\tinvokeCleanup(s);\r\n\t\t\t} catch (e) {\r\n\t\t\t\thasErrored = e;\r\n\t\t\t}\r\n\t\t});\r\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\r\n\t}\r\n};\r\n\r\n/**\r\n * Get a hook's state from the currentComponent\r\n * @param {number} index The index of the hook to get\r\n * @param {number} type The index of the hook to get\r\n * @returns {any}\r\n */\r\nfunction getHookState(index, type) {\r\n\tif (options._hook) {\r\n\t\toptions._hook(currentComponent, index, currentHook || type);\r\n\t}\r\n\tcurrentHook = 0;\r\n\r\n\t// Largely inspired by:\r\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\r\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\r\n\t// Other implementations to look at:\r\n\t// * https://codesandbox.io/s/mnox05qp8\r\n\tconst hooks =\r\n\t\tcurrentComponent.__hooks ||\r\n\t\t(currentComponent.__hooks = {\r\n\t\t\t_list: [],\r\n\t\t\t_pendingEffects: []\r\n\t\t});\r\n\r\n\tif (index >= hooks._list.length) {\r\n\t\thooks._list.push({});\r\n\t}\r\n\treturn hooks._list[index];\r\n}\r\n\r\n/**\r\n * @param {import('./index').StateUpdater<any>} [initialState]\r\n */\r\nexport function useState(initialState) {\r\n\tcurrentHook = 1;\r\n\treturn useReducer(invokeOrReturn, initialState);\r\n}\r\n\r\n/**\r\n * @param {import('./index').Reducer<any, any>} reducer\r\n * @param {import('./index').StateUpdater<any>} initialState\r\n * @param {(initialState: any) => void} [init]\r\n * @returns {[ any, (state: any) => void ]}\r\n */\r\nexport function useReducer(reducer, initialState, init) {\r\n\t/** @type {import('./internal').ReducerHookState} */\r\n\tconst hookState = getHookState(currentIndex++, 2);\r\n\thookState._reducer = reducer;\r\n\tif (!hookState._component) {\r\n\t\thookState._value = [\r\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\r\n\r\n\t\t\taction => {\r\n\t\t\t\tconst nextValue = hookState._reducer(hookState._value[0], action);\r\n\t\t\t\tif (hookState._value[0] !== nextValue) {\r\n\t\t\t\t\thookState._value = [nextValue, hookState._value[1]];\r\n\t\t\t\t\thookState._component.setState({});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\thookState._component = currentComponent;\r\n\t}\r\n\r\n\treturn hookState._value;\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 3);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useLayoutEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 4);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent._renderCallbacks.push(state);\r\n\t}\r\n}\r\n\r\nexport function useRef(initialValue) {\r\n\tcurrentHook = 5;\r\n\treturn useMemo(() => ({ current: initialValue }), []);\r\n}\r\n\r\n/**\r\n * @param {object} ref\r\n * @param {() => object} createHandle\r\n * @param {any[]} args\r\n */\r\nexport function useImperativeHandle(ref, createHandle, args) {\r\n\tcurrentHook = 6;\r\n\tuseLayoutEffect(\r\n\t\t() => {\r\n\t\t\tif (typeof ref == 'function') ref(createHandle());\r\n\t\t\telse if (ref) ref.current = createHandle();\r\n\t\t},\r\n\t\targs == null ? args : args.concat(ref)\r\n\t);\r\n}\r\n\r\n/**\r\n * @param {() => any} factory\r\n * @param {any[]} args\r\n */\r\nexport function useMemo(factory, args) {\r\n\t/** @type {import('./internal').MemoHookState} */\r\n\tconst state = getHookState(currentIndex++, 7);\r\n\tif (argsChanged(state._args, args)) {\r\n\t\tstate._value = factory();\r\n\t\tstate._args = args;\r\n\t\tstate._factory = factory;\r\n\t}\r\n\r\n\treturn state._value;\r\n}\r\n\r\n/**\r\n * @param {() => void} callback\r\n * @param {any[]} args\r\n */\r\nexport function useCallback(callback, args) {\r\n\tcurrentHook = 8;\r\n\treturn useMemo(() => callback, args);\r\n}\r\n\r\n/**\r\n * @param {import('./internal').PreactContext} context\r\n */\r\nexport function useContext(context) {\r\n\tconst provider = currentComponent.context[context._id];\r\n\t// We could skip this call here, but than we'd not call\r\n\t// `options._hook`. We need to do that in order to make\r\n\t// the devtools aware of this hook.\r\n\t/** @type {import('./internal').ContextHookState} */\r\n\tconst state = getHookState(currentIndex++, 9);\r\n\t// The devtools needs access to the context object to\r\n\t// be able to pull of the default value when no provider\r\n\t// is present in the tree.\r\n\tstate._context = context;\r\n\tif (!provider) return context._defaultValue;\r\n\t// This is probably not safe to convert to \"!\"\r\n\tif (state._value == null) {\r\n\t\tstate._value = true;\r\n\t\tprovider.sub(currentComponent);\r\n\t}\r\n\treturn provider.props.value;\r\n}\r\n\r\n/**\r\n * Display a custom label for a custom hook for the devtools panel\r\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\r\n */\r\nexport function useDebugValue(value, formatter) {\r\n\tif (options.useDebugValue) {\r\n\t\toptions.useDebugValue(formatter ? formatter(value) : value);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {(error: any) => void} cb\r\n */\r\nexport function useErrorBoundary(cb) {\r\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\r\n\tconst state = getHookState(currentIndex++, 10);\r\n\tconst errState = useState();\r\n\tstate._value = cb;\r\n\tif (!currentComponent.componentDidCatch) {\r\n\t\tcurrentComponent.componentDidCatch = err => {\r\n\t\t\tif (state._value) state._value(err);\r\n\t\t\terrState[1](err);\r\n\t\t};\r\n\t}\r\n\treturn [\r\n\t\terrState[0],\r\n\t\t() => {\r\n\t\t\terrState[1](undefined);\r\n\t\t}\r\n\t];\r\n}\r\n\r\n/**\r\n * After paint effects consumer.\r\n */\r\nfunction flushAfterPaintEffects() {\r\n\tlet component;\r\n\t// sort the queue by depth (outermost to innermost)\r\n\tafterPaintEffects.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\twhile (component = afterPaintEffects.pop()) {\r\n\t\tif (!component._parentDom) continue;\r\n\t\ttry {\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t} catch (e) {\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\r\n\r\n/**\r\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\r\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\r\n * the next browser frame.\r\n *\r\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\r\n * even if RAF doesn't fire (for example if the browser tab is not visible)\r\n *\r\n * @param {() => void} callback\r\n */\r\nfunction afterNextFrame(callback) {\r\n\tconst done = () => {\r\n\t\tclearTimeout(timeout);\r\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\r\n\t\tsetTimeout(callback);\r\n\t};\r\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\r\n\r\n\tlet raf;\r\n\tif (HAS_RAF) {\r\n\t\traf = requestAnimationFrame(done);\r\n\t}\r\n}\r\n\r\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\r\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\r\n// Perhaps this is not such a big deal.\r\n/**\r\n * Schedule afterPaintEffects flush after the browser paints\r\n * @param {number} newQueueLength\r\n */\r\nfunction afterPaint(newQueueLength) {\r\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\r\n\t\tprevRaf = options.requestAnimationFrame;\r\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeCleanup(hook) {\r\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\tlet cleanup = hook._cleanup;\r\n\tif (typeof cleanup == 'function') {\r\n\t\thook._cleanup = undefined;\r\n\t\tcleanup();\r\n\t}\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * Invoke a Hook's effect\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeEffect(hook) {\r\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\thook._cleanup = hook._value();\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * @param {any[]} oldArgs\r\n * @param {any[]} newArgs\r\n */\r\nfunction argsChanged(oldArgs, newArgs) {\r\n\treturn (\r\n\t\t!oldArgs ||\r\n\t\toldArgs.length !== newArgs.length ||\r\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\r\n\t);\r\n}\r\n\r\nfunction invokeOrReturn(arg, f) {\r\n\treturn typeof f == 'function' ? f(arg) : f;\r\n}\r\n", "/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Check if two objects have a different shape\r\n * @param {object} a\r\n * @param {object} b\r\n * @returns {boolean}\r\n */\r\nexport function shallowDiffers(a, b) {\r\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\r\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\r\n\treturn false;\r\n}\r\n\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n", "import { Component } from 'preact';\r\nimport { shallowDiffers } from './util';\r\n\r\n/**\r\n * Component class with a predefined `shouldComponentUpdate` implementation\r\n */\r\nexport function PureComponent(p) {\r\n\tthis.props = p;\r\n}\r\nPureComponent.prototype = new Component();\r\n// Some third-party libraries check if this property is present\r\nPureComponent.prototype.isPureReactComponent = true;\r\nPureComponent.prototype.shouldComponentUpdate = function(props, state) {\r\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\r\n};\r\n", "import { options } from 'preact';\r\nimport { assign } from './util';\r\n\r\nlet oldDiffHook = options._diff;\r\noptions._diff = vnode => {\r\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\r\n\t\tvnode.props.ref = vnode.ref;\r\n\t\tvnode.ref = null;\r\n\t}\r\n\tif (oldDiffHook) oldDiffHook(vnode);\r\n};\r\n\r\nexport const REACT_FORWARD_SYMBOL =\r\n\t(typeof Symbol != 'undefined' &&\r\n\t\tSymbol.for &&\r\n\t\tSymbol.for('react.forward_ref')) ||\r\n\t0xf47;\r\n\r\n/**\r\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\r\n * wrap components. Using `forwardRef` there is an easy way to get a reference\r\n * of the wrapped component instead of one of the wrapper itself.\r\n * @param {import('./index').ForwardFn} fn\r\n * @returns {import('./internal').FunctionComponent}\r\n */\r\nexport function forwardRef(fn) {\r\n\t// We always have ref in props.ref, except for\r\n\t// mobx-react. It will call this function directly\r\n\t// and always pass ref as the second argument.\r\n\tfunction Forwarded(props, ref) {\r\n\t\tlet clone = assign({}, props);\r\n\t\tdelete clone.ref;\r\n\t\tref = props.ref || ref;\r\n\t\treturn fn(\r\n\t\t\tclone,\r\n\t\t\t!ref || (typeof ref === 'object' && !('current' in ref)) ? null : ref\r\n\t\t);\r\n\t}\r\n\r\n\t// mobx-react checks for this being present\r\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\r\n\t// mobx-react heavily relies on implementation details.\r\n\t// It expects an object here with a `render` property,\r\n\t// and prototype.render will fail. Without this\r\n\t// mobx-react throws.\r\n\tForwarded.render = Forwarded;\r\n\r\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\r\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\r\n\treturn Forwarded;\r\n}\r\n", "import { Component, createElement, options, Fragment } from 'preact';\r\nimport { assign } from './util';\r\n\r\nconst oldCatchError = options._catchError;\r\noptions._catchError = function(error, newVNode, oldVNode) {\r\n\tif (error.then) {\r\n\t\t/** @type {import('./internal').Component} */\r\n\t\tlet component;\r\n\t\tlet vnode = newVNode;\r\n\r\n\t\tfor (; (vnode = vnode._parent); ) {\r\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\r\n\t\t\t\tif (newVNode._dom == null) {\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t}\r\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\r\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\toldCatchError(error, newVNode, oldVNode);\r\n};\r\n\r\nconst oldUnmount = options.unmount;\r\noptions.unmount = function(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tconst component = vnode._component;\r\n\tif (component && component._onResolve) {\r\n\t\tcomponent._onResolve();\r\n\t}\r\n\r\n\t// if the component is still hydrating\r\n\t// most likely it is because the component is suspended\r\n\t// we set the vnode.type as `null` so that it is not a typeof function\r\n\t// so the unmount will remove the vnode._dom\r\n\tif (component && vnode._hydrating === true) {\r\n\t\tvnode.type = null;\r\n\t}\r\n\r\n\tif (oldUnmount) oldUnmount(vnode);\r\n};\r\n\r\nfunction detachedClone(vnode, detachedParent, parentDom) {\r\n\tif (vnode) {\r\n\t\tif (vnode._component && vnode._component.__hooks) {\r\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\r\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\r\n\t\t\t});\r\n\r\n\t\t\tvnode._component.__hooks = null;\r\n\t\t}\r\n\r\n\t\tvnode = assign({}, vnode);\r\n\t\tif (vnode._component != null) {\r\n\t\t\tif (vnode._component._parentDom === parentDom) {\r\n\t\t\t\tvnode._component._parentDom = detachedParent;\r\n\t\t\t}\r\n\t\t\tvnode._component = null;\r\n\t\t}\r\n\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\r\n\t\t\t);\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\nfunction removeOriginal(vnode, detachedParent, originalParent) {\r\n\tif (vnode) {\r\n\t\tvnode._original = null;\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\r\n\t\t\t);\r\n\r\n\t\tif (vnode._component) {\r\n\t\t\tif (vnode._component._parentDom === detachedParent) {\r\n\t\t\t\tif (vnode._dom) {\r\n\t\t\t\t\toriginalParent.insertBefore(vnode._dom, vnode._nextDom);\r\n\t\t\t\t}\r\n\t\t\t\tvnode._component._force = true;\r\n\t\t\t\tvnode._component._parentDom = originalParent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\n// having custom inheritance instead of a class here saves a lot of bytes\r\nexport function Suspense() {\r\n\t// we do not call super here to golf some bytes...\r\n\tthis._pendingSuspensionCount = 0;\r\n\tthis._suspenders = null;\r\n\tthis._detachOnNextRender = null;\r\n}\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspense.prototype = new Component();\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {Promise} promise The thrown promise\r\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\r\n */\r\nSuspense.prototype._childDidSuspend = function(promise, suspendingVNode) {\r\n\tconst suspendingComponent = suspendingVNode._component;\r\n\r\n\t/** @type {import('./internal').SuspenseComponent} */\r\n\tconst c = this;\r\n\r\n\tif (c._suspenders == null) {\r\n\t\tc._suspenders = [];\r\n\t}\r\n\tc._suspenders.push(suspendingComponent);\r\n\r\n\tconst resolve = suspended(c._vnode);\r\n\r\n\tlet resolved = false;\r\n\tconst onResolved = () => {\r\n\t\tif (resolved) return;\r\n\r\n\t\tresolved = true;\r\n\t\tsuspendingComponent._onResolve = null;\r\n\r\n\t\tif (resolve) {\r\n\t\t\tresolve(onSuspensionComplete);\r\n\t\t} else {\r\n\t\t\tonSuspensionComplete();\r\n\t\t}\r\n\t};\r\n\r\n\tsuspendingComponent._onResolve = onResolved;\r\n\r\n\tconst onSuspensionComplete = () => {\r\n\t\tif (!--c._pendingSuspensionCount) {\r\n\t\t\t// If the suspension was during hydration we don't need to restore the\r\n\t\t\t// suspended children into the _children array\r\n\t\t\tif (c.state._suspended) {\r\n\t\t\t\tconst suspendedVNode = c.state._suspended;\r\n\t\t\t\tc._vnode._children[0] = removeOriginal(\r\n\t\t\t\t\tsuspendedVNode,\r\n\t\t\t\t\tsuspendedVNode._component._parentDom,\r\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\r\n\r\n\t\t\tlet suspended;\r\n\t\t\twhile ((suspended = c._suspenders.pop())) {\r\n\t\t\t\tsuspended.forceUpdate();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * We do not set `suspended: true` during hydration because we want the actual markup\r\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\r\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\r\n\t */\r\n\tconst wasHydrating = suspendingVNode._hydrating === true;\r\n\tif (!c._pendingSuspensionCount++ && !wasHydrating) {\r\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\r\n\t}\r\n\tpromise.then(onResolved, onResolved);\r\n};\r\n\r\nSuspense.prototype.componentWillUnmount = function() {\r\n\tthis._suspenders = [];\r\n};\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\r\n * @param {import('./internal').SuspenseState} state\r\n */\r\nSuspense.prototype.render = function(props, state) {\r\n\tif (this._detachOnNextRender) {\r\n\t\t// When the Suspense's _vnode was created by a call to createVNode\r\n\t\t// (i.e. due to a setState further up in the tree)\r\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\r\n\t\tif (this._vnode._children) {\r\n\t\t\tconst detachedParent = document.createElement('div');\r\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\r\n\t\t\tthis._vnode._children[0] = detachedClone(\r\n\t\t\t\tthis._detachOnNextRender,\r\n\t\t\t\tdetachedParent,\r\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tthis._detachOnNextRender = null;\r\n\t}\r\n\r\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\r\n\t/** @type {import('./internal').VNode} */\r\n\tconst fallback =\r\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\r\n\tif (fallback) fallback._hydrating = null;\r\n\r\n\treturn [\r\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\r\n\t\tfallback\r\n\t];\r\n};\r\n\r\n/**\r\n * Checks and calls the parent component's _suspended method, passing in the\r\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\r\n * that one of its children/descendants suspended.\r\n *\r\n * The parent MAY return a callback. The callback will get called when the\r\n * suspension resolves, notifying the parent of the fact.\r\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\r\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\r\n * This is a way for the parent to delay unsuspending.\r\n *\r\n * If the parent does not return a callback then the resolved vnode\r\n * gets unsuspended immediately when it resolves.\r\n *\r\n * @param {import('./internal').VNode} vnode\r\n * @returns {((unsuspend: () => void) => void)?}\r\n */\r\nexport function suspended(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tlet component = vnode._parent._component;\r\n\treturn component && component._suspended && component._suspended(vnode);\r\n}\r\n\r\nexport function lazy(loader) {\r\n\tlet prom;\r\n\tlet component;\r\n\tlet error;\r\n\r\n\tfunction Lazy(props) {\r\n\t\tif (!prom) {\r\n\t\t\tprom = loader();\r\n\t\t\tprom.then(\r\n\t\t\t\texports => {\r\n\t\t\t\t\tcomponent = exports.default || exports;\r\n\t\t\t\t},\r\n\t\t\t\te => {\r\n\t\t\t\t\terror = e;\r\n\t\t\t\t}\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error;\r\n\t\t}\r\n\r\n\t\tif (!component) {\r\n\t\t\tthrow prom;\r\n\t\t}\r\n\r\n\t\treturn createElement(component, props);\r\n\t}\r\n\r\n\tLazy.displayName = 'Lazy';\r\n\tLazy._forwarded = true;\r\n\treturn Lazy;\r\n}\r\n", "import { Component, toChildArray } from 'preact';\r\nimport { suspended } from './suspense.js';\r\n\r\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\r\nconst SUSPENDED_COUNT = 0;\r\nconst RESOLVED_COUNT = 1;\r\nconst NEXT_NODE = 2;\r\n\r\n// Having custom inheritance instead of a class here saves a lot of bytes.\r\nexport function SuspenseList() {\r\n\tthis._next = null;\r\n\tthis._map = null;\r\n}\r\n\r\n// Mark one of child's earlier suspensions as resolved.\r\n// Some pending callbacks may become callable due to this\r\n// (e.g. the last suspended descendant gets resolved when\r\n// revealOrder === 'together'). Process those callbacks as well.\r\nconst resolve = (list, child, node) => {\r\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\r\n\t\t// The number a child (or any of its descendants) has been suspended\r\n\t\t// matches the number of times it's been resolved. Therefore we\r\n\t\t// mark the child as completely resolved by deleting it from ._map.\r\n\t\t// This is used to figure out when *all* children have been completely\r\n\t\t// resolved when revealOrder is 'together'.\r\n\t\tlist._map.delete(child);\r\n\t}\r\n\r\n\t// If revealOrder is falsy then we can do an early exit, as the\r\n\t// callbacks won't get queued in the node anyway.\r\n\t// If revealOrder is 'together' then also do an early exit\r\n\t// if all suspended descendants have not yet been resolved.\r\n\tif (\r\n\t\t!list.props.revealOrder ||\r\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\r\n\t) {\r\n\t\treturn;\r\n\t}\r\n\r\n\t// Walk the currently suspended children in order, calling their\r\n\t// stored callbacks on the way. Stop if we encounter a child that\r\n\t// has not been completely resolved yet.\r\n\tnode = list._next;\r\n\twhile (node) {\r\n\t\twhile (node.length > 3) {\r\n\t\t\tnode.pop()();\r\n\t\t}\r\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\r\n\t\t\tbreak;\r\n\t\t}\r\n\t\tlist._next = node = node[NEXT_NODE];\r\n\t}\r\n};\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspenseList.prototype = new Component();\r\n\r\nSuspenseList.prototype._suspended = function(child) {\r\n\tconst list = this;\r\n\tconst delegated = suspended(list._vnode);\r\n\r\n\tlet node = list._map.get(child);\r\n\tnode[SUSPENDED_COUNT]++;\r\n\r\n\treturn unsuspend => {\r\n\t\tconst wrappedUnsuspend = () => {\r\n\t\t\tif (!list.props.revealOrder) {\r\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\r\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\r\n\t\t\t\tunsuspend();\r\n\t\t\t} else {\r\n\t\t\t\tnode.push(unsuspend);\r\n\t\t\t\tresolve(list, child, node);\r\n\t\t\t}\r\n\t\t};\r\n\t\tif (delegated) {\r\n\t\t\tdelegated(wrappedUnsuspend);\r\n\t\t} else {\r\n\t\t\twrappedUnsuspend();\r\n\t\t}\r\n\t};\r\n};\r\n\r\nSuspenseList.prototype.render = function(props) {\r\n\tthis._next = null;\r\n\tthis._map = new Map();\r\n\r\n\tconst children = toChildArray(props.children);\r\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\r\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\r\n\t\t// then flip the child list around so that the last child will be\r\n\t\t// the first in the linked list.\r\n\t\tchildren.reverse();\r\n\t}\r\n\t// Build the linked list. Iterate through the children in reverse order\r\n\t// so that `_next` points to the first linked list node to be resolved.\r\n\tfor (let i = children.length; i--; ) {\r\n\t\t// Create a new linked list node as an array of form:\r\n\t\t// \t[suspended_count, resolved_count, next_node]\r\n\t\t// where suspended_count and resolved_count are numeric counters for\r\n\t\t// keeping track how many times a node has been suspended and resolved.\r\n\t\t//\r\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\r\n\t\t// processing callbacks until componentDidMount has been called. In a sense\r\n\t\t// node is suspended at least until componentDidMount gets called!\r\n\t\t//\r\n\t\t// Pending callbacks are added to the end of the node:\r\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\r\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\r\n\t}\r\n\treturn props.children;\r\n};\r\n\r\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function() {\r\n\t// Iterate through all children after mounting for two reasons:\r\n\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\r\n\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\r\n\t//    The nodes can now be completely consumed from the linked list.\r\n\t// 2. Handle nodes that might have gotten resolved between render and\r\n\t//    componentDidMount.\r\n\tthis._map.forEach((node, child) => {\r\n\t\tresolve(this, child, node);\r\n\t});\r\n};\r\n", "import {\r\n\trender as preactRender,\r\n\thydrate as preactHydrate,\r\n\toptions,\r\n\ttoChildArray,\r\n\tComponent\r\n} from 'preact';\r\n\r\nexport const REACT_ELEMENT_TYPE =\r\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\r\n\t0xeac7;\r\n\r\nconst CAMEL_PROPS = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\r\n\r\nconst IS_DOM = typeof document !== 'undefined';\r\n\r\n// Input types for which onchange should not be converted to oninput.\r\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\r\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\r\nconst onChangeInputType = type =>\r\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\r\n\t\t? /fil|che|rad/i\r\n\t\t: /fil|che|ra/i\r\n\t).test(type);\r\n\r\n// Some libraries like `react-virtualized` explicitly check for this.\r\nComponent.prototype.isReactComponent = {};\r\n\r\n// `UNSAFE_*` lifecycle hooks\r\n// Preact only ever invokes the unprefixed methods.\r\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\r\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\r\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\r\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\r\n// See https://github.com/preactjs/preact/issues/1941\r\n[\r\n\t'componentWillMount',\r\n\t'componentWillReceiveProps',\r\n\t'componentWillUpdate'\r\n].forEach(key => {\r\n\tObject.defineProperty(Component.prototype, key, {\r\n\t\tconfigurable: true,\r\n\t\tget() {\r\n\t\t\treturn this['UNSAFE_' + key];\r\n\t\t},\r\n\t\tset(v) {\r\n\t\t\tObject.defineProperty(this, key, {\r\n\t\t\t\tconfigurable: true,\r\n\t\t\t\twritable: true,\r\n\t\t\t\tvalue: v\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n});\r\n\r\n/**\r\n * Proxy render() since React returns a Component reference.\r\n * @param {import('./internal').VNode} vnode VNode tree to render\r\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\r\n * @param {() => void} [callback] Optional callback that will be called after rendering\r\n * @returns {import('./internal').Component | null} The root component reference or null\r\n */\r\nexport function render(vnode, parent, callback) {\r\n\t// React destroys any existing DOM nodes, see #1727\r\n\t// ...but only on the first render, see #1828\r\n\tif (parent._children == null) {\r\n\t\tparent.textContent = '';\r\n\t}\r\n\r\n\tpreactRender(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nexport function hydrate(vnode, parent, callback) {\r\n\tpreactHydrate(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nlet oldEventHook = options.event;\r\noptions.event = e => {\r\n\tif (oldEventHook) e = oldEventHook(e);\r\n\te.persist = empty;\r\n\te.isPropagationStopped = isPropagationStopped;\r\n\te.isDefaultPrevented = isDefaultPrevented;\r\n\treturn (e.nativeEvent = e);\r\n};\r\n\r\nfunction empty() {}\r\n\r\nfunction isPropagationStopped() {\r\n\treturn this.cancelBubble;\r\n}\r\n\r\nfunction isDefaultPrevented() {\r\n\treturn this.defaultPrevented;\r\n}\r\n\r\nlet classNameDescriptor = {\r\n\tconfigurable: true,\r\n\tget() {\r\n\t\treturn this.class;\r\n\t}\r\n};\r\n\r\nlet oldVNodeHook = options.vnode;\r\noptions.vnode = vnode => {\r\n\tlet type = vnode.type;\r\n\tlet props = vnode.props;\r\n\tlet normalizedProps = props;\r\n\r\n\t// only normalize props on Element nodes\r\n\tif (typeof type === 'string') {\r\n\t\tconst nonCustomElement = type.indexOf('-') === -1;\r\n\t\tnormalizedProps = {};\r\n\r\n\t\tfor (let i in props) {\r\n\t\t\tlet value = props[i];\r\n\r\n\t\t\tif (IS_DOM && i === 'children' && type === 'noscript') {\r\n\t\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\telse if (i === 'value' && 'defaultValue' in props && value == null) {\r\n\t\t\t\t// Skip applying value if it is null/undefined and we already set\r\n\t\t\t\t// a default value\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (\r\n\t\t\t\ti === 'defaultValue' &&\r\n\t\t\t\t'value' in props &&\r\n\t\t\t\tprops.value == null\r\n\t\t\t) {\r\n\t\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\r\n\t\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\r\n\t\t\t\ti = 'value';\r\n\t\t\t} else if (i === 'download' && value === true) {\r\n\t\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\r\n\t\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\r\n\t\t\t\t// converts it to an empty string instead, otherwise the attribute\r\n\t\t\t\t// value will be used as the file name and the file will be called\r\n\t\t\t\t// \"true\" upon downloading it.\r\n\t\t\t\tvalue = '';\r\n\t\t\t} else if (/ondoubleclick/i.test(i)) {\r\n\t\t\t\ti = 'ondblclick';\r\n\t\t\t} else if (\r\n\t\t\t\t/^onchange(textarea|input)/i.test(i + type) &&\r\n\t\t\t\t!onChangeInputType(props.type)\r\n\t\t\t) {\r\n\t\t\t\ti = 'oninput';\r\n\t\t\t} else if (/^onfocus$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusin';\r\n\t\t\t} else if (/^onblur$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusout';\r\n\t\t\t} else if (/^on(Ani|Tra|Tou|BeforeInp)/.test(i)) {\r\n\t\t\t\ti = i.toLowerCase();\r\n\t\t\t} else if (nonCustomElement && CAMEL_PROPS.test(i)) {\r\n\t\t\t\ti = i.replace(/[A-Z0-9]/, '-$&').toLowerCase();\r\n\t\t\t} else if (value === null) {\r\n\t\t\t\tvalue = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tnormalizedProps[i] = value;\r\n\t\t}\r\n\r\n\t\t// Add support for array select values: <select multiple value={[]} />\r\n\t\tif (\r\n\t\t\ttype == 'select' &&\r\n\t\t\tnormalizedProps.multiple &&\r\n\t\t\tArray.isArray(normalizedProps.value)\r\n\t\t) {\r\n\t\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tchild.props.selected =\r\n\t\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Adding support for defaultValue in select tag\r\n\t\tif (type == 'select' && normalizedProps.defaultValue != null) {\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tif (normalizedProps.multiple) {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tvnode.props = normalizedProps;\r\n\r\n\t\tif (props.class != props.className) {\r\n\t\t\tclassNameDescriptor.enumerable = 'className' in props;\r\n\t\t\tif (props.className != null) normalizedProps.class = props.className;\r\n\t\t\tObject.defineProperty(normalizedProps, 'className', classNameDescriptor);\r\n\t\t}\r\n\t}\r\n\r\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\r\n\r\n\tif (oldVNodeHook) oldVNodeHook(vnode);\r\n};\r\n\r\n// Only needed for react-relay\r\nlet currentComponent;\r\nconst oldBeforeRender = options._render;\r\noptions._render = function(vnode) {\r\n\tif (oldBeforeRender) {\r\n\t\toldBeforeRender(vnode);\r\n\t}\r\n\tcurrentComponent = vnode._component;\r\n};\r\n\r\n// This is a very very private internal function for React it\r\n// is used to sort-of do runtime dependency injection. So far\r\n// only `react-relay` makes use of it. It uses it to read the\r\n// context value.\r\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\r\n\tReactCurrentDispatcher: {\r\n\t\tcurrent: {\r\n\t\t\treadContext(context) {\r\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n", "import {\r\n\tcreateElement,\r\n\trender as preactRender,\r\n\tcloneElement as preactCloneElement,\r\n\tcreateRef,\r\n\tComponent,\r\n\tcreateContext,\r\n\tFragment\r\n} from 'preact';\r\nimport {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue\r\n} from 'preact/hooks';\r\nimport { PureComponent } from './PureComponent';\r\nimport { memo } from './memo';\r\nimport { forwardRef } from './forwardRef';\r\nimport { Children } from './Children';\r\nimport { Suspense, lazy } from './suspense';\r\nimport { SuspenseList } from './suspense-list';\r\nimport { createPortal } from './portals';\r\nimport {\r\n\thydrate,\r\n\trender,\r\n\tREACT_ELEMENT_TYPE,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n} from './render';\r\n\r\nconst version = '17.0.2'; // trick libraries to think we are react\r\n\r\n/**\r\n * Legacy version of createElement.\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\r\n */\r\nfunction createFactory(type) {\r\n\treturn createElement.bind(null, type);\r\n}\r\n\r\n/**\r\n * Check if the passed element is a valid (p)react node.\r\n * @param {*} element The element to check\r\n * @returns {boolean}\r\n */\r\nfunction isValidElement(element) {\r\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\r\n}\r\n\r\n/**\r\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\r\n * all vnode normalizations.\r\n * @param {import('./internal').VNode} element The vnode to clone\r\n * @param {object} props Props to add when cloning\r\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\r\n */\r\nfunction cloneElement(element) {\r\n\tif (!isValidElement(element)) return element;\r\n\treturn preactCloneElement.apply(null, arguments);\r\n}\r\n\r\n/**\r\n * Remove a component tree from the DOM, including state and event handlers.\r\n * @param {import('./internal').PreactElement} container\r\n * @returns {boolean}\r\n */\r\nfunction unmountComponentAtNode(container) {\r\n\tif (container._children) {\r\n\t\tpreactRender(null, container);\r\n\t\treturn true;\r\n\t}\r\n\treturn false;\r\n}\r\n\r\n/**\r\n * Get the matching DOM node for a component\r\n * @param {import('./internal').Component} component\r\n * @returns {import('./internal').PreactElement | null}\r\n */\r\nfunction findDOMNode(component) {\r\n\treturn (\r\n\t\t(component &&\r\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\r\n\t\tnull\r\n\t);\r\n}\r\n\r\n/**\r\n * Deprecated way to control batched rendering inside the reconciler, but we\r\n * already schedule in batches inside our rendering code\r\n * @template Arg\r\n * @param {(arg: Arg) => void} callback function that triggers the updated\r\n * @param {Arg} [arg] Optional argument that can be passed to the callback\r\n */\r\n// eslint-disable-next-line camelcase\r\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\r\n * implmented here as a no-op.\r\n * @template Arg\r\n * @template Result\r\n * @param {(arg: Arg) => Result} callback function that runs before the flush\r\n * @param {Arg} [arg] Optional arugment that can be passed to the callback\r\n * @returns\r\n */\r\nconst flushSync = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\r\n * that just renders its children without imposing any restrictions.\r\n */\r\nconst StrictMode = Fragment;\r\n\r\nexport * from 'preact/hooks';\r\nexport {\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\t// eslint-disable-next-line camelcase\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n\r\n// React copies the named exports to the default one.\r\nexport default {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue,\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n", "// @ts-nocheck\nimport { PureComponent } from 'preact/compat'\nimport { Data, I18n } from '../../config'\nimport Icons from '../../icons'\n\nconst THEME_ICONS = {\n  light: 'outline',\n  dark: 'solid',\n}\n\nexport default class Navigation extends PureComponent {\n  constructor() {\n    super()\n\n    this.categories = Data.categories.filter((category) => {\n      return !category.target\n    })\n\n    this.state = {\n      categoryId: this.categories[0].id,\n    }\n  }\n\n  renderIcon(category) {\n    const { icon } = category\n\n    if (icon) {\n      if (icon.svg) {\n        return (\n          <span\n            class=\"flex\"\n            dangerouslySetInnerHTML={{ __html: icon.svg }}\n          ></span>\n        )\n      }\n\n      if (icon.src) {\n        return <img src={icon.src} />\n      }\n    }\n\n    const categoryIcons =\n      Icons.categories[category.id] || Icons.categories.custom\n\n    const style =\n      this.props.icons == 'auto'\n        ? THEME_ICONS[this.props.theme]\n        : this.props.icons\n\n    return categoryIcons[style] || categoryIcons\n  }\n\n  render() {\n    let selectedCategoryIndex = null\n\n    return (\n      <nav\n        id=\"nav\"\n        class=\"padding\"\n        data-position={this.props.position}\n        dir={this.props.dir}\n      >\n        <div class=\"flex relative\">\n          {this.categories.map((category, i) => {\n            const title = category.name || I18n.categories[category.id]\n            const selected =\n              !this.props.unfocused && category.id == this.state.categoryId\n\n            if (selected) {\n              selectedCategoryIndex = i\n            }\n\n            return (\n              <button\n                aria-label={title}\n                aria-selected={selected || undefined}\n                title={title}\n                type=\"button\"\n                class=\"flex flex-grow flex-center\"\n                onMouseDown={(e) => e.preventDefault()}\n                onClick={() => {\n                  this.props.onClick({ category, i })\n                }}\n              >\n                {this.renderIcon(category)}\n              </button>\n            )\n          })}\n\n          <div\n            class=\"bar\"\n            style={{\n              width: `${100 / this.categories.length}%`,\n              opacity: selectedCategoryIndex == null ? 0 : 1,\n              transform:\n                this.props.dir === 'rtl'\n                  ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)`\n                  : `translateX(${selectedCategoryIndex * 100}%)`,\n            }}\n          ></div>\n        </div>\n      </nav>\n    )\n  }\n}\n", "import { PureComponent } from 'preact/compat'\n\nexport default class PureInlineComponent extends PureComponent {\n  shouldComponentUpdate(nextProps) {\n    for (let k in nextProps) {\n      if (k == 'children') continue\n\n      if (nextProps[k] != this.props[k]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n", "// @ts-nocheck\nimport { Component, createRef } from 'preact'\n\nimport { deepEqual, sleep, getEmojiData } from '../../utils'\nimport { Data, I18n, init } from '../../config'\nimport { SearchIndex, Store, FrequentlyUsed } from '../../helpers'\nimport Icons from '../../icons'\n\nimport { Emoji } from '../Emoji'\nimport { Navigation } from '../Navigation'\nimport { PureInlineComponent } from '../HOCs'\n\nconst Performance = {\n  rowsPerRender: 10,\n}\n\nexport default class Picker extends Component {\n  constructor(props) {\n    super()\n\n    this.observers = []\n\n    this.state = {\n      pos: [-1, -1],\n      perLine: this.initDynamicPerLine(props),\n      visibleRows: { 0: true },\n      ...this.getInitialState(props),\n    }\n  }\n\n  getInitialState(props = this.props) {\n    return {\n      skin: Store.get('skin') || props.skin,\n      theme: this.initTheme(props.theme),\n    }\n  }\n\n  componentWillMount() {\n    this.dir = I18n.rtl ? 'rtl' : 'ltr'\n    this.refs = {\n      menu: createRef(),\n      navigation: createRef(),\n      scroll: createRef(),\n      search: createRef(),\n      searchInput: createRef(),\n      skinToneButton: createRef(),\n      skinToneRadio: createRef(),\n    }\n\n    this.initGrid()\n\n    if (\n      this.props.stickySearch == false &&\n      this.props.searchPosition == 'sticky'\n    ) {\n      console.warn(\n        '[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.',\n      )\n\n      this.props.searchPosition = 'static'\n    }\n  }\n\n  componentDidMount() {\n    this.register()\n\n    this.shadowRoot = this.base.parentNode\n\n    if (this.props.autoFocus) {\n      const { searchInput } = this.refs\n      if (searchInput.current) {\n        searchInput.current.focus()\n      }\n    }\n  }\n\n  componentWillReceiveProps(nextProps) {\n    this.nextState || (this.nextState = {})\n\n    for (const k in nextProps) {\n      this.nextState[k] = nextProps[k]\n    }\n\n    clearTimeout(this.nextStateTimer)\n    this.nextStateTimer = setTimeout(() => {\n      let requiresGridReset = false\n\n      for (const k in this.nextState) {\n        this.props[k] = this.nextState[k]\n\n        if (k === 'custom' || k === 'categories') {\n          requiresGridReset = true\n        }\n      }\n\n      delete this.nextState\n      const nextState = this.getInitialState()\n\n      if (requiresGridReset) {\n        return this.reset(nextState)\n      }\n\n      this.setState(nextState)\n    })\n  }\n\n  componentWillUnmount() {\n    this.unregister()\n  }\n\n  async reset(nextState = {}) {\n    await init(this.props)\n\n    this.initGrid()\n    this.unobserve()\n\n    this.setState(nextState, () => {\n      this.observeCategories()\n      this.observeRows()\n    })\n  }\n\n  register() {\n    document.addEventListener('click', this.handleClickOutside)\n    this.observe()\n  }\n\n  unregister() {\n    document.removeEventListener('click', this.handleClickOutside)\n    this.darkMedia?.removeEventListener('change', this.darkMediaCallback)\n    this.unobserve()\n  }\n\n  observe() {\n    this.observeCategories()\n    this.observeRows()\n  }\n\n  unobserve({ except = [] } = {}) {\n    if (!Array.isArray(except)) {\n      except = [except]\n    }\n\n    for (const observer of this.observers) {\n      if (except.includes(observer)) continue\n      observer.disconnect()\n    }\n\n    this.observers = [].concat(except)\n  }\n\n  initGrid() {\n    const { categories } = Data\n\n    this.refs.categories = new Map()\n\n    const navKey = Data.categories.map((category) => category.id).join(',')\n    if (this.navKey && this.navKey != navKey) {\n      this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0)\n    }\n    this.navKey = navKey\n\n    this.grid = []\n    this.grid.setsize = 0\n\n    const addRow = (rows, category) => {\n      const row = []\n      row.__categoryId = category.id\n      row.__index = rows.length\n      this.grid.push(row)\n\n      const rowIndex = this.grid.length - 1\n      const rowRef = rowIndex % Performance.rowsPerRender ? {} : createRef()\n      rowRef.index = rowIndex\n      rowRef.posinset = this.grid.setsize + 1\n      rows.push(rowRef)\n\n      return row\n    }\n\n    for (let category of categories) {\n      const rows = []\n      let row = addRow(rows, category)\n\n      for (let emoji of category.emojis) {\n        if (row.length == this.getPerLine()) {\n          row = addRow(rows, category)\n        }\n\n        this.grid.setsize += 1\n        row.push(emoji)\n      }\n\n      this.refs.categories.set(category.id, { root: createRef(), rows })\n    }\n  }\n\n  darkMediaCallback = () => {\n    if (this.props.theme != 'auto') return\n    this.setState({ theme: this.darkMedia.matches ? 'dark' : 'light' })\n  }\n\n  initTheme(theme) {\n    if (theme != 'auto') return theme\n\n    if (!this.darkMedia) {\n      this.darkMedia = matchMedia('(prefers-color-scheme: dark)')\n      if (this.darkMedia.media.match(/^not/)) return 'light'\n\n      this.darkMedia.addEventListener('change', this.darkMediaCallback)\n    }\n\n    return this.darkMedia.matches ? 'dark' : 'light'\n  }\n\n  handleClickOutside = (e) => {\n    const { element } = this.props\n\n    if (e.target != element) {\n      if (this.state.showSkins) {\n        this.closeSkins()\n      }\n\n      if (this.props.onClickOutside) {\n        this.props.onClickOutside(e)\n      }\n    }\n  }\n\n  handleBaseClick = (e) => {\n    if (!this.state.showSkins) return\n    if (!e.target.closest('.menu')) {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  handleBaseKeydown = (e) => {\n    if (!this.state.showSkins) return\n    if (e.key == 'Escape') {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  initDynamicPerLine(props = this.props) {\n    if (!props.dynamicWidth) return\n    const { element, emojiButtonSize } = props\n\n    const calculatePerLine = () => {\n      const { width } = element.getBoundingClientRect()\n      return Math.floor(width / emojiButtonSize)\n    }\n\n    const observer = new ResizeObserver(() => {\n      this.unobserve({ except: observer })\n      this.setState({ perLine: calculatePerLine() }, () => {\n        this.initGrid()\n        this.forceUpdate(() => {\n          this.observeCategories()\n          this.observeRows()\n        })\n      })\n    })\n\n    observer.observe(element)\n    this.observers.push(observer)\n\n    return calculatePerLine()\n  }\n\n  getPerLine() {\n    return this.state.perLine || this.props.perLine\n  }\n\n  getEmojiByPos([p1, p2]) {\n    const grid = this.state.searchResults || this.grid\n    const emoji = grid[p1] && grid[p1][p2]\n\n    if (!emoji) return\n    return SearchIndex.get(emoji)\n  }\n\n  observeCategories() {\n    const navigation = this.refs.navigation.current\n    if (!navigation) return\n\n    const visibleCategories = new Map()\n    const setFocusedCategory = (categoryId) => {\n      if (categoryId != navigation.state.categoryId) {\n        navigation.setState({ categoryId })\n      }\n    }\n\n    const observerOptions = {\n      root: this.refs.scroll.current,\n      threshold: [0.0, 1.0],\n    }\n\n    const observer = new IntersectionObserver((entries) => {\n      for (const entry of entries) {\n        const id = entry.target.dataset.id\n        visibleCategories.set(id, entry.intersectionRatio)\n      }\n\n      const ratios = [...visibleCategories]\n      for (const [id, ratio] of ratios) {\n        if (ratio) {\n          setFocusedCategory(id)\n          break\n        }\n      }\n    }, observerOptions)\n\n    for (const { root } of this.refs.categories.values()) {\n      observer.observe(root.current)\n    }\n\n    this.observers.push(observer)\n  }\n\n  observeRows() {\n    const visibleRows = { ...this.state.visibleRows }\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        for (const entry of entries) {\n          const index = parseInt(entry.target.dataset.index)\n\n          if (entry.isIntersecting) {\n            visibleRows[index] = true\n          } else {\n            delete visibleRows[index]\n          }\n        }\n\n        this.setState({ visibleRows })\n      },\n      {\n        root: this.refs.scroll.current,\n        rootMargin: `${\n          this.props.emojiButtonSize * (Performance.rowsPerRender + 5)\n        }px 0px ${this.props.emojiButtonSize * Performance.rowsPerRender}px`,\n      },\n    )\n\n    for (const { rows } of this.refs.categories.values()) {\n      for (const row of rows) {\n        if (row.current) {\n          observer.observe(row.current)\n        }\n      }\n    }\n\n    this.observers.push(observer)\n  }\n\n  preventDefault(e) {\n    e.preventDefault()\n  }\n\n  handleSearchClick = () => {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    if (!emoji) return\n\n    this.setState({ pos: [-1, -1] })\n  }\n\n  handleSearchInput = async () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    const { value } = input\n    const searchResults = await SearchIndex.search(value)\n    const afterRender = () => {\n      if (!this.refs.scroll.current) return\n      this.refs.scroll.current.scrollTop = 0\n    }\n\n    if (!searchResults) {\n      return this.setState({ searchResults, pos: [-1, -1] }, afterRender)\n    }\n\n    const pos = input.selectionStart == input.value.length ? [0, 0] : [-1, -1]\n    const grid = []\n    grid.setsize = searchResults.length\n    let row = null\n\n    for (let emoji of searchResults) {\n      if (!grid.length || row.length == this.getPerLine()) {\n        row = []\n        row.__categoryId = 'search'\n        row.__index = grid.length\n        grid.push(row)\n      }\n\n      row.push(emoji)\n    }\n\n    this.ignoreMouse()\n    this.setState({ searchResults: grid, pos }, afterRender)\n  }\n\n  handleSearchKeyDown = (e) => {\n    // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n    const input = e.currentTarget\n    e.stopImmediatePropagation()\n\n    switch (e.key) {\n      case 'ArrowLeft':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, left: true })\n        break\n\n      case 'ArrowRight':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, right: true })\n        break\n\n      case 'ArrowUp':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, up: true })\n        break\n\n      case 'ArrowDown':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, down: true })\n        break\n\n      case 'Enter':\n        e.preventDefault()\n        this.handleEmojiClick({ e, pos: this.state.pos })\n        break\n\n      case 'Escape':\n        e.preventDefault()\n        if (this.state.searchResults) {\n          this.clearSearch()\n        } else {\n          this.unfocusSearch()\n        }\n        break\n\n      default:\n        break\n    }\n  }\n\n  clearSearch = () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.value = ''\n    input.focus()\n\n    this.handleSearchInput()\n  }\n\n  unfocusSearch() {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.blur()\n  }\n\n  navigate({ e, input, left, right, up, down }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    let [p1, p2] = this.state.pos\n\n    const pos = (() => {\n      if (p1 == 0) {\n        if (p2 == 0 && !e.repeat && (left || up)) {\n          return null\n        }\n      }\n\n      if (p1 == -1) {\n        if (\n          !e.repeat &&\n          (right || down) &&\n          input.selectionStart == input.value.length\n        ) {\n          return [0, 0]\n        }\n\n        return null\n      }\n\n      if (left || right) {\n        let row = grid[p1]\n        const increment = left ? -1 : 1\n\n        p2 += increment\n        if (!row[p2]) {\n          p1 += increment\n          row = grid[p1]\n\n          if (!row) {\n            p1 = left ? 0 : grid.length - 1\n            p2 = left ? 0 : grid[p1].length - 1\n\n            return [p1, p2]\n          }\n\n          p2 = left ? row.length - 1 : 0\n        }\n\n        return [p1, p2]\n      }\n\n      if (up || down) {\n        p1 += up ? -1 : 1\n        const row = grid[p1]\n\n        if (!row) {\n          p1 = up ? 0 : grid.length - 1\n          p2 = up ? 0 : grid[p1].length - 1\n\n          return [p1, p2]\n        }\n\n        if (!row[p2]) {\n          p2 = row.length - 1\n        }\n\n        return [p1, p2]\n      }\n    })()\n\n    if (pos) {\n      e.preventDefault()\n    } else {\n      if (this.state.pos[0] > -1) {\n        this.setState({ pos: [-1, -1] })\n      }\n\n      return\n    }\n\n    this.setState({ pos, keyboard: true }, () => {\n      this.scrollTo({ row: pos[0] })\n    })\n  }\n\n  scrollTo({ categoryId, row }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    const scroll = this.refs.scroll.current\n    const scrollRect = scroll.getBoundingClientRect()\n\n    let scrollTop = 0\n\n    if (row >= 0) {\n      categoryId = grid[row].__categoryId\n    }\n\n    if (categoryId) {\n      const ref =\n        this.refs[categoryId] || this.refs.categories.get(categoryId).root\n      const categoryRect = ref.current.getBoundingClientRect()\n\n      scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1\n    }\n\n    if (row >= 0) {\n      if (!row) {\n        scrollTop = 0\n      } else {\n        const rowIndex = grid[row].__index\n        const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize\n        const rowBot =\n          rowTop +\n          this.props.emojiButtonSize +\n          this.props.emojiButtonSize * 0.88\n\n        if (rowTop < scroll.scrollTop) {\n          scrollTop = rowTop\n        } else if (rowBot > scroll.scrollTop + scrollRect.height) {\n          scrollTop = rowBot - scrollRect.height\n        } else {\n          return\n        }\n      }\n    }\n\n    this.ignoreMouse()\n    scroll.scrollTop = scrollTop\n  }\n\n  ignoreMouse() {\n    this.mouseIsIgnored = true\n    clearTimeout(this.ignoreMouseTimer)\n    this.ignoreMouseTimer = setTimeout(() => {\n      delete this.mouseIsIgnored\n    }, 100)\n  }\n\n  handleCategoryClick = ({ category, i }) => {\n    this.scrollTo(i == 0 ? { row: -1 } : { categoryId: category.id })\n  }\n\n  handleEmojiOver(pos) {\n    if (this.mouseIsIgnored || this.state.showSkins) return\n    this.setState({ pos: pos || [-1, -1], keyboard: false })\n  }\n\n  handleEmojiClick({ e, emoji, pos }) {\n    if (!this.props.onEmojiSelect) return\n\n    if (!emoji && pos) {\n      emoji = this.getEmojiByPos(pos)\n    }\n\n    if (emoji) {\n      const emojiData = getEmojiData(emoji, { skinIndex: this.state.skin - 1 })\n\n      if (this.props.maxFrequentRows) {\n        FrequentlyUsed.add(emojiData, this.props)\n      }\n\n      this.props.onEmojiSelect(emojiData, e)\n    }\n  }\n\n  openSkins = (e) => {\n    const { currentTarget } = e\n    const rect = currentTarget.getBoundingClientRect()\n\n    this.setState({ showSkins: rect }, async () => {\n      // Firefox requires 2 frames for the transition to consistenly work\n      await sleep(2)\n\n      const menu = this.refs.menu.current\n      if (!menu) return\n\n      menu.classList.remove('hidden')\n      this.refs.skinToneRadio.current.focus()\n\n      this.base.addEventListener('click', this.handleBaseClick, true)\n      this.base.addEventListener('keydown', this.handleBaseKeydown, true)\n    })\n  }\n\n  closeSkins() {\n    if (!this.state.showSkins) return\n    this.setState({ showSkins: null, tempSkin: null })\n\n    this.base.removeEventListener('click', this.handleBaseClick)\n    this.base.removeEventListener('keydown', this.handleBaseKeydown)\n  }\n\n  handleSkinMouseOver(tempSkin) {\n    this.setState({ tempSkin })\n  }\n\n  handleSkinClick(skin) {\n    this.ignoreMouse()\n    this.closeSkins()\n\n    this.setState({ skin, tempSkin: null })\n    Store.set('skin', skin)\n  }\n\n  renderNav() {\n    return (\n      <Navigation\n        key={this.navKey}\n        ref={this.refs.navigation}\n        icons={this.props.icons}\n        theme={this.state.theme}\n        dir={this.dir}\n        unfocused={!!this.state.searchResults}\n        position={this.props.navPosition}\n        onClick={this.handleCategoryClick}\n      />\n    )\n  }\n\n  renderPreview() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const noSearchResults =\n      this.state.searchResults && !this.state.searchResults.length\n\n    return (\n      <div\n        id=\"preview\"\n        class=\"flex flex-middle\"\n        dir={this.dir}\n        data-position={this.props.previewPosition}\n      >\n        <div class=\"flex flex-middle flex-grow\">\n          <div\n            class=\"flex flex-auto flex-middle flex-center\"\n            style={{\n              height: this.props.emojiButtonSize,\n              fontSize: this.props.emojiButtonSize,\n            }}\n          >\n            <Emoji\n              emoji={emoji}\n              id={\n                noSearchResults\n                  ? this.props.noResultsEmoji || 'cry'\n                  : this.props.previewEmoji ||\n                    (this.props.previewPosition == 'top'\n                      ? 'point_down'\n                      : 'point_up')\n              }\n              set={this.props.set}\n              size={this.props.emojiButtonSize}\n              skin={this.state.tempSkin || this.state.skin}\n              spritesheet={true}\n              getSpritesheetURL={this.props.getSpritesheetURL}\n            />\n          </div>\n\n          <div class={`margin-${this.dir[0]}`}>\n            {emoji || noSearchResults ? (\n              <div class={`padding-${this.dir[2]} align-${this.dir[0]}`}>\n                <div class=\"preview-title ellipsis\">\n                  {emoji ? emoji.name : I18n.search_no_results_1}\n                </div>\n                <div class=\"preview-subtitle ellipsis color-c\">\n                  {emoji ? emoji.skins[0].shortcodes : I18n.search_no_results_2}\n                </div>\n              </div>\n            ) : (\n              <div class=\"preview-placeholder color-c\">{I18n.pick}</div>\n            )}\n          </div>\n        </div>\n\n        {!emoji &&\n          this.props.skinTonePosition == 'preview' &&\n          this.renderSkinToneButton()}\n      </div>\n    )\n  }\n\n  renderEmojiButton(emoji, { pos, posinset, grid }) {\n    const size = this.props.emojiButtonSize\n    const skin = this.state.tempSkin || this.state.skin\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n    const native = emojiSkin.native\n    const selected = deepEqual(this.state.pos, pos)\n    const key = pos.concat(emoji.id).join('')\n\n    return (\n      <PureInlineComponent key={key} {...{ selected, skin, size }}>\n        <button\n          aria-label={native}\n          aria-selected={selected || undefined}\n          aria-posinset={posinset}\n          aria-setsize={grid.setsize}\n          data-keyboard={this.state.keyboard}\n          title={this.props.previewPosition == 'none' ? emoji.name : undefined}\n          type=\"button\"\n          class=\"flex flex-center flex-middle\"\n          tabindex=\"-1\"\n          onClick={(e) => this.handleEmojiClick({ e, emoji })}\n          onMouseEnter={() => this.handleEmojiOver(pos)}\n          onMouseLeave={() => this.handleEmojiOver()}\n          style={{\n            width: this.props.emojiButtonSize,\n            height: this.props.emojiButtonSize,\n            fontSize: this.props.emojiSize,\n            lineHeight: 0,\n          }}\n        >\n          <div\n            aria-hidden=\"true\"\n            class=\"background\"\n            style={{\n              borderRadius: this.props.emojiButtonRadius,\n              backgroundColor: this.props.emojiButtonColors\n                ? this.props.emojiButtonColors[\n                    (posinset - 1) % this.props.emojiButtonColors.length\n                  ]\n                : undefined,\n            }}\n          ></div>\n          <Emoji\n            emoji={emoji}\n            set={this.props.set}\n            size={this.props.emojiSize}\n            skin={skin}\n            spritesheet={true}\n            getSpritesheetURL={this.props.getSpritesheetURL}\n          />\n        </button>\n      </PureInlineComponent>\n    )\n  }\n\n  renderSearch() {\n    const renderSkinTone =\n      this.props.previewPosition == 'none' ||\n      this.props.skinTonePosition == 'search'\n\n    return (\n      <div>\n        <div class=\"spacer\"></div>\n        <div class=\"flex flex-middle\">\n          <div class=\"search relative flex-grow\">\n            <input\n              type=\"search\"\n              ref={this.refs.searchInput}\n              placeholder={I18n.search}\n              onClick={this.handleSearchClick}\n              onInput={this.handleSearchInput}\n              onKeyDown={this.handleSearchKeyDown}\n              autoComplete=\"off\"\n            ></input>\n            <span class=\"icon loupe flex\">{Icons.search.loupe}</span>\n            {this.state.searchResults && (\n              <button\n                title=\"Clear\"\n                aria-label=\"Clear\"\n                type=\"button\"\n                class=\"icon delete flex\"\n                onClick={this.clearSearch}\n                onMouseDown={this.preventDefault}\n              >\n                {Icons.search.delete}\n              </button>\n            )}\n          </div>\n\n          {renderSkinTone && this.renderSkinToneButton()}\n        </div>\n      </div>\n    )\n  }\n\n  renderSearchResults() {\n    const { searchResults } = this.state\n    if (!searchResults) return null\n\n    return (\n      <div class=\"category\" ref={this.refs.search}>\n        <div class={`sticky padding-small align-${this.dir[0]}`}>\n          {I18n.categories.search}\n        </div>\n        <div>\n          {!searchResults.length ? (\n            <div class={`padding-small align-${this.dir[0]}`}>\n              {this.props.onAddCustomEmoji && (\n                <a onClick={this.props.onAddCustomEmoji}>{I18n.add_custom}</a>\n              )}\n            </div>\n          ) : (\n            searchResults.map((row, i) => {\n              return (\n                <div class=\"flex\">\n                  {row.map((emoji, ii) => {\n                    return this.renderEmojiButton(emoji, {\n                      pos: [i, ii],\n                      posinset: i * this.props.perLine + ii + 1,\n                      grid: searchResults,\n                    })\n                  })}\n                </div>\n              )\n            })\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  renderCategories() {\n    const { categories } = Data\n    const hidden = !!this.state.searchResults\n    const perLine = this.getPerLine()\n\n    return (\n      <div\n        style={{\n          visibility: hidden ? 'hidden' : undefined,\n          display: hidden ? 'none' : undefined,\n          height: '100%',\n        }}\n      >\n        {categories.map((category) => {\n          const { root, rows } = this.refs.categories.get(category.id)\n\n          return (\n            <div\n              data-id={category.target ? category.target.id : category.id}\n              class=\"category\"\n              ref={root}\n            >\n              <div class={`sticky padding-small align-${this.dir[0]}`}>\n                {category.name || I18n.categories[category.id]}\n              </div>\n              <div\n                class=\"relative\"\n                style={{\n                  height: rows.length * this.props.emojiButtonSize,\n                }}\n              >\n                {rows.map((row, i) => {\n                  const targetRow =\n                    row.index - (row.index % Performance.rowsPerRender)\n                  const visible = this.state.visibleRows[targetRow]\n                  const ref = 'current' in row ? row : undefined\n\n                  if (!visible && !ref) {\n                    return null\n                  }\n\n                  const start = i * perLine\n                  const end = start + perLine\n                  const emojiIds = category.emojis.slice(start, end)\n\n                  if (emojiIds.length < perLine) {\n                    emojiIds.push(...new Array(perLine - emojiIds.length))\n                  }\n\n                  return (\n                    <div\n                      key={row.index}\n                      data-index={row.index}\n                      ref={ref}\n                      class=\"flex row\"\n                      style={{ top: i * this.props.emojiButtonSize }}\n                    >\n                      {visible &&\n                        emojiIds.map((emojiId, ii) => {\n                          if (!emojiId) {\n                            return (\n                              <div\n                                style={{\n                                  width: this.props.emojiButtonSize,\n                                  height: this.props.emojiButtonSize,\n                                }}\n                              ></div>\n                            )\n                          }\n\n                          const emoji = SearchIndex.get(emojiId)\n\n                          return this.renderEmojiButton(emoji, {\n                            pos: [row.index, ii],\n                            posinset: row.posinset + ii,\n                            grid: this.grid,\n                          })\n                        })}\n                    </div>\n                  )\n                })}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  renderSkinToneButton() {\n    if (this.props.skinTonePosition == 'none') {\n      return null\n    }\n\n    return (\n      <div\n        class=\"flex flex-auto flex-center flex-middle\"\n        style={{\n          position: 'relative',\n          width: this.props.emojiButtonSize,\n          height: this.props.emojiButtonSize,\n        }}\n      >\n        <button\n          type=\"button\"\n          ref={this.refs.skinToneButton}\n          class=\"skin-tone-button flex flex-auto flex-center flex-middle\"\n          aria-selected={this.state.showSkins ? '' : undefined}\n          aria-label={I18n.skins.choose}\n          title={I18n.skins.choose}\n          onClick={this.openSkins}\n          style={{\n            width: this.props.emojiSize,\n            height: this.props.emojiSize,\n          }}\n        >\n          <span class={`skin-tone skin-tone-${this.state.skin}`}></span>\n        </button>\n      </div>\n    )\n  }\n\n  renderLiveRegion() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const contents = emoji ? emoji.name : ''\n\n    return (\n      <div aria-live=\"polite\" class=\"sr-only\">\n        {contents}\n      </div>\n    )\n  }\n\n  renderSkins() {\n    const skinToneButton = this.refs.skinToneButton.current\n    const skinToneButtonRect = skinToneButton.getBoundingClientRect()\n    const baseRect = this.base.getBoundingClientRect()\n\n    const position = {}\n\n    if (this.dir == 'ltr') {\n      position.right = baseRect.right - skinToneButtonRect.right - 3\n    } else {\n      position.left = skinToneButtonRect.left - baseRect.left - 3\n    }\n\n    if (\n      this.props.previewPosition == 'bottom' &&\n      this.props.skinTonePosition == 'preview'\n    ) {\n      position.bottom = baseRect.bottom - skinToneButtonRect.top + 6\n    } else {\n      position.top = skinToneButtonRect.bottom - baseRect.top + 3\n      position.bottom = 'auto'\n    }\n\n    return (\n      <div\n        ref={this.refs.menu}\n        role=\"radiogroup\"\n        dir={this.dir}\n        aria-label={I18n.skins.choose}\n        class=\"menu hidden\"\n        data-position={position.top ? 'top' : 'bottom'}\n        style={position}\n      >\n        {[...Array(6).keys()].map((i) => {\n          const skin = i + 1\n          const checked = this.state.skin == skin\n\n          return (\n            <div>\n              <input\n                type=\"radio\"\n                name=\"skin-tone\"\n                value={skin}\n                aria-label={I18n.skins[skin]}\n                ref={checked ? this.refs.skinToneRadio : null}\n                defaultChecked={checked}\n                onChange={() => this.handleSkinMouseOver(skin)}\n                onKeyDown={(e) => {\n                  if (\n                    e.code == 'Enter' ||\n                    e.code == 'Space' ||\n                    e.code == 'Tab'\n                  ) {\n                    e.preventDefault()\n                    this.handleSkinClick(skin)\n                  }\n                }}\n              ></input>\n\n              <button\n                aria-hidden=\"true\"\n                tabindex=\"-1\"\n                onClick={() => this.handleSkinClick(skin)}\n                onMouseEnter={() => this.handleSkinMouseOver(skin)}\n                onMouseLeave={() => this.handleSkinMouseOver()}\n                class=\"option flex flex-grow flex-middle\"\n              >\n                <span class={`skin-tone skin-tone-${skin}`}></span>\n                <span class=\"margin-small-lr\">{I18n.skins[skin]}</span>\n              </button>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  render() {\n    const lineWidth = this.props.perLine * this.props.emojiButtonSize\n\n    return (\n      <section\n        id=\"root\"\n        class=\"flex flex-column\"\n        dir={this.dir}\n        style={{\n          width: this.props.dynamicWidth\n            ? '100%'\n            : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`,\n        }}\n        data-emoji-set={this.props.set}\n        data-theme={this.state.theme}\n        data-menu={this.state.showSkins ? '' : undefined}\n      >\n        {this.props.previewPosition == 'top' && this.renderPreview()}\n        {this.props.navPosition == 'top' && this.renderNav()}\n        {this.props.searchPosition == 'sticky' && (\n          <div class=\"padding-lr\">{this.renderSearch()}</div>\n        )}\n\n        <div ref={this.refs.scroll} class=\"scroll flex-grow padding-lr\">\n          <div\n            style={{\n              width: this.props.dynamicWidth ? '100%' : lineWidth,\n              height: '100%',\n            }}\n          >\n            {this.props.searchPosition == 'static' && this.renderSearch()}\n            {this.renderSearchResults()}\n            {this.renderCategories()}\n          </div>\n        </div>\n\n        {this.props.navPosition == 'bottom' && this.renderNav()}\n        {this.props.previewPosition == 'bottom' && this.renderPreview()}\n        {this.state.showSkins && this.renderSkins()}\n        {this.renderLiveRegion()}\n      </section>\n    )\n  }\n}\n", "// @ts-nocheck\nimport { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { ShadowElement } from '../HTMLElement'\nimport { Picker, PickerStyles } from '.'\nimport PickerProps from './PickerProps'\n\nexport default class PickerElement extends ShadowElement {\n  static Props = PickerProps\n\n  constructor(props) {\n    super(props, { styles: PickerStyles })\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, PickerProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init(props)\n    if (this.disconnected) return\n\n    render(<Picker {...props} />, this.shadowRoot)\n  }\n}\n\nif (\n  typeof customElements !== 'undefined' &&\n  !customElements.get('em-emoji-picker')\n) {\n  customElements.define('em-emoji-picker', PickerElement)\n}\n", "module.exports = \"3dd0c551c1eb387f\";", "import * as EmojiMart from './index'\nwindow.EmojiMart = EmojiMart\n"], "names": ["$6c4536fb2a4895b4$export$2e2bcd8739ae039", "self", "ReferenceError", "$ad94dbce8d8f97cd$var$asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "$ad94dbce8d8f97cd$export$2e2bcd8739ae039", "fn", "this", "args", "arguments", "apply", "err", "undefined", "$93d6a431d757303b$export$2e2bcd8739ae039", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "$4e5e624c58f130d4$var$_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "$4e5e624c58f130d4$export$2e2bcd8739ae039", "protoProps", "staticProps", "prototype", "$1925096de05c38f6$export$2e2bcd8739ae039", "obj", "$29142ba251ee4641$var$setPrototypeOf", "o1", "p1", "setPrototypeOf", "o", "p", "__proto__", "$29142ba251ee4641$export$2e2bcd8739ae039", "$f5126df1c9010078$export$2e2bcd8739ae039", "subClass", "superClass", "create", "constructor", "$d157009937ef4ff3$export$2e2bcd8739ae039", "source", "ownKeys", "keys", "getOwnPropertySymbols", "concat", "filter", "sym", "getOwnPropertyDescriptor", "for<PERSON>ach", "$c95062186ceba6fa$export$2e2bcd8739ae039", "arr", "Array", "isArray", "$2485abae9c544bad$export$2e2bcd8739ae039", "iter", "Symbol", "iterator", "from", "$8bc35a03689a517d$export$2e2bcd8739ae039", "$cdd568d0e069067c$export$2e2bcd8739ae039", "len", "arr2", "$e4bdcee7faff07c4$export$2e2bcd8739ae039", "minLen", "n", "toString", "call", "slice", "name", "test", "$86c5c41d8d85293f$export$2e2bcd8739ae039", "$93c15ee9d3c33e8c$export$2e2bcd8739ae039", "$3ee521c8d01c5032$export$2e2bcd8739ae039", "$d418667d8ed84253$export$2e2bcd8739ae039", "$b8dcecc0bb01f0df$export$2e2bcd8739ae039", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "$ec604029a6b4d2ce$var$getPrototypeOf", "getPrototypeOf", "$ec604029a6b4d2ce$export$2e2bcd8739ae039", "$145791f4947b6114$export$2e2bcd8739ae039", "$59c2014697fa5835$export$2e2bcd8739ae039", "$5677636b33a9838c$export$2e2bcd8739ae039", "Derived", "hasNativeReflectConstruct", "result", "Super", "<PERSON><PERSON><PERSON><PERSON>", "$39d7cefc4e35d7d3$var$runtime", "exports", "Op", "hasOwn", "hasOwnProperty", "$Symbol", "iteratorSymbol", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "err1", "wrap", "innerFn", "outerFn", "tryLocsList", "protoGenerator", "Generator", "generator", "context", "Context", "_invoke", "state", "GenStateSuspendedStart", "method", "GenStateExecuting", "Error", "GenStateCompleted", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "type", "GenStateSuspendedYield", "makeInvokeMethod", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "AsyncIterator", "PromiseImpl", "invoke", "value1", "__await", "unwrapped", "previousPromise", "callInvokeWithMethodAndArg", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "next1", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "mark", "awrap", "async", "object", "key1", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "$39d7cefc4e35d7d3$exports", "regeneratorRuntime", "accidentalStrictMode", "globalThis", "Function", "options", "vnodeId", "rerenderQueue", "defer", "prevDebounce", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "children", "ref", "normalizedProps", "defaultProps", "createVNode", "original", "vnode", "__", "__b", "__e", "__d", "__h", "Fragment", "Component", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "process", "debounceRendering", "queue", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "oldDom", "parentDom", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "newDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reorderC<PERSON>dren", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "applyRef", "tmp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "nextDom", "sibDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "setStyle", "style", "setProperty", "dom", "oldValue", "useCapture", "cssText", "replace", "toLowerCase", "_listeners", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "setAttribute", "removeAttribute", "event", "newVNode", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "provider", "componentContext", "newType", "contextType", "render", "doR<PERSON>", "sub", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "innerHTML", "hydrate", "diffProps", "checked", "diffElementNodes", "diffed", "root", "cb", "current", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "bind", "setTimeout", "__source", "__self", "$6103878897084a15$export$2e2bcd8739ae039", "set", "window", "localStorage", "JSON", "stringify", "get", "parse", "$9b62b44a9d23e9b3$var$CACHE", "Map", "$9b62b44a9d23e9b3$var$VERSIONS", "v", "emoji", "$9b62b44a9d23e9b3$var$isSupported", "has", "supported", "$9b62b44a9d23e9b3$var$isEmojiSupported", "ctx", "navigator", "userAgent", "includes", "getContext", "willReadFrequently", "CANVAS_WIDTH", "textSize", "Math", "floor", "CANVAS_HEIGHT", "font", "textBaseline", "canvas", "width", "height", "unicode", "clearRect", "fillStyle", "fillText", "getImageData", "count", "x", "y", "measureText", "$9b62b44a9d23e9b3$export$2e2bcd8739ae039", "latestVersion", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_step", "_iterator", "_value", "return", "noCountryFlags", "$4bf584a3aa18bc2e$var$DEFAULTS", "$4bf584a3aa18bc2e$var$Index", "$4bf584a3aa18bc2e$export$2e2bcd8739ae039", "add", "emojiId", "id", "param", "maxFrequentRows", "perLine", "emojiIds", "max", "last", "emojiId1", "aScore", "bScore", "localeCompare", "removedIds", "removedId", "splice", "DEFAULTS", "$539692ed3c2014cd$exports", "$e56fab9f28ec2f6c$export$2e2bcd8739ae039", "autoFocus", "dynamicWidth", "emojiButtonColors", "emojiButtonRadius", "emojiButtonSize", "emojiSize", "emojiVersion", "choices", "except<PERSON><PERSON><PERSON><PERSON>", "icons", "locale", "navPosition", "noResultsEmoji", "previewEmoji", "previewPosition", "searchPosition", "skin", "skinTonePosition", "theme", "categories", "categoryIcons", "custom", "i18n", "getImageURL", "getSpritesheetURL", "onAddCustomEmoji", "onClickOutside", "onEmojiSelect", "stickySearch", "deprecated", "$b55119b9446ffa3f$export$dbe3113d60765c1a", "$b55119b9446ffa3f$export$2d0294657ab35f1b", "$b55119b9446ffa3f$var$fetchCache", "$b55119b9446ffa3f$var$fetchJSON", "src", "$b55119b9446ffa3f$var$_fetchJSON", "$parcel$interopDefault", "_callee", "response", "json", "fetch", "_ctx", "$b55119b9446ffa3f$var$promise", "$b55119b9446ffa3f$var$initCallback", "$b55119b9446ffa3f$var$initialized", "$b55119b9446ffa3f$export$2cd8252107eb640b", "caller", "$b55119b9446ffa3f$var$_init", "console", "warn", "$b55119b9446ffa3f$var$__init", "alias", "category", "prevCategory", "emoji1", "latestVersionSupport", "categoryIndex", "resetSearchIndex", "category1", "icon", "emojiIndex", "emoji2", "ignore", "_iteratorNormalCompletion1", "_didIteratorError1", "_iteratorError1", "_iterator1", "_step1", "emoticon", "skinIndex", "_iteratorNormalCompletion2", "_didIteratorError2", "_iteratorError2", "_iterator2", "_step2", "native", "skinShortcodes", "t0", "emoticons", "natives", "unshift", "emojis", "aliases", "t3", "originalCategories", "t4", "t8", "parseInt", "t9", "c1", "c2", "version", "$bfca2f6fee0b6f0c$export$bcb25aa587e9cb13", "search", "keywords", "map", "strings", "_param", "split", "string", "flat", "trim", "join", "t10", "skins", "shortcodes", "t11", "$00d81bcb220161ba$export$2e2bcd8739ae039", "$b55119b9446ffa3f$export$75fe5f91d452f94b", "element", "_props", "k", "$b55119b9446ffa3f$export$88c9ddb45cea7241", "propName", "defaults", "getAttribute", "transform", "$00d81bcb220161ba$var$Pool", "$00d81bcb220161ba$var$_search", "maxResults", "pool", "results", "scores", "score", "_args", "word", "words", "t1", "SHORTCODES_REGEX", "$925234ceb1784ee5$export$e772c8ff12451969", "$925234ceb1784ee5$var$_sleep", "frames", "requestAnimationFrame", "$925234ceb1784ee5$export$d10ac59fbe52a745", "_skinIndex", "emojiData", "unified", "$925234ceb1784ee5$export$5ef5574deca44bc0", "nativeString", "$925234ceb1784ee5$var$_getEmojiDataFromNative", "$871cb3bce09a38de$export$2e2bcd8739ae039", "activity", "outline", "$03ffe5e2888517f7$export$34b9dba7ce09269b", "xmlns", "viewBox", "d", "solid", "flags", "foods", "frequent", "nature", "objects", "people", "places", "symbols", "loupe", "delete", "$3ed9a08a810e89ae$export$2e2bcd8739ae039", "matches", "match", "fallback", "emojiSkin", "imageSrc", "spritesheet", "spritesheetSrc", "class", "max<PERSON><PERSON><PERSON>", "size", "maxHeight", "display", "alt", "fontSize", "fontFamily", "backgroundImage", "backgroundSize", "sheet", "cols", "rows", "backgroundPosition", "$ea431ce6c185313b$var$isNativeReflectConstruct", "Date", "$ea431ce6c185313b$var$construct", "Parent1", "args1", "Class1", "Parent", "Class", "$ea431ce6c185313b$export$2e2bcd8739ae039", "$ca772f0f66f8f134$var$wrapNativeSuper", "_cache", "Wrapper", "$ca772f0f66f8f134$export$2e2bcd8739ae039", "$d1b3121271e9311b$export$2e2bcd8739ae039", "WindowHTMLElement1", "HTMLElement", "parent", "_this", "attributeChangedCallback", "attr", "_", "newValue", "Props", "disconnected", "unregister", "$9dec3d2911e366e6$export$2e2bcd8739ae039", "HTMLElement1", "ShadowElement", "styles", "setShadow", "injectStyles", "attachShadow", "mode", "textContent", "shadowRoot", "$698767fb7b753b83$export$2e2bcd8739ae039", "$58897f86f45c5ef0$export$2e2bcd8739ae039", "EmojiElement", "$fe141346c86d79a3$export$b3890eb0ae9dca99", "customElements", "currentComponent", "prevRaf", "afterPaintEffects", "oldBeforeDiff", "oldBeforeRender", "oldAfterDiff", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "flushAfterPaintEffects", "invokeCleanup", "invokeEffect", "hooks", "raf", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "hasErrored", "hook", "comp", "cleanup", "shallow<PERSON>iffers", "PureComponent", "isPureReactComponent", "oldDiffHook", "for", "Forwarded", "oldCatchError", "oldUnmount", "Suspense", "_suspenders", "suspended", "SuspenseList", "_map", "promise", "suspendingVNode", "suspendingComponent", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "removeOriginal", "detachedParent", "originalParent", "wasHydrating", "detachedComponent", "__c", "__v", "__k", "detachedClone", "effect", "list", "revealOrder", "delegated", "unsuspend", "wrappedUnsuspend", "_$n", "REACT_ELEMENT_TYPE", "CAMEL_PROPS", "IS_DOM", "isReactComponent", "oldEventHook", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "persist", "nativeEvent", "classNameDescriptor", "oldVNodeHook", "nonCustomElement", "multiple", "selected", "defaultValue", "className", "$$typeof", "$214fe9f3421232c9$var$THEME_ICONS", "light", "dark", "$214fe9f3421232c9$export$2e2bcd8739ae039", "PureComponent1", "Navigation", "categoryId", "svg", "__html", "selectedCategoryIndex", "position", "dir", "title", "unfocused", "onMouseDown", "preventDefault", "onClick", "_this1", "renderIcon", "opacity", "$e13fe00a6b7624ff$export$221d75b3f55bb0bd", "$837232bba285d30b$export$2e2bcd8739ae039", "PureInlineComponent", "nextProps", "$81e916a19baa5fe7$var$Performance", "$81e916a19baa5fe7$export$2e2bcd8739ae039", "Component1", "Picker", "darkMedia", "showSkins", "<PERSON><PERSON><PERSON>", "closest", "stopImmediatePropagation", "getEmojiByPos", "pos", "input", "searchResults", "afterRender", "grid", "row", "searchInput", "scroll", "scrollTop", "selectionStart", "setsize", "getPerLine", "__categoryId", "__index", "ignoreMouse", "currentTarget", "navigate", "left", "right", "up", "down", "handleEmojiClick", "clearSearch", "unfocusSearch", "focus", "handleSearchInput", "scrollTo", "rect", "getBoundingClientRect", "menu", "_this2", "classList", "remove", "skinToneRadio", "handleBaseClick", "handleBaseKeydown", "observers", "initDynamicPerLine", "visibleRows", "getInitialState", "initTheme", "rtl", "navigation", "skinToneButton", "initGrid", "register", "k1", "nextState", "nextStateTimer", "requiresGridReset", "unobserve", "observeCategories", "observeRows", "handleClickOutside", "observe", "darkMediaCallback", "_except", "except", "observer", "disconnect", "navKey", "addRow", "rowIndex", "rowRef", "index", "posinset", "rows1", "row1", "matchMedia", "media", "calculatePerLine", "ResizeObserver", "_this4", "_this3", "p2", "visibleCategories", "observerOptions", "threshold", "IntersectionObserver", "entries", "dataset", "intersectionRatio", "ratios", "id1", "_iteratorNormalCompletion3", "_didIteratorError3", "_iteratorError3", "_step3", "_iterator3", "isIntersecting", "rootMargin", "_iteratorNormalCompletion5", "_didIteratorError5", "_iteratorError5", "_step5", "_iterator5", "_iteratorNormalCompletion4", "_didIteratorError4", "_iteratorError4", "_step4", "_iterator4", "blur", "_pos", "repeat", "increment", "row2", "keyboard", "scrollRect", "top", "rowTop", "rowBot", "mouseIsIgnored", "ignoreMouseTimer", "tempSkin", "handleCategoryClick", "noSearchResults", "search_no_results_1", "search_no_results_2", "pick", "renderSkinToneButton", "every", "val", "tabindex", "onMouseEnter", "handleEmojiOver", "onMouseLeave", "lineHeight", "borderRadius", "backgroundColor", "renderSkinTone", "placeholder", "handleSearchClick", "onInput", "onKeyDown", "handleSearchKeyDown", "autoComplete", "ii", "_this5", "renderEmojiButton", "add_custom", "hidden", "visibility", "ref1", "_this7", "_emojiIds", "targetRow", "visible", "_this6", "start", "end", "choose", "openSkins", "skinToneButtonRect", "baseRect", "bottom", "role", "defaultChecked", "onChange", "_this8", "handleSkinMouseOver", "code", "handleSkinClick", "lineWidth", "renderPreview", "renderNav", "renderSearch", "renderSearchResults", "renderCategories", "<PERSON><PERSON>kins", "renderLiveRegion", "$fe141346c86d79a3$export$16fa2f45be04daa8", "$d2f87de611a069da$export$2e2bcd8739ae039", "ShadowElement1", "Picker<PERSON><PERSON>", "$25fe16978ac26962$exports", "EmojiMart", "$90bdfdba10cec957$exports"], "version": 3, "file": "browser.js.map"}