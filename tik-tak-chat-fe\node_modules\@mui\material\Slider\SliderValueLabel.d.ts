import * as React from 'react';
import { SliderValueLabelProps } from "./SliderValueLabel.types.js";
/**
 * @ignore - internal component.
 */
declare function SliderValueLabel(props: SliderValueLabelProps): React.ReactElement<{
  className?: string;
  children?: React.ReactNode;
}, string | React.JSXElementConstructor<any>> | null;
declare namespace SliderValueLabel {
  var propTypes: any;
}
export default SliderValueLabel;