{"version": 3, "sources": ["../../react-scrollable-feed/node_modules/tslib/tslib.es6.js", "../../react-scrollable-feed/node_modules/style-inject/dist/style-inject.es.js", "../../react-scrollable-feed/src/index.tsx"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", "import * as React from 'react'\r\nimport { ReactNode } from 'react';\r\nimport styles from './styles.css'\r\n\r\nexport type ScrollableFeedProps = {\r\n  forceScroll?: boolean;\r\n  animateScroll?: (element: HTMLElement, offset: number) => void;\r\n  onScrollComplete?: () => void;\r\n  changeDetectionFilter?: (previousProps: ScrollableFeedComponentProps, newProps: ScrollableFeedComponentProps) => boolean;\r\n  viewableDetectionEpsilon?: number;\r\n  className?: string;\r\n  onScroll?: (isAtBottom: boolean) => void;\r\n}\r\n\r\ntype ScrollableFeedComponentProps = Readonly<{ children?: ReactNode }> & Readonly<ScrollableFeedProps>;\r\n\r\nclass ScrollableFeed extends React.Component<ScrollableFeedProps> {\r\n  private readonly wrapperRef: React.RefObject<HTMLDivElement>;\r\n  private readonly bottomRef: React.RefObject<HTMLDivElement>;\r\n\r\n  constructor(props: ScrollableFeedProps) {\r\n    super(props);\r\n    this.bottomRef = React.createRef();\r\n    this.wrapperRef = React.createRef();\r\n    this.handleScroll = this.handleScroll.bind(this);\r\n  }\r\n\r\n  static defaultProps: ScrollableFeedProps = {\r\n    forceScroll: false,\r\n    animateScroll: (element: HTMLElement, offset: number): void => {\r\n      if (element.scrollBy) {\r\n        element.scrollBy({ top: offset });\r\n      }\r\n      else {\r\n        element.scrollTop = offset;\r\n      }\r\n    },\r\n    onScrollComplete: () => {},\r\n    changeDetectionFilter: () => true,\r\n    viewableDetectionEpsilon: 2,\r\n    onScroll: () => {},\r\n  };\r\n\r\n  getSnapshotBeforeUpdate(): boolean {\r\n    if (this.wrapperRef.current && this.bottomRef.current) {\r\n      const { viewableDetectionEpsilon } = this.props;\r\n      return ScrollableFeed.isViewable(this.wrapperRef.current, this.bottomRef.current, viewableDetectionEpsilon!); //This argument is passed down to componentDidUpdate as 3rd parameter\r\n    }\r\n    return false;\r\n  }\r\n\r\n  componentDidUpdate(previousProps: ScrollableFeedComponentProps, {}: any, snapshot: boolean): void {\r\n    const { forceScroll, changeDetectionFilter } = this.props;\r\n    const isValidChange = changeDetectionFilter!(previousProps, this.props);\r\n    if (isValidChange && (forceScroll || snapshot) && this.bottomRef.current && this.wrapperRef.current) {\r\n      this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);\r\n    }\r\n  }\r\n\r\n  componentDidMount(): void {\r\n    //Scroll to bottom from the start\r\n    if (this.bottomRef.current && this.wrapperRef.current) {\r\n      this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Scrolls a parent element such that the child element will be in view\r\n   * @param parent\r\n   * @param child\r\n   */\r\n  protected scrollParentToChild(parent: HTMLElement, child: HTMLElement): void {\r\n    const { viewableDetectionEpsilon } = this.props;\r\n    if (!ScrollableFeed.isViewable(parent, child, viewableDetectionEpsilon!)) {\r\n      //Source: https://stackoverflow.com/a/45411081/6316091\r\n      const parentRect = parent.getBoundingClientRect();\r\n      const childRect = child.getBoundingClientRect();\r\n\r\n      //Scroll by offset relative to parent\r\n      const scrollOffset = (childRect.top + parent.scrollTop) - parentRect.top;\r\n      const { animateScroll, onScrollComplete } = this.props;\r\n      if (animateScroll) {\r\n        animateScroll(parent, scrollOffset);\r\n        onScrollComplete!();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns whether a child element is visible within a parent element\r\n   *\r\n   * @param parent\r\n   * @param child\r\n   * @param epsilon\r\n   */\r\n  private static isViewable(parent: HTMLElement, child: HTMLElement, epsilon: number): boolean {\r\n    epsilon = epsilon || 0;\r\n\r\n    //Source: https://stackoverflow.com/a/45411081/6316091\r\n    const parentRect = parent.getBoundingClientRect();\r\n    const childRect = child.getBoundingClientRect();\r\n\r\n    const childTopIsViewable = (childRect.top >= parentRect.top);\r\n\r\n    const childOffsetToParentBottom = parentRect.top + parent.clientHeight - childRect.top;\r\n    const childBottomIsViewable = childOffsetToParentBottom + epsilon >= 0;\r\n\r\n    return childTopIsViewable && childBottomIsViewable;\r\n  }\r\n\r\n  /**\r\n   * Fires the onScroll event, sending isAtBottom boolean as its first parameter\r\n   */\r\n  protected handleScroll(): void {\r\n    const { viewableDetectionEpsilon, onScroll } = this.props;\r\n    if (onScroll && this.bottomRef.current && this.wrapperRef.current) {\r\n      const isAtBottom = ScrollableFeed.isViewable(this.wrapperRef.current, this.bottomRef.current, viewableDetectionEpsilon!);\r\n      onScroll(isAtBottom);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Scroll to the bottom\r\n   */\r\n  public scrollToBottom(): void {\r\n    if (this.bottomRef.current && this.wrapperRef.current) {\r\n      this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);\r\n    }\r\n  }\r\n\r\n  render(): React.ReactNode {\r\n    const { children, className } = this.props;\r\n    const joinedClassName = styles.scrollableDiv + (className ? \" \" + className : \"\");\r\n    return (\r\n      <div className={joinedClassName} ref={this.wrapperRef} onScroll={this.handleScroll}>\r\n        {children}\r\n        <div ref={this.bottomRef}></div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\nexport default ScrollableFeed;\r\n"], "mappings": ";;;;;;;;;AAgBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,EAAE,aAAc,SAAS,SAAUA,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;EAAE,KACzE,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;EAAE;AAC5E,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC5B,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;EAAE;AACrC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAE;AACrF;AC3BA,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAK,QAAQ,OAAS,OAAM,CAAA;AAC5B,MAAI,WAAW,IAAI;AAEnB,MAAI,CAAC,OAAO,OAAO,aAAa,aAAa;AAAE;EAAO;AAEtD,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AAEb,MAAI,aAAa,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,OAAO,KAAK,UAAU;IAC9C,OAAW;AACL,WAAK,YAAY,KAAK;IAC5B;EACA,OAAS;AACL,SAAK,YAAY,KAAK;EAC1B;AAEE,MAAI,MAAM,YAAY;AACpB,UAAM,WAAW,UAAU;EAC/B,OAAS;AACL,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;EAClD;AACA;;;;ACTA,IAAA;;EAAA,SAAA,QAAA;AAA6B,cAAAC,iBAAA,MAAA;AAI3B,aAAAA,gBAAY,OAA0B;AAAtC,UAAA,QACE,OAAA,KAAA,MAAM,KAAK,KAAC;AACZ,YAAK,gBAAYC,wBAAe;AAChC,YAAK,iBAAaA,wBAAe;AACjC,YAAK,eAAe,MAAK,aAAa,KAAK,KAAI;;;AAmBjD,IAAAD,gBAAA,UAAA,0BAAA,WAAA;AACE,UAAI,KAAK,WAAW,WAAW,KAAK,UAAU,SAAS;AAC7C,YAAA,2BAA6B,KAAK,MAAK;AAC/C,eAAOA,gBAAe,WAAW,KAAK,WAAW,SAAS,KAAK,UAAU,SAAS,wBAAyB;;AAE7G,aAAO;;AAGT,IAAAA,gBAAA,UAAA,qBAAA,SAAmB,eAA6C,IAAS,UAAiB;AAClF,UAAA,KAAyC,KAAK,OAA5C,cAAW,GAAA,aAAE,wBAAqB,GAAA;AAC1C,UAAM,gBAAgB,sBAAuB,eAAe,KAAK,KAAK;AACtE,UAAI,kBAAkB,eAAe,aAAa,KAAK,UAAU,WAAW,KAAK,WAAW,SAAS;AACnG,aAAK,oBAAoB,KAAK,WAAW,SAAS,KAAK,UAAU,OAAO;;;AAI5E,IAAAA,gBAAA,UAAA,oBAAA,WAAA;AAEE,UAAI,KAAK,UAAU,WAAW,KAAK,WAAW,SAAS;AACrD,aAAK,oBAAoB,KAAK,WAAW,SAAS,KAAK,UAAU,OAAO;;;AASlE,IAAAA,gBAAA,UAAA,sBAAV,SAA8B,QAAqB,OAAkB;AAC3D,UAAA,2BAA6B,KAAK,MAAK;AAC/C,UAAI,CAACA,gBAAe,WAAW,QAAQ,OAAO,wBAAyB,GAAG;AAExE,YAAM,aAAa,OAAO,sBAAqB;AAC/C,YAAM,YAAY,MAAM,sBAAqB;AAG7C,YAAM,eAAgB,UAAU,MAAM,OAAO,YAAa,WAAW;AAC/D,YAAA,KAAsC,KAAK,OAAzC,gBAAa,GAAA,eAAE,mBAAgB,GAAA;AACvC,YAAI,eAAe;AACjB,wBAAc,QAAQ,YAAY;AAClC,2BAAiB;;;;AAYR,IAAAA,gBAAA,aAAf,SAA0B,QAAqB,OAAoB,SAAe;AAChF,gBAAU,WAAW;AAGrB,UAAM,aAAa,OAAO,sBAAqB;AAC/C,UAAM,YAAY,MAAM,sBAAqB;AAE7C,UAAM,qBAAsB,UAAU,OAAO,WAAW;AAExD,UAAM,4BAA4B,WAAW,MAAM,OAAO,eAAe,UAAU;AACnF,UAAM,wBAAwB,4BAA4B,WAAW;AAErE,aAAO,sBAAsB;;AAMrB,IAAAA,gBAAA,UAAA,eAAV,WAAA;AACQ,UAAA,KAAyC,KAAK,OAA5C,2BAAwB,GAAA,0BAAE,WAAQ,GAAA;AAC1C,UAAI,YAAY,KAAK,UAAU,WAAW,KAAK,WAAW,SAAS;AACjE,YAAM,aAAaA,gBAAe,WAAW,KAAK,WAAW,SAAS,KAAK,UAAU,SAAS,wBAAyB;AACvH,iBAAS,UAAU;;;AAOhB,IAAAA,gBAAA,UAAA,iBAAP,WAAA;AACE,UAAI,KAAK,UAAU,WAAW,KAAK,WAAW,SAAS;AACrD,aAAK,oBAAoB,KAAK,WAAW,SAAS,KAAK,UAAU,OAAO;;;AAI5E,IAAAA,gBAAA,UAAA,SAAA,WAAA;AACQ,UAAA,KAA0B,KAAK,OAA7B,WAAQ,GAAA,UAAE,YAAS,GAAA;AAC3B,UAAM,kBAAkB,OAAO,iBAAiB,YAAY,MAAM,YAAY;AAC9E,iBACEE;QAAAA;QAAAA,EAAK,WAAW,iBAAiB,KAAK,KAAK,YAAY,UAAU,KAAK,aAAY;QAC/E;YACDA,4BAAAA,OAAAA,EAAK,KAAK,KAAK,UAAS,CAAA;MAAQ;;AA7G/B,IAAAF,gBAAA,eAAoC;MACzC,aAAa;MACb,eAAe,SAAC,SAAsB,QAAc;AAClD,YAAI,QAAQ,UAAU;AACpB,kBAAQ,SAAS,EAAE,KAAK,OAAM,CAAE;eAE7B;AACH,kBAAQ,YAAY;;;MAGxB,kBAAkB,WAAA;MAAA;MAClB,uBAAuB,WAAA;AAAM,eAAA;MAAI;MACjC,0BAA0B;MAC1B,UAAU,WAAA;MAAA;;AAoGd,WAAAA;IA5H6BG,sBAAe;;;", "names": ["d", "b", "ScrollableFeed", "React.createRef", "React.createElement", "React.Component"]}