{"name": "tic-tak-talk", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.2", "socket.io": "^4.6.1"}, "devDependencies": {"nodemon": "^2.0.21"}}