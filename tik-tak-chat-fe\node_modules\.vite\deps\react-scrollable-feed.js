import {
  require_react
} from "./chunk-QP4RLAFO.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/react-scrollable-feed/dist/index.es.js
var import_react = __toESM(require_react());
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2) if (b2.hasOwnProperty(p)) d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
function styleInject(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z = ".styles_scrollable-div__prSCv {\r\n  max-height: inherit;\r\n  height: inherit;\r\n  overflow-y: auto;\r\n}\r\n";
var styles = { "scrollable-div": "styles_scrollable-div__prSCv", "scrollableDiv": "styles_scrollable-div__prSCv" };
styleInject(css_248z);
var ScrollableFeed = (
  /** @class */
  function(_super) {
    __extends(ScrollableFeed2, _super);
    function ScrollableFeed2(props) {
      var _this = _super.call(this, props) || this;
      _this.bottomRef = (0, import_react.createRef)();
      _this.wrapperRef = (0, import_react.createRef)();
      _this.handleScroll = _this.handleScroll.bind(_this);
      return _this;
    }
    ScrollableFeed2.prototype.getSnapshotBeforeUpdate = function() {
      if (this.wrapperRef.current && this.bottomRef.current) {
        var viewableDetectionEpsilon = this.props.viewableDetectionEpsilon;
        return ScrollableFeed2.isViewable(this.wrapperRef.current, this.bottomRef.current, viewableDetectionEpsilon);
      }
      return false;
    };
    ScrollableFeed2.prototype.componentDidUpdate = function(previousProps, _a, snapshot) {
      var _b = this.props, forceScroll = _b.forceScroll, changeDetectionFilter = _b.changeDetectionFilter;
      var isValidChange = changeDetectionFilter(previousProps, this.props);
      if (isValidChange && (forceScroll || snapshot) && this.bottomRef.current && this.wrapperRef.current) {
        this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);
      }
    };
    ScrollableFeed2.prototype.componentDidMount = function() {
      if (this.bottomRef.current && this.wrapperRef.current) {
        this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);
      }
    };
    ScrollableFeed2.prototype.scrollParentToChild = function(parent, child) {
      var viewableDetectionEpsilon = this.props.viewableDetectionEpsilon;
      if (!ScrollableFeed2.isViewable(parent, child, viewableDetectionEpsilon)) {
        var parentRect = parent.getBoundingClientRect();
        var childRect = child.getBoundingClientRect();
        var scrollOffset = childRect.top + parent.scrollTop - parentRect.top;
        var _a = this.props, animateScroll = _a.animateScroll, onScrollComplete = _a.onScrollComplete;
        if (animateScroll) {
          animateScroll(parent, scrollOffset);
          onScrollComplete();
        }
      }
    };
    ScrollableFeed2.isViewable = function(parent, child, epsilon) {
      epsilon = epsilon || 0;
      var parentRect = parent.getBoundingClientRect();
      var childRect = child.getBoundingClientRect();
      var childTopIsViewable = childRect.top >= parentRect.top;
      var childOffsetToParentBottom = parentRect.top + parent.clientHeight - childRect.top;
      var childBottomIsViewable = childOffsetToParentBottom + epsilon >= 0;
      return childTopIsViewable && childBottomIsViewable;
    };
    ScrollableFeed2.prototype.handleScroll = function() {
      var _a = this.props, viewableDetectionEpsilon = _a.viewableDetectionEpsilon, onScroll = _a.onScroll;
      if (onScroll && this.bottomRef.current && this.wrapperRef.current) {
        var isAtBottom = ScrollableFeed2.isViewable(this.wrapperRef.current, this.bottomRef.current, viewableDetectionEpsilon);
        onScroll(isAtBottom);
      }
    };
    ScrollableFeed2.prototype.scrollToBottom = function() {
      if (this.bottomRef.current && this.wrapperRef.current) {
        this.scrollParentToChild(this.wrapperRef.current, this.bottomRef.current);
      }
    };
    ScrollableFeed2.prototype.render = function() {
      var _a = this.props, children = _a.children, className = _a.className;
      var joinedClassName = styles.scrollableDiv + (className ? " " + className : "");
      return (0, import_react.createElement)(
        "div",
        { className: joinedClassName, ref: this.wrapperRef, onScroll: this.handleScroll },
        children,
        (0, import_react.createElement)("div", { ref: this.bottomRef })
      );
    };
    ScrollableFeed2.defaultProps = {
      forceScroll: false,
      animateScroll: function(element, offset) {
        if (element.scrollBy) {
          element.scrollBy({ top: offset });
        } else {
          element.scrollTop = offset;
        }
      },
      onScrollComplete: function() {
      },
      changeDetectionFilter: function() {
        return true;
      },
      viewableDetectionEpsilon: 2,
      onScroll: function() {
      }
    };
    return ScrollableFeed2;
  }(import_react.Component)
);
var index_es_default = ScrollableFeed;
export {
  index_es_default as default
};
/*! Bundled license information:

react-scrollable-feed/dist/index.es.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
  
  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
  
  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** *)
*/
//# sourceMappingURL=react-scrollable-feed.js.map
