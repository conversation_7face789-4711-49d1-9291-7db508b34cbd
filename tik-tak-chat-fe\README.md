# Chat app realtime
link demo: https://tiktak-gamma.vercel.app/
* react
* chakra-ui
* socket.io
* deploy by vercel
* responsive (moblie, ipad, ipad pro, small tablet, pc)

## functions:
* chat real-time
* box chat group (update name, add, remove user), one-one
* login, logout, register access
* notifycation message, delete message
* edit password, avatar, name
* search user

1. login, register page
![image](https://user-images.githubusercontent.com/101584126/228760721-4fc03163-3e8d-4515-aff3-77efc9938384.png)
![image](https://user-images.githubusercontent.com/101584126/228760928-de6060aa-a904-443d-92cd-c94d03338e66.png)

2. chat page
![image](https://user-images.githubusercontent.com/101584126/228761813-91dfcee3-0cd5-47ea-87f5-4d51d78c2384.png)
![image](https://user-images.githubusercontent.com/101584126/228761822-08372ca4-0503-4871-9c6f-05776f820d5f.png)
![image](https://user-images.githubusercontent.com/101584126/228761899-7cd22266-04f9-48ed-ad30-9d7ef85e9516.png)




