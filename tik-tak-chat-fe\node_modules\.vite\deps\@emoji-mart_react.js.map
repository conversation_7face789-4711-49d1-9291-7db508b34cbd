{"version": 3, "sources": ["../../emoji-mart/dist/packages/emoji-mart/src/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Picker/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Picker/Picker.tsx", "../../emoji-mart/dist/node_modules/@swc/helpers/src/_define_property.mjs", "../../emoji-mart/dist/node_modules/preact/jsx-runtime/dist/jsxRuntime.module.js", "../../emoji-mart/dist/node_modules/preact/jsx-runtime/src/index.js", "../../emoji-mart/dist/node_modules/preact/dist/preact.module.js", "../../emoji-mart/dist/node_modules/preact/src/constants.js", "../../emoji-mart/dist/node_modules/preact/src/util.js", "../../emoji-mart/dist/node_modules/preact/src/options.js", "../../emoji-mart/dist/node_modules/preact/src/create-element.js", "../../emoji-mart/dist/node_modules/preact/src/component.js", "../../emoji-mart/dist/node_modules/preact/src/create-context.js", "../../emoji-mart/dist/node_modules/preact/src/diff/children.js", "../../emoji-mart/dist/node_modules/preact/src/diff/props.js", "../../emoji-mart/dist/node_modules/preact/src/diff/index.js", "../../emoji-mart/dist/node_modules/preact/src/render.js", "../../emoji-mart/dist/node_modules/preact/src/clone-element.js", "../../emoji-mart/dist/node_modules/preact/src/diff/catch-error.js", "../../emoji-mart/dist/packages/emoji-mart/src/utils.ts", "../../emoji-mart/dist/packages/emoji-mart/src/helpers/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/helpers/store.ts", "../../emoji-mart/dist/packages/emoji-mart/src/helpers/native-support.ts", "../../emoji-mart/dist/packages/emoji-mart/src/helpers/frequently-used.ts", "../../emoji-mart/dist/packages/emoji-mart/src/helpers/search-index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/config.ts", "../../emoji-mart/dist/packages/emoji-mart-data/i18n/en.json", "../../emoji-mart/dist/packages/emoji-mart/src/components/Picker/PickerProps.ts", "../../emoji-mart/dist/packages/emoji-mart/src/icons.tsx", "../../emoji-mart/dist/packages/emoji-mart/src/components/Emoji/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Emoji/Emoji.tsx", "../../emoji-mart/dist/packages/emoji-mart/src/components/Emoji/EmojiElement.jsx", "../../emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/HTMLElement.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/HTMLElement/ShadowElement.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Emoji/EmojiProps.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Navigation/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Navigation/Navigation.tsx", "../../emoji-mart/dist/node_modules/preact/compat/dist/compat.module.js", "../../emoji-mart/dist/node_modules/preact/compat/src/util.js", "../../emoji-mart/dist/node_modules/preact/compat/src/PureComponent.js", "../../emoji-mart/dist/node_modules/preact/compat/src/memo.js", "../../emoji-mart/dist/node_modules/preact/compat/src/forwardRef.js", "../../emoji-mart/dist/node_modules/preact/compat/src/Children.js", "../../emoji-mart/dist/node_modules/preact/compat/src/suspense.js", "../../emoji-mart/dist/node_modules/preact/compat/src/suspense-list.js", "../../emoji-mart/dist/node_modules/preact/compat/src/portals.js", "../../emoji-mart/dist/node_modules/preact/compat/src/render.js", "../../emoji-mart/dist/node_modules/preact/compat/src/index.js", "../../emoji-mart/dist/node_modules/preact/hooks/dist/hooks.module.js", "../../emoji-mart/dist/node_modules/preact/hooks/src/index.js", "../../emoji-mart/dist/packages/emoji-mart/src/components/HOCs/index.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/HOCs/PureInlineComponent.ts", "../../emoji-mart/dist/packages/emoji-mart/src/components/Picker/PickerElement.tsx", "../../emoji-mart/dist/node_modules/@parcel/runtime-js/lib/bundles/runtime-1b9572f9f2947a02.js", "../../@emoji-mart/react/dist/packages/emoji-mart-react/react.tsx"], "sourcesContent": ["export { PickerElement as Picker } from './components/Picker'\nexport { EmojiElement as Emoji } from './components/Emoji'\n\nexport { FrequentlyUsed, SafeFlags, SearchIndex, Store } from './helpers'\n\nexport { init, Data, I18n } from './config'\n\nexport { getEmojiDataFromNative } from './utils'\n", "// @ts-nocheck\nexport { default as Picker } from './Picker'\nexport { default as PickerElement } from './PickerElement'\nexport { default as PickerStyles } from 'bundle-text:./PickerStyles.scss'\n", "// @ts-nocheck\nimport { Component, createRef } from 'preact'\n\nimport { deepEqual, sleep, getEmojiData } from '../../utils'\nimport { Data, I18n, init } from '../../config'\nimport { SearchIndex, Store, FrequentlyUsed } from '../../helpers'\nimport Icons from '../../icons'\n\nimport { Emoji } from '../Emoji'\nimport { Navigation } from '../Navigation'\nimport { PureInlineComponent } from '../HOCs'\n\nconst Performance = {\n  rowsPerRender: 10,\n}\n\nexport default class Picker extends Component {\n  constructor(props) {\n    super()\n\n    this.observers = []\n\n    this.state = {\n      pos: [-1, -1],\n      perLine: this.initDynamicPerLine(props),\n      visibleRows: { 0: true },\n      ...this.getInitialState(props),\n    }\n  }\n\n  getInitialState(props = this.props) {\n    return {\n      skin: Store.get('skin') || props.skin,\n      theme: this.initTheme(props.theme),\n    }\n  }\n\n  componentWillMount() {\n    this.dir = I18n.rtl ? 'rtl' : 'ltr'\n    this.refs = {\n      menu: createRef(),\n      navigation: createRef(),\n      scroll: createRef(),\n      search: createRef(),\n      searchInput: createRef(),\n      skinToneButton: createRef(),\n      skinToneRadio: createRef(),\n    }\n\n    this.initGrid()\n\n    if (\n      this.props.stickySearch == false &&\n      this.props.searchPosition == 'sticky'\n    ) {\n      console.warn(\n        '[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`.',\n      )\n\n      this.props.searchPosition = 'static'\n    }\n  }\n\n  componentDidMount() {\n    this.register()\n\n    this.shadowRoot = this.base.parentNode\n\n    if (this.props.autoFocus) {\n      const { searchInput } = this.refs\n      if (searchInput.current) {\n        searchInput.current.focus()\n      }\n    }\n  }\n\n  componentWillReceiveProps(nextProps) {\n    this.nextState || (this.nextState = {})\n\n    for (const k in nextProps) {\n      this.nextState[k] = nextProps[k]\n    }\n\n    clearTimeout(this.nextStateTimer)\n    this.nextStateTimer = setTimeout(() => {\n      let requiresGridReset = false\n\n      for (const k in this.nextState) {\n        this.props[k] = this.nextState[k]\n\n        if (k === 'custom' || k === 'categories') {\n          requiresGridReset = true\n        }\n      }\n\n      delete this.nextState\n      const nextState = this.getInitialState()\n\n      if (requiresGridReset) {\n        return this.reset(nextState)\n      }\n\n      this.setState(nextState)\n    })\n  }\n\n  componentWillUnmount() {\n    this.unregister()\n  }\n\n  async reset(nextState = {}) {\n    await init(this.props)\n\n    this.initGrid()\n    this.unobserve()\n\n    this.setState(nextState, () => {\n      this.observeCategories()\n      this.observeRows()\n    })\n  }\n\n  register() {\n    document.addEventListener('click', this.handleClickOutside)\n    this.observe()\n  }\n\n  unregister() {\n    document.removeEventListener('click', this.handleClickOutside)\n    this.darkMedia?.removeEventListener('change', this.darkMediaCallback)\n    this.unobserve()\n  }\n\n  observe() {\n    this.observeCategories()\n    this.observeRows()\n  }\n\n  unobserve({ except = [] } = {}) {\n    if (!Array.isArray(except)) {\n      except = [except]\n    }\n\n    for (const observer of this.observers) {\n      if (except.includes(observer)) continue\n      observer.disconnect()\n    }\n\n    this.observers = [].concat(except)\n  }\n\n  initGrid() {\n    const { categories } = Data\n\n    this.refs.categories = new Map()\n\n    const navKey = Data.categories.map((category) => category.id).join(',')\n    if (this.navKey && this.navKey != navKey) {\n      this.refs.scroll.current && (this.refs.scroll.current.scrollTop = 0)\n    }\n    this.navKey = navKey\n\n    this.grid = []\n    this.grid.setsize = 0\n\n    const addRow = (rows, category) => {\n      const row = []\n      row.__categoryId = category.id\n      row.__index = rows.length\n      this.grid.push(row)\n\n      const rowIndex = this.grid.length - 1\n      const rowRef = rowIndex % Performance.rowsPerRender ? {} : createRef()\n      rowRef.index = rowIndex\n      rowRef.posinset = this.grid.setsize + 1\n      rows.push(rowRef)\n\n      return row\n    }\n\n    for (let category of categories) {\n      const rows = []\n      let row = addRow(rows, category)\n\n      for (let emoji of category.emojis) {\n        if (row.length == this.getPerLine()) {\n          row = addRow(rows, category)\n        }\n\n        this.grid.setsize += 1\n        row.push(emoji)\n      }\n\n      this.refs.categories.set(category.id, { root: createRef(), rows })\n    }\n  }\n\n  darkMediaCallback = () => {\n    if (this.props.theme != 'auto') return\n    this.setState({ theme: this.darkMedia.matches ? 'dark' : 'light' })\n  }\n\n  initTheme(theme) {\n    if (theme != 'auto') return theme\n\n    if (!this.darkMedia) {\n      this.darkMedia = matchMedia('(prefers-color-scheme: dark)')\n      if (this.darkMedia.media.match(/^not/)) return 'light'\n\n      this.darkMedia.addEventListener('change', this.darkMediaCallback)\n    }\n\n    return this.darkMedia.matches ? 'dark' : 'light'\n  }\n\n  handleClickOutside = (e) => {\n    const { element } = this.props\n\n    if (e.target != element) {\n      if (this.state.showSkins) {\n        this.closeSkins()\n      }\n\n      if (this.props.onClickOutside) {\n        this.props.onClickOutside(e)\n      }\n    }\n  }\n\n  handleBaseClick = (e) => {\n    if (!this.state.showSkins) return\n    if (!e.target.closest('.menu')) {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  handleBaseKeydown = (e) => {\n    if (!this.state.showSkins) return\n    if (e.key == 'Escape') {\n      e.preventDefault()\n      e.stopImmediatePropagation()\n\n      this.closeSkins()\n    }\n  }\n\n  initDynamicPerLine(props = this.props) {\n    if (!props.dynamicWidth) return\n    const { element, emojiButtonSize } = props\n\n    const calculatePerLine = () => {\n      const { width } = element.getBoundingClientRect()\n      return Math.floor(width / emojiButtonSize)\n    }\n\n    const observer = new ResizeObserver(() => {\n      this.unobserve({ except: observer })\n      this.setState({ perLine: calculatePerLine() }, () => {\n        this.initGrid()\n        this.forceUpdate(() => {\n          this.observeCategories()\n          this.observeRows()\n        })\n      })\n    })\n\n    observer.observe(element)\n    this.observers.push(observer)\n\n    return calculatePerLine()\n  }\n\n  getPerLine() {\n    return this.state.perLine || this.props.perLine\n  }\n\n  getEmojiByPos([p1, p2]) {\n    const grid = this.state.searchResults || this.grid\n    const emoji = grid[p1] && grid[p1][p2]\n\n    if (!emoji) return\n    return SearchIndex.get(emoji)\n  }\n\n  observeCategories() {\n    const navigation = this.refs.navigation.current\n    if (!navigation) return\n\n    const visibleCategories = new Map()\n    const setFocusedCategory = (categoryId) => {\n      if (categoryId != navigation.state.categoryId) {\n        navigation.setState({ categoryId })\n      }\n    }\n\n    const observerOptions = {\n      root: this.refs.scroll.current,\n      threshold: [0.0, 1.0],\n    }\n\n    const observer = new IntersectionObserver((entries) => {\n      for (const entry of entries) {\n        const id = entry.target.dataset.id\n        visibleCategories.set(id, entry.intersectionRatio)\n      }\n\n      const ratios = [...visibleCategories]\n      for (const [id, ratio] of ratios) {\n        if (ratio) {\n          setFocusedCategory(id)\n          break\n        }\n      }\n    }, observerOptions)\n\n    for (const { root } of this.refs.categories.values()) {\n      observer.observe(root.current)\n    }\n\n    this.observers.push(observer)\n  }\n\n  observeRows() {\n    const visibleRows = { ...this.state.visibleRows }\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        for (const entry of entries) {\n          const index = parseInt(entry.target.dataset.index)\n\n          if (entry.isIntersecting) {\n            visibleRows[index] = true\n          } else {\n            delete visibleRows[index]\n          }\n        }\n\n        this.setState({ visibleRows })\n      },\n      {\n        root: this.refs.scroll.current,\n        rootMargin: `${\n          this.props.emojiButtonSize * (Performance.rowsPerRender + 5)\n        }px 0px ${this.props.emojiButtonSize * Performance.rowsPerRender}px`,\n      },\n    )\n\n    for (const { rows } of this.refs.categories.values()) {\n      for (const row of rows) {\n        if (row.current) {\n          observer.observe(row.current)\n        }\n      }\n    }\n\n    this.observers.push(observer)\n  }\n\n  preventDefault(e) {\n    e.preventDefault()\n  }\n\n  handleSearchClick = () => {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    if (!emoji) return\n\n    this.setState({ pos: [-1, -1] })\n  }\n\n  handleSearchInput = async () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    const { value } = input\n    const searchResults = await SearchIndex.search(value)\n    const afterRender = () => {\n      if (!this.refs.scroll.current) return\n      this.refs.scroll.current.scrollTop = 0\n    }\n\n    if (!searchResults) {\n      return this.setState({ searchResults, pos: [-1, -1] }, afterRender)\n    }\n\n    const pos = input.selectionStart == input.value.length ? [0, 0] : [-1, -1]\n    const grid = []\n    grid.setsize = searchResults.length\n    let row = null\n\n    for (let emoji of searchResults) {\n      if (!grid.length || row.length == this.getPerLine()) {\n        row = []\n        row.__categoryId = 'search'\n        row.__index = grid.length\n        grid.push(row)\n      }\n\n      row.push(emoji)\n    }\n\n    this.ignoreMouse()\n    this.setState({ searchResults: grid, pos }, afterRender)\n  }\n\n  handleSearchKeyDown = (e) => {\n    // const specialKey = e.altKey || e.ctrlKey || e.metaKey\n    const input = e.currentTarget\n    e.stopImmediatePropagation()\n\n    switch (e.key) {\n      case 'ArrowLeft':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, left: true })\n        break\n\n      case 'ArrowRight':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, right: true })\n        break\n\n      case 'ArrowUp':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, up: true })\n        break\n\n      case 'ArrowDown':\n        // if (specialKey) return\n        // e.preventDefault()\n        this.navigate({ e, input, down: true })\n        break\n\n      case 'Enter':\n        e.preventDefault()\n        this.handleEmojiClick({ e, pos: this.state.pos })\n        break\n\n      case 'Escape':\n        e.preventDefault()\n        if (this.state.searchResults) {\n          this.clearSearch()\n        } else {\n          this.unfocusSearch()\n        }\n        break\n\n      default:\n        break\n    }\n  }\n\n  clearSearch = () => {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.value = ''\n    input.focus()\n\n    this.handleSearchInput()\n  }\n\n  unfocusSearch() {\n    const input = this.refs.searchInput.current\n    if (!input) return\n\n    input.blur()\n  }\n\n  navigate({ e, input, left, right, up, down }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    let [p1, p2] = this.state.pos\n\n    const pos = (() => {\n      if (p1 == 0) {\n        if (p2 == 0 && !e.repeat && (left || up)) {\n          return null\n        }\n      }\n\n      if (p1 == -1) {\n        if (\n          !e.repeat &&\n          (right || down) &&\n          input.selectionStart == input.value.length\n        ) {\n          return [0, 0]\n        }\n\n        return null\n      }\n\n      if (left || right) {\n        let row = grid[p1]\n        const increment = left ? -1 : 1\n\n        p2 += increment\n        if (!row[p2]) {\n          p1 += increment\n          row = grid[p1]\n\n          if (!row) {\n            p1 = left ? 0 : grid.length - 1\n            p2 = left ? 0 : grid[p1].length - 1\n\n            return [p1, p2]\n          }\n\n          p2 = left ? row.length - 1 : 0\n        }\n\n        return [p1, p2]\n      }\n\n      if (up || down) {\n        p1 += up ? -1 : 1\n        const row = grid[p1]\n\n        if (!row) {\n          p1 = up ? 0 : grid.length - 1\n          p2 = up ? 0 : grid[p1].length - 1\n\n          return [p1, p2]\n        }\n\n        if (!row[p2]) {\n          p2 = row.length - 1\n        }\n\n        return [p1, p2]\n      }\n    })()\n\n    if (pos) {\n      e.preventDefault()\n    } else {\n      if (this.state.pos[0] > -1) {\n        this.setState({ pos: [-1, -1] })\n      }\n\n      return\n    }\n\n    this.setState({ pos, keyboard: true }, () => {\n      this.scrollTo({ row: pos[0] })\n    })\n  }\n\n  scrollTo({ categoryId, row }) {\n    const grid = this.state.searchResults || this.grid\n    if (!grid.length) return\n\n    const scroll = this.refs.scroll.current\n    const scrollRect = scroll.getBoundingClientRect()\n\n    let scrollTop = 0\n\n    if (row >= 0) {\n      categoryId = grid[row].__categoryId\n    }\n\n    if (categoryId) {\n      const ref =\n        this.refs[categoryId] || this.refs.categories.get(categoryId).root\n      const categoryRect = ref.current.getBoundingClientRect()\n\n      scrollTop = categoryRect.top - (scrollRect.top - scroll.scrollTop) + 1\n    }\n\n    if (row >= 0) {\n      if (!row) {\n        scrollTop = 0\n      } else {\n        const rowIndex = grid[row].__index\n        const rowTop = scrollTop + rowIndex * this.props.emojiButtonSize\n        const rowBot =\n          rowTop +\n          this.props.emojiButtonSize +\n          this.props.emojiButtonSize * 0.88\n\n        if (rowTop < scroll.scrollTop) {\n          scrollTop = rowTop\n        } else if (rowBot > scroll.scrollTop + scrollRect.height) {\n          scrollTop = rowBot - scrollRect.height\n        } else {\n          return\n        }\n      }\n    }\n\n    this.ignoreMouse()\n    scroll.scrollTop = scrollTop\n  }\n\n  ignoreMouse() {\n    this.mouseIsIgnored = true\n    clearTimeout(this.ignoreMouseTimer)\n    this.ignoreMouseTimer = setTimeout(() => {\n      delete this.mouseIsIgnored\n    }, 100)\n  }\n\n  handleCategoryClick = ({ category, i }) => {\n    this.scrollTo(i == 0 ? { row: -1 } : { categoryId: category.id })\n  }\n\n  handleEmojiOver(pos) {\n    if (this.mouseIsIgnored || this.state.showSkins) return\n    this.setState({ pos: pos || [-1, -1], keyboard: false })\n  }\n\n  handleEmojiClick({ e, emoji, pos }) {\n    if (!this.props.onEmojiSelect) return\n\n    if (!emoji && pos) {\n      emoji = this.getEmojiByPos(pos)\n    }\n\n    if (emoji) {\n      const emojiData = getEmojiData(emoji, { skinIndex: this.state.skin - 1 })\n\n      if (this.props.maxFrequentRows) {\n        FrequentlyUsed.add(emojiData, this.props)\n      }\n\n      this.props.onEmojiSelect(emojiData, e)\n    }\n  }\n\n  openSkins = (e) => {\n    const { currentTarget } = e\n    const rect = currentTarget.getBoundingClientRect()\n\n    this.setState({ showSkins: rect }, async () => {\n      // Firefox requires 2 frames for the transition to consistenly work\n      await sleep(2)\n\n      const menu = this.refs.menu.current\n      if (!menu) return\n\n      menu.classList.remove('hidden')\n      this.refs.skinToneRadio.current.focus()\n\n      this.base.addEventListener('click', this.handleBaseClick, true)\n      this.base.addEventListener('keydown', this.handleBaseKeydown, true)\n    })\n  }\n\n  closeSkins() {\n    if (!this.state.showSkins) return\n    this.setState({ showSkins: null, tempSkin: null })\n\n    this.base.removeEventListener('click', this.handleBaseClick)\n    this.base.removeEventListener('keydown', this.handleBaseKeydown)\n  }\n\n  handleSkinMouseOver(tempSkin) {\n    this.setState({ tempSkin })\n  }\n\n  handleSkinClick(skin) {\n    this.ignoreMouse()\n    this.closeSkins()\n\n    this.setState({ skin, tempSkin: null })\n    Store.set('skin', skin)\n  }\n\n  renderNav() {\n    return (\n      <Navigation\n        key={this.navKey}\n        ref={this.refs.navigation}\n        icons={this.props.icons}\n        theme={this.state.theme}\n        dir={this.dir}\n        unfocused={!!this.state.searchResults}\n        position={this.props.navPosition}\n        onClick={this.handleCategoryClick}\n      />\n    )\n  }\n\n  renderPreview() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const noSearchResults =\n      this.state.searchResults && !this.state.searchResults.length\n\n    return (\n      <div\n        id=\"preview\"\n        class=\"flex flex-middle\"\n        dir={this.dir}\n        data-position={this.props.previewPosition}\n      >\n        <div class=\"flex flex-middle flex-grow\">\n          <div\n            class=\"flex flex-auto flex-middle flex-center\"\n            style={{\n              height: this.props.emojiButtonSize,\n              fontSize: this.props.emojiButtonSize,\n            }}\n          >\n            <Emoji\n              emoji={emoji}\n              id={\n                noSearchResults\n                  ? this.props.noResultsEmoji || 'cry'\n                  : this.props.previewEmoji ||\n                    (this.props.previewPosition == 'top'\n                      ? 'point_down'\n                      : 'point_up')\n              }\n              set={this.props.set}\n              size={this.props.emojiButtonSize}\n              skin={this.state.tempSkin || this.state.skin}\n              spritesheet={true}\n              getSpritesheetURL={this.props.getSpritesheetURL}\n            />\n          </div>\n\n          <div class={`margin-${this.dir[0]}`}>\n            {emoji || noSearchResults ? (\n              <div class={`padding-${this.dir[2]} align-${this.dir[0]}`}>\n                <div class=\"preview-title ellipsis\">\n                  {emoji ? emoji.name : I18n.search_no_results_1}\n                </div>\n                <div class=\"preview-subtitle ellipsis color-c\">\n                  {emoji ? emoji.skins[0].shortcodes : I18n.search_no_results_2}\n                </div>\n              </div>\n            ) : (\n              <div class=\"preview-placeholder color-c\">{I18n.pick}</div>\n            )}\n          </div>\n        </div>\n\n        {!emoji &&\n          this.props.skinTonePosition == 'preview' &&\n          this.renderSkinToneButton()}\n      </div>\n    )\n  }\n\n  renderEmojiButton(emoji, { pos, posinset, grid }) {\n    const size = this.props.emojiButtonSize\n    const skin = this.state.tempSkin || this.state.skin\n    const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n    const native = emojiSkin.native\n    const selected = deepEqual(this.state.pos, pos)\n    const key = pos.concat(emoji.id).join('')\n\n    return (\n      <PureInlineComponent key={key} {...{ selected, skin, size }}>\n        <button\n          aria-label={native}\n          aria-selected={selected || undefined}\n          aria-posinset={posinset}\n          aria-setsize={grid.setsize}\n          data-keyboard={this.state.keyboard}\n          title={this.props.previewPosition == 'none' ? emoji.name : undefined}\n          type=\"button\"\n          class=\"flex flex-center flex-middle\"\n          tabindex=\"-1\"\n          onClick={(e) => this.handleEmojiClick({ e, emoji })}\n          onMouseEnter={() => this.handleEmojiOver(pos)}\n          onMouseLeave={() => this.handleEmojiOver()}\n          style={{\n            width: this.props.emojiButtonSize,\n            height: this.props.emojiButtonSize,\n            fontSize: this.props.emojiSize,\n            lineHeight: 0,\n          }}\n        >\n          <div\n            aria-hidden=\"true\"\n            class=\"background\"\n            style={{\n              borderRadius: this.props.emojiButtonRadius,\n              backgroundColor: this.props.emojiButtonColors\n                ? this.props.emojiButtonColors[\n                    (posinset - 1) % this.props.emojiButtonColors.length\n                  ]\n                : undefined,\n            }}\n          ></div>\n          <Emoji\n            emoji={emoji}\n            set={this.props.set}\n            size={this.props.emojiSize}\n            skin={skin}\n            spritesheet={true}\n            getSpritesheetURL={this.props.getSpritesheetURL}\n          />\n        </button>\n      </PureInlineComponent>\n    )\n  }\n\n  renderSearch() {\n    const renderSkinTone =\n      this.props.previewPosition == 'none' ||\n      this.props.skinTonePosition == 'search'\n\n    return (\n      <div>\n        <div class=\"spacer\"></div>\n        <div class=\"flex flex-middle\">\n          <div class=\"search relative flex-grow\">\n            <input\n              type=\"search\"\n              ref={this.refs.searchInput}\n              placeholder={I18n.search}\n              onClick={this.handleSearchClick}\n              onInput={this.handleSearchInput}\n              onKeyDown={this.handleSearchKeyDown}\n              autoComplete=\"off\"\n            ></input>\n            <span class=\"icon loupe flex\">{Icons.search.loupe}</span>\n            {this.state.searchResults && (\n              <button\n                title=\"Clear\"\n                aria-label=\"Clear\"\n                type=\"button\"\n                class=\"icon delete flex\"\n                onClick={this.clearSearch}\n                onMouseDown={this.preventDefault}\n              >\n                {Icons.search.delete}\n              </button>\n            )}\n          </div>\n\n          {renderSkinTone && this.renderSkinToneButton()}\n        </div>\n      </div>\n    )\n  }\n\n  renderSearchResults() {\n    const { searchResults } = this.state\n    if (!searchResults) return null\n\n    return (\n      <div class=\"category\" ref={this.refs.search}>\n        <div class={`sticky padding-small align-${this.dir[0]}`}>\n          {I18n.categories.search}\n        </div>\n        <div>\n          {!searchResults.length ? (\n            <div class={`padding-small align-${this.dir[0]}`}>\n              {this.props.onAddCustomEmoji && (\n                <a onClick={this.props.onAddCustomEmoji}>{I18n.add_custom}</a>\n              )}\n            </div>\n          ) : (\n            searchResults.map((row, i) => {\n              return (\n                <div class=\"flex\">\n                  {row.map((emoji, ii) => {\n                    return this.renderEmojiButton(emoji, {\n                      pos: [i, ii],\n                      posinset: i * this.props.perLine + ii + 1,\n                      grid: searchResults,\n                    })\n                  })}\n                </div>\n              )\n            })\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  renderCategories() {\n    const { categories } = Data\n    const hidden = !!this.state.searchResults\n    const perLine = this.getPerLine()\n\n    return (\n      <div\n        style={{\n          visibility: hidden ? 'hidden' : undefined,\n          display: hidden ? 'none' : undefined,\n          height: '100%',\n        }}\n      >\n        {categories.map((category) => {\n          const { root, rows } = this.refs.categories.get(category.id)\n\n          return (\n            <div\n              data-id={category.target ? category.target.id : category.id}\n              class=\"category\"\n              ref={root}\n            >\n              <div class={`sticky padding-small align-${this.dir[0]}`}>\n                {category.name || I18n.categories[category.id]}\n              </div>\n              <div\n                class=\"relative\"\n                style={{\n                  height: rows.length * this.props.emojiButtonSize,\n                }}\n              >\n                {rows.map((row, i) => {\n                  const targetRow =\n                    row.index - (row.index % Performance.rowsPerRender)\n                  const visible = this.state.visibleRows[targetRow]\n                  const ref = 'current' in row ? row : undefined\n\n                  if (!visible && !ref) {\n                    return null\n                  }\n\n                  const start = i * perLine\n                  const end = start + perLine\n                  const emojiIds = category.emojis.slice(start, end)\n\n                  if (emojiIds.length < perLine) {\n                    emojiIds.push(...new Array(perLine - emojiIds.length))\n                  }\n\n                  return (\n                    <div\n                      key={row.index}\n                      data-index={row.index}\n                      ref={ref}\n                      class=\"flex row\"\n                      style={{ top: i * this.props.emojiButtonSize }}\n                    >\n                      {visible &&\n                        emojiIds.map((emojiId, ii) => {\n                          if (!emojiId) {\n                            return (\n                              <div\n                                style={{\n                                  width: this.props.emojiButtonSize,\n                                  height: this.props.emojiButtonSize,\n                                }}\n                              ></div>\n                            )\n                          }\n\n                          const emoji = SearchIndex.get(emojiId)\n\n                          return this.renderEmojiButton(emoji, {\n                            pos: [row.index, ii],\n                            posinset: row.posinset + ii,\n                            grid: this.grid,\n                          })\n                        })}\n                    </div>\n                  )\n                })}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  renderSkinToneButton() {\n    if (this.props.skinTonePosition == 'none') {\n      return null\n    }\n\n    return (\n      <div\n        class=\"flex flex-auto flex-center flex-middle\"\n        style={{\n          position: 'relative',\n          width: this.props.emojiButtonSize,\n          height: this.props.emojiButtonSize,\n        }}\n      >\n        <button\n          type=\"button\"\n          ref={this.refs.skinToneButton}\n          class=\"skin-tone-button flex flex-auto flex-center flex-middle\"\n          aria-selected={this.state.showSkins ? '' : undefined}\n          aria-label={I18n.skins.choose}\n          title={I18n.skins.choose}\n          onClick={this.openSkins}\n          style={{\n            width: this.props.emojiSize,\n            height: this.props.emojiSize,\n          }}\n        >\n          <span class={`skin-tone skin-tone-${this.state.skin}`}></span>\n        </button>\n      </div>\n    )\n  }\n\n  renderLiveRegion() {\n    const emoji = this.getEmojiByPos(this.state.pos)\n    const contents = emoji ? emoji.name : ''\n\n    return (\n      <div aria-live=\"polite\" class=\"sr-only\">\n        {contents}\n      </div>\n    )\n  }\n\n  renderSkins() {\n    const skinToneButton = this.refs.skinToneButton.current\n    const skinToneButtonRect = skinToneButton.getBoundingClientRect()\n    const baseRect = this.base.getBoundingClientRect()\n\n    const position = {}\n\n    if (this.dir == 'ltr') {\n      position.right = baseRect.right - skinToneButtonRect.right - 3\n    } else {\n      position.left = skinToneButtonRect.left - baseRect.left - 3\n    }\n\n    if (\n      this.props.previewPosition == 'bottom' &&\n      this.props.skinTonePosition == 'preview'\n    ) {\n      position.bottom = baseRect.bottom - skinToneButtonRect.top + 6\n    } else {\n      position.top = skinToneButtonRect.bottom - baseRect.top + 3\n      position.bottom = 'auto'\n    }\n\n    return (\n      <div\n        ref={this.refs.menu}\n        role=\"radiogroup\"\n        dir={this.dir}\n        aria-label={I18n.skins.choose}\n        class=\"menu hidden\"\n        data-position={position.top ? 'top' : 'bottom'}\n        style={position}\n      >\n        {[...Array(6).keys()].map((i) => {\n          const skin = i + 1\n          const checked = this.state.skin == skin\n\n          return (\n            <div>\n              <input\n                type=\"radio\"\n                name=\"skin-tone\"\n                value={skin}\n                aria-label={I18n.skins[skin]}\n                ref={checked ? this.refs.skinToneRadio : null}\n                defaultChecked={checked}\n                onChange={() => this.handleSkinMouseOver(skin)}\n                onKeyDown={(e) => {\n                  if (\n                    e.code == 'Enter' ||\n                    e.code == 'Space' ||\n                    e.code == 'Tab'\n                  ) {\n                    e.preventDefault()\n                    this.handleSkinClick(skin)\n                  }\n                }}\n              ></input>\n\n              <button\n                aria-hidden=\"true\"\n                tabindex=\"-1\"\n                onClick={() => this.handleSkinClick(skin)}\n                onMouseEnter={() => this.handleSkinMouseOver(skin)}\n                onMouseLeave={() => this.handleSkinMouseOver()}\n                class=\"option flex flex-grow flex-middle\"\n              >\n                <span class={`skin-tone skin-tone-${skin}`}></span>\n                <span class=\"margin-small-lr\">{I18n.skins[skin]}</span>\n              </button>\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  render() {\n    const lineWidth = this.props.perLine * this.props.emojiButtonSize\n\n    return (\n      <section\n        id=\"root\"\n        class=\"flex flex-column\"\n        dir={this.dir}\n        style={{\n          width: this.props.dynamicWidth\n            ? '100%'\n            : `calc(${lineWidth}px + (var(--padding) + var(--sidebar-width)))`,\n        }}\n        data-emoji-set={this.props.set}\n        data-theme={this.state.theme}\n        data-menu={this.state.showSkins ? '' : undefined}\n      >\n        {this.props.previewPosition == 'top' && this.renderPreview()}\n        {this.props.navPosition == 'top' && this.renderNav()}\n        {this.props.searchPosition == 'sticky' && (\n          <div class=\"padding-lr\">{this.renderSearch()}</div>\n        )}\n\n        <div ref={this.refs.scroll} class=\"scroll flex-grow padding-lr\">\n          <div\n            style={{\n              width: this.props.dynamicWidth ? '100%' : lineWidth,\n              height: '100%',\n            }}\n          >\n            {this.props.searchPosition == 'static' && this.renderSearch()}\n            {this.renderSearchResults()}\n            {this.renderCategories()}\n          </div>\n        </div>\n\n        {this.props.navPosition == 'bottom' && this.renderNav()}\n        {this.props.previewPosition == 'bottom' && this.renderPreview()}\n        {this.state.showSkins && this.renderSkins()}\n        {this.renderLiveRegion()}\n      </section>\n    )\n  }\n}\n", "export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n", "import{options as r,Fragment as _}from\"preact\";export{Fragment}from\"preact\";var o=0;function e(_,e,n,t,f){var l,s,u={};for(s in e)\"ref\"==s?l=e[s]:u[s]=e[s];var a={type:_,props:u,key:n,ref:l,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--o,__source:t,__self:f};if(\"function\"==typeof _&&(l=_.defaultProps))for(s in l)void 0===u[s]&&(u[s]=l[s]);return r.vnode&&r.vnode(a),a}export{e as jsx,e as jsxs,e as jsxDEV};\n//# sourceMappingURL=jsxRuntime.module.js.map\n", "import { options, Fragment } from 'preact';\r\n\r\n/** @typedef {import('preact').VNode} VNode */\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * @fileoverview\r\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\r\n * - jsx(type, props, key)\r\n * - jsxs(type, props, key)\r\n * - jsxDEV(type, props, key, __source, __self)\r\n *\r\n * The implementation of createVNode here is optimized for performance.\r\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\r\n */\r\n\r\n/**\r\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\r\n * @param {VNode['type']} type\r\n * @param {VNode['props']} props\r\n * @param {VNode['key']} [key]\r\n * @param {string} [__source]\r\n * @param {string} [__self]\r\n */\r\nfunction createVNode(type, props, key, __source, __self) {\r\n\t// We'll want to preserve `ref` in props to get rid of the need for\r\n\t// forwardRef components in the future, but that should happen via\r\n\t// a separate PR.\r\n\tlet normalizedProps = {},\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'ref') {\r\n\t\t\tref = props[i];\r\n\t\t} else {\r\n\t\t\tnormalizedProps[i] = props[i];\r\n\t\t}\r\n\t}\r\n\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops: normalizedProps,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: --vnodeId,\r\n\t\t__source,\r\n\t\t__self\r\n\t};\r\n\r\n\t// If a Component VNode, check for and apply defaultProps.\r\n\t// Note: `type` is often a String, and can be `undefined` in development.\r\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\r\n\t\tfor (i in ref)\r\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\r\n\t\t\t\tnormalizedProps[i] = ref[i];\r\n\t\t\t}\r\n\t}\r\n\r\n\tif (options.vnode) options.vnode(vnode);\r\n\treturn vnode;\r\n}\r\n\r\nexport {\r\n\tcreateVNode as jsx,\r\n\tcreateVNode as jsxs,\r\n\tcreateVNode as jsxDEV,\r\n\tFragment\r\n};\r\n", "var n,l,u,i,t,r,o,f,e={},c=[],s=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function a(n,l){for(var u in l)n[u]=l[u];return n}function h(n){var l=n.parentNode;l&&l.removeChild(n)}function v(l,u,i){var t,r,o,f={};for(o in u)\"key\"==o?t=u[o]:\"ref\"==o?r=u[o]:f[o]=u[o];if(arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),\"function\"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)void 0===f[o]&&(f[o]=l.defaultProps[o]);return y(l,f,t,r,null)}function y(n,i,t,r,o){var f={type:n,props:i,key:t,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++u:o};return null==o&&null!=l.vnode&&l.vnode(f),f}function p(){return{current:null}}function d(n){return n.children}function _(n,l){this.props=n,this.context=l}function k(n,l){if(null==l)return n.__?k(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return\"function\"==typeof n.type?k(n):null}function b(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return b(n)}}function m(n){(!n.__d&&(n.__d=!0)&&t.push(n)&&!g.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||r)(g)}function g(){for(var n;g.__r=t.length;)n=t.sort(function(n,l){return n.__v.__b-l.__v.__b}),t=[],n.some(function(n){var l,u,i,t,r,o;n.__d&&(r=(t=(l=n).__v).__e,(o=l.__P)&&(u=[],(i=a({},t)).__v=t.__v+1,j(o,t,i,l.__n,void 0!==o.ownerSVGElement,null!=t.__h?[r]:null,u,null==r?k(t):r,t.__h),z(u,t),t.__e!=r&&b(t)))})}function w(n,l,u,i,t,r,o,f,s,a){var h,v,p,_,b,m,g,w=i&&i.__k||c,A=w.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(_=u.__k[h]=null==(_=l[h])||\"boolean\"==typeof _?null:\"string\"==typeof _||\"number\"==typeof _||\"bigint\"==typeof _?y(null,_,null,null,_):Array.isArray(_)?y(d,{children:_},null,null,null):_.__b>0?y(_.type,_.props,_.key,null,_.__v):_)){if(_.__=u,_.__b=u.__b+1,null===(p=w[h])||p&&_.key==p.key&&_.type===p.type)w[h]=void 0;else for(v=0;v<A;v++){if((p=w[v])&&_.key==p.key&&_.type===p.type){w[v]=void 0;break}p=null}j(n,_,p=p||e,t,r,o,f,s,a),b=_.__e,(v=_.ref)&&p.ref!=v&&(g||(g=[]),p.ref&&g.push(p.ref,null,_),g.push(v,_.__c||b,_)),null!=b?(null==m&&(m=b),\"function\"==typeof _.type&&_.__k===p.__k?_.__d=s=x(_,s,n):s=P(n,_,p,w,b,s),\"function\"==typeof u.type&&(u.__d=s)):s&&p.__e==s&&s.parentNode!=n&&(s=k(p))}for(u.__e=m,h=A;h--;)null!=w[h]&&(\"function\"==typeof u.type&&null!=w[h].__e&&w[h].__e==u.__d&&(u.__d=k(i,h+1)),N(w[h],w[h]));if(g)for(h=0;h<g.length;h++)M(g[h],g[++h],g[++h])}function x(n,l,u){for(var i,t=n.__k,r=0;t&&r<t.length;r++)(i=t[r])&&(i.__=n,l=\"function\"==typeof i.type?x(i,l,u):P(u,i,i,t,i.__e,l));return l}function A(n,l){return l=l||[],null==n||\"boolean\"==typeof n||(Array.isArray(n)?n.some(function(n){A(n,l)}):l.push(n)),l}function P(n,l,u,i,t,r){var o,f,e;if(void 0!==l.__d)o=l.__d,l.__d=void 0;else if(null==u||t!=r||null==t.parentNode)n:if(null==r||r.parentNode!==n)n.appendChild(t),o=null;else{for(f=r,e=0;(f=f.nextSibling)&&e<i.length;e+=2)if(f==t)break n;n.insertBefore(t,r),o=r}return void 0!==o?o:t.nextSibling}function C(n,l,u,i,t){var r;for(r in u)\"children\"===r||\"key\"===r||r in l||H(n,r,null,u[r],i);for(r in l)t&&\"function\"!=typeof l[r]||\"children\"===r||\"key\"===r||\"value\"===r||\"checked\"===r||u[r]===l[r]||H(n,r,l[r],u[r],i)}function $(n,l,u){\"-\"===l[0]?n.setProperty(l,u):n[l]=null==u?\"\":\"number\"!=typeof u||s.test(l)?u:u+\"px\"}function H(n,l,u,i,t){var r;n:if(\"style\"===l)if(\"string\"==typeof u)n.style.cssText=u;else{if(\"string\"==typeof i&&(n.style.cssText=i=\"\"),i)for(l in i)u&&l in u||$(n.style,l,\"\");if(u)for(l in u)i&&u[l]===i[l]||$(n.style,l,u[l])}else if(\"o\"===l[0]&&\"n\"===l[1])r=l!==(l=l.replace(/Capture$/,\"\")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?i||n.addEventListener(l,r?T:I,r):n.removeEventListener(l,r?T:I,r);else if(\"dangerouslySetInnerHTML\"!==l){if(t)l=l.replace(/xlink[H:h]/,\"h\").replace(/sName$/,\"s\");else if(\"href\"!==l&&\"list\"!==l&&\"form\"!==l&&\"tabIndex\"!==l&&\"download\"!==l&&l in n)try{n[l]=null==u?\"\":u;break n}catch(n){}\"function\"==typeof u||(null!=u&&(!1!==u||\"a\"===l[0]&&\"r\"===l[1])?n.setAttribute(l,u):n.removeAttribute(l))}}function I(n){this.l[n.type+!1](l.event?l.event(n):n)}function T(n){this.l[n.type+!0](l.event?l.event(n):n)}function j(n,u,i,t,r,o,f,e,c){var s,h,v,y,p,k,b,m,g,x,A,P=u.type;if(void 0!==u.constructor)return null;null!=i.__h&&(c=i.__h,e=u.__e=i.__e,u.__h=null,o=[e]),(s=l.__b)&&s(u);try{n:if(\"function\"==typeof P){if(m=u.props,g=(s=P.contextType)&&t[s.__c],x=s?g?g.props.value:s.__:t,i.__c?b=(h=u.__c=i.__c).__=h.__E:(\"prototype\"in P&&P.prototype.render?u.__c=h=new P(m,x):(u.__c=h=new _(m,x),h.constructor=P,h.render=O),g&&g.sub(h),h.props=m,h.state||(h.state={}),h.context=x,h.__n=t,v=h.__d=!0,h.__h=[]),null==h.__s&&(h.__s=h.state),null!=P.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=a({},h.__s)),a(h.__s,P.getDerivedStateFromProps(m,h.__s))),y=h.props,p=h.state,v)null==P.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(null==P.getDerivedStateFromProps&&m!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(m,x),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(m,h.__s,x)||u.__v===i.__v){h.props=m,h.state=h.__s,u.__v!==i.__v&&(h.__d=!1),h.__v=u,u.__e=i.__e,u.__k=i.__k,u.__k.forEach(function(n){n&&(n.__=u)}),h.__h.length&&f.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(m,h.__s,x),null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,p,k)})}h.context=x,h.props=m,h.state=h.__s,(s=l.__r)&&s(u),h.__d=!1,h.__v=u,h.__P=n,s=h.render(h.props,h.state,h.context),h.state=h.__s,null!=h.getChildContext&&(t=a(a({},t),h.getChildContext())),v||null==h.getSnapshotBeforeUpdate||(k=h.getSnapshotBeforeUpdate(y,p)),A=null!=s&&s.type===d&&null==s.key?s.props.children:s,w(n,Array.isArray(A)?A:[A],u,i,t,r,o,f,e,c),h.base=u.__e,u.__h=null,h.__h.length&&f.push(h),b&&(h.__E=h.__=null),h.__e=!1}else null==o&&u.__v===i.__v?(u.__k=i.__k,u.__e=i.__e):u.__e=L(i.__e,u,i,t,r,o,f,c);(s=l.diffed)&&s(u)}catch(n){u.__v=null,(c||null!=o)&&(u.__e=e,u.__h=!!c,o[o.indexOf(e)]=null),l.__e(n,u,i)}}function z(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function L(l,u,i,t,r,o,f,c){var s,a,v,y=i.props,p=u.props,d=u.type,_=0;if(\"svg\"===d&&(r=!0),null!=o)for(;_<o.length;_++)if((s=o[_])&&\"setAttribute\"in s==!!d&&(d?s.localName===d:3===s.nodeType)){l=s,o[_]=null;break}if(null==l){if(null===d)return document.createTextNode(p);l=r?document.createElementNS(\"http://www.w3.org/2000/svg\",d):document.createElement(d,p.is&&p),o=null,c=!1}if(null===d)y===p||c&&l.data===p||(l.data=p);else{if(o=o&&n.call(l.childNodes),a=(y=i.props||e).dangerouslySetInnerHTML,v=p.dangerouslySetInnerHTML,!c){if(null!=o)for(y={},_=0;_<l.attributes.length;_++)y[l.attributes[_].name]=l.attributes[_].value;(v||a)&&(v&&(a&&v.__html==a.__html||v.__html===l.innerHTML)||(l.innerHTML=v&&v.__html||\"\"))}if(C(l,p,y,r,c),v)u.__k=[];else if(_=u.props.children,w(l,Array.isArray(_)?_:[_],u,i,t,r&&\"foreignObject\"!==d,o,f,o?o[0]:i.__k&&k(i,0),c),null!=o)for(_=o.length;_--;)null!=o[_]&&h(o[_]);c||(\"value\"in p&&void 0!==(_=p.value)&&(_!==y.value||_!==l.value||\"progress\"===d&&!_)&&H(l,\"value\",_,y.value,!1),\"checked\"in p&&void 0!==(_=p.checked)&&_!==l.checked&&H(l,\"checked\",_,y.checked,!1))}return l}function M(n,u,i){try{\"function\"==typeof n?n(u):n.current=u}catch(n){l.__e(n,i)}}function N(n,u,i){var t,r;if(l.unmount&&l.unmount(n),(t=n.ref)&&(t.current&&t.current!==n.__e||M(t,null,u)),null!=(t=n.__c)){if(t.componentWillUnmount)try{t.componentWillUnmount()}catch(n){l.__e(n,u)}t.base=t.__P=null}if(t=n.__k)for(r=0;r<t.length;r++)t[r]&&N(t[r],u,\"function\"!=typeof n.type);i||null==n.__e||h(n.__e),n.__e=n.__d=void 0}function O(n,l,u){return this.constructor(n,u)}function S(u,i,t){var r,o,f;l.__&&l.__(u,i),o=(r=\"function\"==typeof t)?null:t&&t.__k||i.__k,f=[],j(i,u=(!r&&t||i).__k=v(d,null,[u]),o||e,e,void 0!==i.ownerSVGElement,!r&&t?[t]:o?null:i.firstChild?n.call(i.childNodes):null,f,!r&&t?t:o?o.__e:i.firstChild,r),z(f,u)}function q(n,l){S(n,l,q)}function B(l,u,i){var t,r,o,f=a({},l.props);for(o in u)\"key\"==o?t=u[o]:\"ref\"==o?r=u[o]:f[o]=u[o];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),y(l.type,f,t||l.key,r||l.ref,null)}function D(n,l){var u={__c:l=\"__cC\"+f++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,i;return this.getChildContext||(u=[],(i={})[l]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(m)},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u}n=c.slice,l={__e:function(n,l){for(var u,i,t;l=l.__;)if((u=l.__c)&&!u.__)try{if((i=u.constructor)&&null!=i.getDerivedStateFromError&&(u.setState(i.getDerivedStateFromError(n)),t=u.__d),null!=u.componentDidCatch&&(u.componentDidCatch(n),t=u.__d),t)return u.__E=u}catch(l){n=l}throw n}},u=0,i=function(n){return null!=n&&void 0===n.constructor},_.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=a({},this.state),\"function\"==typeof n&&(n=n(a({},u),this.props)),n&&a(u,n),null!=n&&this.__v&&(l&&this.__h.push(l),m(this))},_.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),m(this))},_.prototype.render=d,t=[],r=\"function\"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,g.__r=0,f=0;export{S as render,q as hydrate,v as createElement,v as h,d as Fragment,p as createRef,i as isValidElement,_ as Component,B as cloneElement,D as createContext,A as toChildArray,l as options};\n//# sourceMappingURL=preact.module.js.map\n", "export const EMPTY_OBJ = {};\r\nexport const EMPTY_ARR = [];\r\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\r\n", "import { EMPTY_ARR } from \"./constants\";\r\n\r\n/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\t// @ts-ignore We change the type of `obj` to be `O & P`\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Remove a child node from its parent if attached. This is a workaround for\r\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\r\n * is smaller than including a dedicated polyfill.\r\n * @param {Node} node The node to remove\r\n */\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n\r\nexport const slice = EMPTY_ARR.slice;\r\n", "import { _catchError } from './diff/catch-error';\r\n\r\n/**\r\n * The `option` object can potentially contain callback functions\r\n * that are called during various stages of our renderer. This is the\r\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\r\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\r\n * for a full list of available option hooks (most editors/IDEs allow you to\r\n * ctrl+click or cmd+click on mac the type definition below).\r\n * @type {import('./internal').Options}\r\n */\r\nconst options = {\r\n\t_catchError\r\n};\r\n\r\nexport default options;\r\n", "import { slice } from './util';\r\nimport options from './options';\r\n\r\nlet vnodeId = 0;\r\n\r\n/**\r\n * Create an virtual node (used for JSX)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * constructor for this virtual node\r\n * @param {object | null | undefined} [props] The properties of the virtual node\r\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createElement(type, props, children) {\r\n\tlet normalizedProps = {},\r\n\t\tkey,\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'key') key = props[i];\r\n\t\telse if (i == 'ref') ref = props[i];\r\n\t\telse normalizedProps[i] = props[i];\r\n\t}\r\n\r\n\tif (arguments.length > 2) {\r\n\t\tnormalizedProps.children =\r\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\r\n\t}\r\n\r\n\t// If a Component VNode, check for and apply defaultProps\r\n\t// Note: type may be undefined in development, must never error here.\r\n\tif (typeof type == 'function' && type.defaultProps != null) {\r\n\t\tfor (i in type.defaultProps) {\r\n\t\t\tif (normalizedProps[i] === undefined) {\r\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn createVNode(type, normalizedProps, key, ref, null);\r\n}\r\n\r\n/**\r\n * Create a VNode (used internally by Preact)\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\r\n * Constructor for this virtual node\r\n * @param {object | string | number | null} props The properties of this virtual node.\r\n * If this virtual node represents a text node, this is the text of the node (string or number).\r\n * @param {string | number | null} key The key for this virtual node, used when\r\n * diffing it against its children\r\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\r\n * receive a reference to its created child\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function createVNode(type, props, key, ref, original) {\r\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\r\n\t// Do not inline into createElement and coerceToVNode!\r\n\tconst vnode = {\r\n\t\ttype,\r\n\t\tprops,\r\n\t\tkey,\r\n\t\tref,\r\n\t\t_children: null,\r\n\t\t_parent: null,\r\n\t\t_depth: 0,\r\n\t\t_dom: null,\r\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\r\n\t\t// be set to dom.nextSibling which can return `null` and it is important\r\n\t\t// to be able to distinguish between an uninitialized _nextDom and\r\n\t\t// a _nextDom that has been set to `null`\r\n\t\t_nextDom: undefined,\r\n\t\t_component: null,\r\n\t\t_hydrating: null,\r\n\t\tconstructor: undefined,\r\n\t\t_original: original == null ? ++vnodeId : original\r\n\t};\r\n\r\n\t// Only invoke the vnode hook if this was *not* a direct copy:\r\n\tif (original == null && options.vnode != null) options.vnode(vnode);\r\n\r\n\treturn vnode;\r\n}\r\n\r\nexport function createRef() {\r\n\treturn { current: null };\r\n}\r\n\r\nexport function Fragment(props) {\r\n\treturn props.children;\r\n}\r\n\r\n/**\r\n * Check if a the argument is a valid Preact VNode.\r\n * @param {*} vnode\r\n * @returns {vnode is import('./internal').VNode}\r\n */\r\nexport const isValidElement = vnode =>\r\n\tvnode != null && vnode.constructor === undefined;\r\n", "import { assign } from './util';\r\nimport { diff, commitRoot } from './diff/index';\r\nimport options from './options';\r\nimport { Fragment } from './create-element';\r\n\r\n/**\r\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\r\n * trigger rendering\r\n * @param {object} props The initial component props\r\n * @param {object} context The initial context from parent components'\r\n * getChildContext\r\n */\r\nexport function Component(props, context) {\r\n\tthis.props = props;\r\n\tthis.context = context;\r\n}\r\n\r\n/**\r\n * Update component state and schedule a re-render.\r\n * @this {import('./internal').Component}\r\n * @param {object | ((s: object, p: object) => object)} update A hash of state\r\n * properties to update with new values or a function that given the current\r\n * state and props returns a new partial state\r\n * @param {() => void} [callback] A function to be called once component state is\r\n * updated\r\n */\r\nComponent.prototype.setState = function(update, callback) {\r\n\t// only clone state when copying to nextState the first time.\r\n\tlet s;\r\n\tif (this._nextState != null && this._nextState !== this.state) {\r\n\t\ts = this._nextState;\r\n\t} else {\r\n\t\ts = this._nextState = assign({}, this.state);\r\n\t}\r\n\r\n\tif (typeof update == 'function') {\r\n\t\t// Some libraries like `immer` mark the current state as readonly,\r\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\r\n\t\tupdate = update(assign({}, s), this.props);\r\n\t}\r\n\r\n\tif (update) {\r\n\t\tassign(s, update);\r\n\t}\r\n\r\n\t// Skip update if updater function returned null\r\n\tif (update == null) return;\r\n\r\n\tif (this._vnode) {\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Immediately perform a synchronous re-render of the component\r\n * @this {import('./internal').Component}\r\n * @param {() => void} [callback] A function to be called after component is\r\n * re-rendered\r\n */\r\nComponent.prototype.forceUpdate = function(callback) {\r\n\tif (this._vnode) {\r\n\t\t// Set render mode so that we can differentiate where the render request\r\n\t\t// is coming from. We need this because forceUpdate should never call\r\n\t\t// shouldComponentUpdate\r\n\t\tthis._force = true;\r\n\t\tif (callback) this._renderCallbacks.push(callback);\r\n\t\tenqueueRender(this);\r\n\t}\r\n};\r\n\r\n/**\r\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\r\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\r\n * @param {object} props Props (eg: JSX attributes) received from parent\r\n * element/component\r\n * @param {object} state The component's current state\r\n * @param {object} context Context object, as returned by the nearest\r\n * ancestor's `getChildContext()`\r\n * @returns {import('./index').ComponentChildren | void}\r\n */\r\nComponent.prototype.render = Fragment;\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n * @param {number | null} [childIndex]\r\n */\r\nexport function getDomSibling(vnode, childIndex) {\r\n\tif (childIndex == null) {\r\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\r\n\t\treturn vnode._parent\r\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\r\n\t\t\t: null;\r\n\t}\r\n\r\n\tlet sibling;\r\n\tfor (; childIndex < vnode._children.length; childIndex++) {\r\n\t\tsibling = vnode._children[childIndex];\r\n\r\n\t\tif (sibling != null && sibling._dom != null) {\r\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\r\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\r\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\r\n\t\t\treturn sibling._dom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we get here, we have not found a DOM node in this vnode's children.\r\n\t// We must resume from this vnode's sibling (in it's parent _children array)\r\n\t// Only climb up and search the parent if we aren't searching through a DOM\r\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\r\n\t// the search)\r\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\r\n}\r\n\r\n/**\r\n * Trigger in-place re-rendering of a component.\r\n * @param {import('./internal').Component} component The component to rerender\r\n */\r\nfunction renderComponent(component) {\r\n\tlet vnode = component._vnode,\r\n\t\toldDom = vnode._dom,\r\n\t\tparentDom = component._parentDom;\r\n\r\n\tif (parentDom) {\r\n\t\tlet commitQueue = [];\r\n\t\tconst oldVNode = assign({}, vnode);\r\n\t\toldVNode._original = vnode._original + 1;\r\n\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tvnode,\r\n\t\t\toldVNode,\r\n\t\t\tcomponent._globalContext,\r\n\t\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t\tvnode._hydrating != null ? [oldDom] : null,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom,\r\n\t\t\tvnode._hydrating\r\n\t\t);\r\n\t\tcommitRoot(commitQueue, vnode);\r\n\r\n\t\tif (vnode._dom != oldDom) {\r\n\t\t\tupdateParentDomPointers(vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').VNode} vnode\r\n */\r\nfunction updateParentDomPointers(vnode) {\r\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\r\n\t\tvnode._dom = vnode._component.base = null;\r\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\r\n\t\t\tlet child = vnode._children[i];\r\n\t\t\tif (child != null && child._dom != null) {\r\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn updateParentDomPointers(vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * The render queue\r\n * @type {Array<import('./internal').Component>}\r\n */\r\nlet rerenderQueue = [];\r\n\r\n/**\r\n * Asynchronously schedule a callback\r\n * @type {(cb: () => void) => void}\r\n */\r\n/* istanbul ignore next */\r\n// Note the following line isn't tree-shaken by rollup cuz of rollup/rollup#2566\r\nconst defer =\r\n\ttypeof Promise == 'function'\r\n\t\t? Promise.prototype.then.bind(Promise.resolve())\r\n\t\t: setTimeout;\r\n\r\n/*\r\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\r\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\r\n * do, and when their effects will be applied. See the links below for some further reading on designing\r\n * asynchronous APIs.\r\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\r\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\r\n */\r\n\r\nlet prevDebounce;\r\n\r\n/**\r\n * Enqueue a rerender of a component\r\n * @param {import('./internal').Component} c The component to rerender\r\n */\r\nexport function enqueueRender(c) {\r\n\tif (\r\n\t\t(!c._dirty &&\r\n\t\t\t(c._dirty = true) &&\r\n\t\t\trerenderQueue.push(c) &&\r\n\t\t\t!process._rerenderCount++) ||\r\n\t\tprevDebounce !== options.debounceRendering\r\n\t) {\r\n\t\tprevDebounce = options.debounceRendering;\r\n\t\t(prevDebounce || defer)(process);\r\n\t}\r\n}\r\n\r\n/** Flush the render queue by rerendering all queued components */\r\nfunction process() {\r\n\tlet queue;\r\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\r\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\t\trerenderQueue = [];\r\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\r\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\r\n\t\tqueue.some(c => {\r\n\t\t\tif (c._dirty) renderComponent(c);\r\n\t\t});\r\n\t}\r\n}\r\nprocess._rerenderCount = 0;\r\n", "import { enqueueRender } from './component';\r\n\r\nexport let i = 0;\r\n\r\nexport function createContext(defaultValue, contextId) {\r\n\tcontextId = '__cC' + i++;\r\n\r\n\tconst context = {\r\n\t\t_id: contextId,\r\n\t\t_defaultValue: defaultValue,\r\n\t\t/** @type {import('./internal').FunctionComponent} */\r\n\t\tConsumer(props, contextValue) {\r\n\t\t\t// return props.children(\r\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\r\n\t\t\t// );\r\n\t\t\treturn props.children(contextValue);\r\n\t\t},\r\n\t\t/** @type {import('./internal').FunctionComponent} */\r\n\t\tProvider(props) {\r\n\t\t\tif (!this.getChildContext) {\r\n\t\t\t\tlet subs = [];\r\n\t\t\t\tlet ctx = {};\r\n\t\t\t\tctx[contextId] = this;\r\n\r\n\t\t\t\tthis.getChildContext = () => ctx;\r\n\r\n\t\t\t\tthis.shouldComponentUpdate = function(_props) {\r\n\t\t\t\t\tif (this.props.value !== _props.value) {\r\n\t\t\t\t\t\t// I think the forced value propagation here was only needed when `options.debounceRendering` was being bypassed:\r\n\t\t\t\t\t\t// https://github.com/preactjs/preact/commit/4d339fb803bea09e9f198abf38ca1bf8ea4b7771#diff-54682ce380935a717e41b8bfc54737f6R358\r\n\t\t\t\t\t\t// In those cases though, even with the value corrected, we're double-rendering all nodes.\r\n\t\t\t\t\t\t// It might be better to just tell folks not to use force-sync mode.\r\n\t\t\t\t\t\t// Currently, using `useContext()` in a class component will overwrite its `this.context` value.\r\n\t\t\t\t\t\t// subs.some(c => {\r\n\t\t\t\t\t\t// \tc.context = _props.value;\r\n\t\t\t\t\t\t// \tenqueueRender(c);\r\n\t\t\t\t\t\t// });\r\n\r\n\t\t\t\t\t\t// subs.some(c => {\r\n\t\t\t\t\t\t// \tc.context[contextId] = _props.value;\r\n\t\t\t\t\t\t// \tenqueueRender(c);\r\n\t\t\t\t\t\t// });\r\n\t\t\t\t\t\tsubs.some(enqueueRender);\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\tthis.sub = c => {\r\n\t\t\t\t\tsubs.push(c);\r\n\t\t\t\t\tlet old = c.componentWillUnmount;\r\n\t\t\t\t\tc.componentWillUnmount = () => {\r\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\r\n\t\t\t\t\t\tif (old) old.call(c);\r\n\t\t\t\t\t};\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\treturn props.children;\r\n\t\t}\r\n\t};\r\n\r\n\t// Devtools needs access to the context object when it\r\n\t// encounters a Provider. This is necessary to support\r\n\t// setting `displayName` on the context object instead\r\n\t// of on the component itself. See:\r\n\t// https://reactjs.org/docs/context.html#contextdisplayname\r\n\r\n\treturn (context.Provider._contextRef = context.Consumer.contextType = context);\r\n}\r\n", "import { diff, unmount, applyRef } from './index';\r\nimport { createVNode, Fragment } from '../create-element';\r\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\r\nimport { getDomSibling } from '../component';\r\n\r\n/**\r\n * Diff the children of a virtual node\r\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\r\n * children are being diffed\r\n * @param {import('../internal').ComponentChildren[]} renderResult\r\n * @param {import('../internal').VNode} newParentVNode The new virtual\r\n * node whose children should be diff'ed against oldParentVNode\r\n * @param {import('../internal').VNode} oldParentVNode The old virtual\r\n * node whose children should be diff'ed against newParentVNode\r\n * @param {object} globalContext The current context object - modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n */\r\nexport function diffChildren(\r\n\tparentDom,\r\n\trenderResult,\r\n\tnewParentVNode,\r\n\toldParentVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\r\n\r\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\r\n\t// as EMPTY_OBJ._children should be `undefined`.\r\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\r\n\r\n\tlet oldChildrenLength = oldChildren.length;\r\n\r\n\tnewParentVNode._children = [];\r\n\tfor (i = 0; i < renderResult.length; i++) {\r\n\t\tchildVNode = renderResult[i];\r\n\r\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\r\n\t\t\tchildVNode = newParentVNode._children[i] = null;\r\n\t\t}\r\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\r\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\r\n\t\t// it's own DOM & etc. pointers\r\n\t\telse if (\r\n\t\t\ttypeof childVNode == 'string' ||\r\n\t\t\ttypeof childVNode == 'number' ||\r\n\t\t\t// eslint-disable-next-line valid-typeof\r\n\t\t\ttypeof childVNode == 'bigint'\r\n\t\t) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode\r\n\t\t\t);\r\n\t\t} else if (Array.isArray(childVNode)) {\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tFragment,\r\n\t\t\t\t{ children: childVNode },\r\n\t\t\t\tnull,\r\n\t\t\t\tnull,\r\n\t\t\t\tnull\r\n\t\t\t);\r\n\t\t} else if (childVNode._depth > 0) {\r\n\t\t\t// VNode is already in use, clone it. This can happen in the following\r\n\t\t\t// scenario:\r\n\t\t\t//   const reuse = <div />\r\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\r\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\r\n\t\t\t\tchildVNode.type,\r\n\t\t\t\tchildVNode.props,\r\n\t\t\t\tchildVNode.key,\r\n\t\t\t\tnull,\r\n\t\t\t\tchildVNode._original\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\r\n\t\t}\r\n\r\n\t\t// Terser removes the `continue` here and wraps the loop body\r\n\t\t// in a `if (childVNode) { ... } condition\r\n\t\tif (childVNode == null) {\r\n\t\t\tcontinue;\r\n\t\t}\r\n\r\n\t\tchildVNode._parent = newParentVNode;\r\n\t\tchildVNode._depth = newParentVNode._depth + 1;\r\n\r\n\t\t// Check if we find a corresponding element in oldChildren.\r\n\t\t// If found, delete the array item by setting to `undefined`.\r\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\r\n\t\t// (holes).\r\n\t\toldVNode = oldChildren[i];\r\n\r\n\t\tif (\r\n\t\t\toldVNode === null ||\r\n\t\t\t(oldVNode &&\r\n\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\tchildVNode.type === oldVNode.type)\r\n\t\t) {\r\n\t\t\toldChildren[i] = undefined;\r\n\t\t} else {\r\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\r\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\r\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\r\n\t\t\t\toldVNode = oldChildren[j];\r\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\r\n\t\t\t\t// We always match by type (in either case).\r\n\t\t\t\tif (\r\n\t\t\t\t\toldVNode &&\r\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\r\n\t\t\t\t\tchildVNode.type === oldVNode.type\r\n\t\t\t\t) {\r\n\t\t\t\t\toldChildren[j] = undefined;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\toldVNode = null;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\toldVNode = oldVNode || EMPTY_OBJ;\r\n\r\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\r\n\t\tdiff(\r\n\t\t\tparentDom,\r\n\t\t\tchildVNode,\r\n\t\t\toldVNode,\r\n\t\t\tglobalContext,\r\n\t\t\tisSvg,\r\n\t\t\texcessDomChildren,\r\n\t\t\tcommitQueue,\r\n\t\t\toldDom,\r\n\t\t\tisHydrating\r\n\t\t);\r\n\r\n\t\tnewDom = childVNode._dom;\r\n\r\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\r\n\t\t\tif (!refs) refs = [];\r\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\r\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\r\n\t\t}\r\n\r\n\t\tif (newDom != null) {\r\n\t\t\tif (firstChildDom == null) {\r\n\t\t\t\tfirstChildDom = newDom;\r\n\t\t\t}\r\n\r\n\t\t\tif (\r\n\t\t\t\ttypeof childVNode.type == 'function' &&\r\n\t\t\t\tchildVNode._children === oldVNode._children\r\n\t\t\t) {\r\n\t\t\t\tchildVNode._nextDom = oldDom = reorderChildren(\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldDom,\r\n\t\t\t\t\tparentDom\r\n\t\t\t\t);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tchildVNode,\r\n\t\t\t\t\toldVNode,\r\n\t\t\t\t\toldChildren,\r\n\t\t\t\t\tnewDom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tif (typeof newParentVNode.type == 'function') {\r\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\r\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\r\n\t\t\t\t//\r\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\r\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\r\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\r\n\t\t\t\t// node's nextSibling.\r\n\t\t\t\tnewParentVNode._nextDom = oldDom;\r\n\t\t\t}\r\n\t\t} else if (\r\n\t\t\toldDom &&\r\n\t\t\toldVNode._dom == oldDom &&\r\n\t\t\toldDom.parentNode != parentDom\r\n\t\t) {\r\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\r\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\r\n\t\t\toldDom = getDomSibling(oldVNode);\r\n\t\t}\r\n\t}\r\n\r\n\tnewParentVNode._dom = firstChildDom;\r\n\r\n\t// Remove remaining oldChildren if there are any.\r\n\tfor (i = oldChildrenLength; i--; ) {\r\n\t\tif (oldChildren[i] != null) {\r\n\t\t\tif (\r\n\t\t\t\ttypeof newParentVNode.type == 'function' &&\r\n\t\t\t\toldChildren[i]._dom != null &&\r\n\t\t\t\toldChildren[i]._dom == newParentVNode._nextDom\r\n\t\t\t) {\r\n\t\t\t\t// If the newParentVNode.__nextDom points to a dom node that is about to\r\n\t\t\t\t// be unmounted, then get the next sibling of that vnode and set\r\n\t\t\t\t// _nextDom to it\r\n\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldParentVNode, i + 1);\r\n\t\t\t}\r\n\r\n\t\t\tunmount(oldChildren[i], oldChildren[i]);\r\n\t\t}\r\n\t}\r\n\r\n\t// Set refs only after unmount\r\n\tif (refs) {\r\n\t\tfor (i = 0; i < refs.length; i++) {\r\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction reorderChildren(childVNode, oldDom, parentDom) {\r\n\t// Note: VNodes in nested suspended trees may be missing _children.\r\n\tlet c = childVNode._children;\r\n\tlet tmp = 0;\r\n\tfor (; c && tmp < c.length; tmp++) {\r\n\t\tlet vnode = c[tmp];\r\n\t\tif (vnode) {\r\n\t\t\t// We typically enter this code path on sCU bailout, where we copy\r\n\t\t\t// oldVNode._children to newVNode._children. If that is the case, we need\r\n\t\t\t// to update the old children's _parent pointer to point to the newVNode\r\n\t\t\t// (childVNode here).\r\n\t\t\tvnode._parent = childVNode;\r\n\r\n\t\t\tif (typeof vnode.type == 'function') {\r\n\t\t\t\toldDom = reorderChildren(vnode, oldDom, parentDom);\r\n\t\t\t} else {\r\n\t\t\t\toldDom = placeChild(\r\n\t\t\t\t\tparentDom,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tvnode,\r\n\t\t\t\t\tc,\r\n\t\t\t\t\tvnode._dom,\r\n\t\t\t\t\toldDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n\r\n/**\r\n * Flatten and loop through the children of a virtual node\r\n * @param {import('../index').ComponentChildren} children The unflattened\r\n * children of a virtual node\r\n * @returns {import('../internal').VNode[]}\r\n */\r\nexport function toChildArray(children, out) {\r\n\tout = out || [];\r\n\tif (children == null || typeof children == 'boolean') {\r\n\t} else if (Array.isArray(children)) {\r\n\t\tchildren.some(child => {\r\n\t\t\ttoChildArray(child, out);\r\n\t\t});\r\n\t} else {\r\n\t\tout.push(children);\r\n\t}\r\n\treturn out;\r\n}\r\n\r\nfunction placeChild(\r\n\tparentDom,\r\n\tchildVNode,\r\n\toldVNode,\r\n\toldChildren,\r\n\tnewDom,\r\n\toldDom\r\n) {\r\n\tlet nextDom;\r\n\tif (childVNode._nextDom !== undefined) {\r\n\t\t// Only Fragments or components that return Fragment like VNodes will\r\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\r\n\t\t// of last DOM child of this child VNode\r\n\t\tnextDom = childVNode._nextDom;\r\n\r\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\r\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\r\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\r\n\t\t// can clean up the property\r\n\t\tchildVNode._nextDom = undefined;\r\n\t} else if (\r\n\t\toldVNode == null ||\r\n\t\tnewDom != oldDom ||\r\n\t\tnewDom.parentNode == null\r\n\t) {\r\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\r\n\t\t\tparentDom.appendChild(newDom);\r\n\t\t\tnextDom = null;\r\n\t\t} else {\r\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\r\n\t\t\tfor (\r\n\t\t\t\tlet sibDom = oldDom, j = 0;\r\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\r\n\t\t\t\tj += 2\r\n\t\t\t) {\r\n\t\t\t\tif (sibDom == newDom) {\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tparentDom.insertBefore(newDom, oldDom);\r\n\t\t\tnextDom = oldDom;\r\n\t\t}\r\n\t}\r\n\r\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\r\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\r\n\t// See more detail in create-element.js:createVNode\r\n\tif (nextDom !== undefined) {\r\n\t\toldDom = nextDom;\r\n\t} else {\r\n\t\toldDom = newDom.nextSibling;\r\n\t}\r\n\r\n\treturn oldDom;\r\n}\r\n", "import { IS_NON_DIMENSIONAL } from '../constants';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff the old and new properties of a VNode and apply changes to the DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to apply\r\n * changes to\r\n * @param {object} newProps The new props\r\n * @param {object} oldProps The old props\r\n * @param {boolean} isSvg Whether or not this node is an SVG node\r\n * @param {boolean} hydrate Whether or not we are in hydration mode\r\n */\r\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\r\n\tlet i;\r\n\r\n\tfor (i in oldProps) {\r\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\r\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n\r\n\tfor (i in newProps) {\r\n\t\tif (\r\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\r\n\t\t\ti !== 'children' &&\r\n\t\t\ti !== 'key' &&\r\n\t\t\ti !== 'value' &&\r\n\t\t\ti !== 'checked' &&\r\n\t\t\toldProps[i] !== newProps[i]\r\n\t\t) {\r\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction setStyle(style, key, value) {\r\n\tif (key[0] === '-') {\r\n\t\tstyle.setProperty(key, value);\r\n\t} else if (value == null) {\r\n\t\tstyle[key] = '';\r\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\r\n\t\tstyle[key] = value;\r\n\t} else {\r\n\t\tstyle[key] = value + 'px';\r\n\t}\r\n}\r\n\r\n/**\r\n * Set a property value on a DOM node\r\n * @param {import('../internal').PreactElement} dom The DOM node to modify\r\n * @param {string} name The name of the property to set\r\n * @param {*} value The value to set the property to\r\n * @param {*} oldValue The old value the property had\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\r\n */\r\nexport function setProperty(dom, name, value, oldValue, isSvg) {\r\n\tlet useCapture;\r\n\r\n\to: if (name === 'style') {\r\n\t\tif (typeof value == 'string') {\r\n\t\t\tdom.style.cssText = value;\r\n\t\t} else {\r\n\t\t\tif (typeof oldValue == 'string') {\r\n\t\t\t\tdom.style.cssText = oldValue = '';\r\n\t\t\t}\r\n\r\n\t\t\tif (oldValue) {\r\n\t\t\t\tfor (name in oldValue) {\r\n\t\t\t\t\tif (!(value && name in value)) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (value) {\r\n\t\t\t\tfor (name in value) {\r\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\r\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\r\n\telse if (name[0] === 'o' && name[1] === 'n') {\r\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\r\n\r\n\t\t// Infer correct casing for DOM built-in events:\r\n\t\tif (name.toLowerCase() in dom) name = name.toLowerCase().slice(2);\r\n\t\telse name = name.slice(2);\r\n\r\n\t\tif (!dom._listeners) dom._listeners = {};\r\n\t\tdom._listeners[name + useCapture] = value;\r\n\r\n\t\tif (value) {\r\n\t\t\tif (!oldValue) {\r\n\t\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\t\tdom.addEventListener(name, handler, useCapture);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\r\n\t\t\tdom.removeEventListener(name, handler, useCapture);\r\n\t\t}\r\n\t} else if (name !== 'dangerouslySetInnerHTML') {\r\n\t\tif (isSvg) {\r\n\t\t\t// Normalize incorrect prop usage for SVG:\r\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\r\n\t\t\t// - className --> class\r\n\t\t\tname = name.replace(/xlink[H:h]/, 'h').replace(/sName$/, 's');\r\n\t\t} else if (\r\n\t\t\tname !== 'href' &&\r\n\t\t\tname !== 'list' &&\r\n\t\t\tname !== 'form' &&\r\n\t\t\t// Default value in browsers is `-1` and an empty string is\r\n\t\t\t// cast to `0` instead\r\n\t\t\tname !== 'tabIndex' &&\r\n\t\t\tname !== 'download' &&\r\n\t\t\tname in dom\r\n\t\t) {\r\n\t\t\ttry {\r\n\t\t\t\tdom[name] = value == null ? '' : value;\r\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\r\n\t\t\t\tbreak o;\r\n\t\t\t} catch (e) {}\r\n\t\t}\r\n\r\n\t\t// ARIA-attributes have a different notion of boolean values.\r\n\t\t// The value `false` is different from the attribute not\r\n\t\t// existing on the DOM, so we can't remove it. For non-boolean\r\n\t\t// ARIA-attributes we could treat false as a removal, but the\r\n\t\t// amount of exceptions would cost us too many bytes. On top of\r\n\t\t// that other VDOM frameworks also always stringify `false`.\r\n\r\n\t\tif (typeof value === 'function') {\r\n\t\t\t// never serialize functions as attribute values\r\n\t\t} else if (\r\n\t\t\tvalue != null &&\r\n\t\t\t(value !== false || (name[0] === 'a' && name[1] === 'r'))\r\n\t\t) {\r\n\t\t\tdom.setAttribute(name, value);\r\n\t\t} else {\r\n\t\t\tdom.removeAttribute(name);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Proxy an event to hooked event handlers\r\n * @param {Event} e The event object from the browser\r\n * @private\r\n */\r\nfunction eventProxy(e) {\r\n\tthis._listeners[e.type + false](options.event ? options.event(e) : e);\r\n}\r\n\r\nfunction eventProxyCapture(e) {\r\n\tthis._listeners[e.type + true](options.event ? options.event(e) : e);\r\n}\r\n", "import { EMPTY_OBJ } from '../constants';\r\nimport { Component, getDomSibling } from '../component';\r\nimport { Fragment } from '../create-element';\r\nimport { diffChildren } from './children';\r\nimport { diffProps, setProperty } from './props';\r\nimport { assign, removeNode, slice } from '../util';\r\nimport options from '../options';\r\n\r\n/**\r\n * Diff two virtual nodes and apply proper changes to the DOM\r\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object. Modified by getChildContext\r\n * @param {boolean} isSvg Whether or not this element is an SVG node\r\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\r\n * element any new dom elements should be placed around. Likely `null` on first\r\n * render (except when hydrating). Can be a sibling DOM element when diffing\r\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\r\n * @param {boolean} [isHydrating] Whether or not we are in hydration\r\n */\r\nexport function diff(\r\n\tparentDom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\toldDom,\r\n\tisHydrating\r\n) {\r\n\tlet tmp,\r\n\t\tnewType = newVNode.type;\r\n\r\n\t// When passing through createElement it assigns the object\r\n\t// constructor as undefined. This to prevent JSON-injection.\r\n\tif (newVNode.constructor !== undefined) return null;\r\n\r\n\t// If the previous diff bailed out, resume creating/hydrating.\r\n\tif (oldVNode._hydrating != null) {\r\n\t\tisHydrating = oldVNode._hydrating;\r\n\t\toldDom = newVNode._dom = oldVNode._dom;\r\n\t\t// if we resume, we want the tree to be \"unlocked\"\r\n\t\tnewVNode._hydrating = null;\r\n\t\texcessDomChildren = [oldDom];\r\n\t}\r\n\r\n\tif ((tmp = options._diff)) tmp(newVNode);\r\n\r\n\ttry {\r\n\t\touter: if (typeof newType == 'function') {\r\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\r\n\t\t\tlet newProps = newVNode.props;\r\n\r\n\t\t\t// Necessary for createContext api. Setting this property will pass\r\n\t\t\t// the context value as `this.context` just for this component.\r\n\t\t\ttmp = newType.contextType;\r\n\t\t\tlet provider = tmp && globalContext[tmp._id];\r\n\t\t\tlet componentContext = tmp\r\n\t\t\t\t? provider\r\n\t\t\t\t\t? provider.props.value\r\n\t\t\t\t\t: tmp._defaultValue\r\n\t\t\t\t: globalContext;\r\n\r\n\t\t\t// Get component and set it to `c`\r\n\t\t\tif (oldVNode._component) {\r\n\t\t\t\tc = newVNode._component = oldVNode._component;\r\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\r\n\t\t\t} else {\r\n\t\t\t\t// Instantiate the new component\r\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\r\n\t\t\t\t\t// @ts-ignore The check above verifies that newType is suppose to be constructed\r\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// @ts-ignore Trust me, Component implements the interface we want\r\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\r\n\t\t\t\t\tc.constructor = newType;\r\n\t\t\t\t\tc.render = doRender;\r\n\t\t\t\t}\r\n\t\t\t\tif (provider) provider.sub(c);\r\n\r\n\t\t\t\tc.props = newProps;\r\n\t\t\t\tif (!c.state) c.state = {};\r\n\t\t\t\tc.context = componentContext;\r\n\t\t\t\tc._globalContext = globalContext;\r\n\t\t\t\tisNew = c._dirty = true;\r\n\t\t\t\tc._renderCallbacks = [];\r\n\t\t\t}\r\n\r\n\t\t\t// Invoke getDerivedStateFromProps\r\n\t\t\tif (c._nextState == null) {\r\n\t\t\t\tc._nextState = c.state;\r\n\t\t\t}\r\n\t\t\tif (newType.getDerivedStateFromProps != null) {\r\n\t\t\t\tif (c._nextState == c.state) {\r\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tassign(\r\n\t\t\t\t\tc._nextState,\r\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\toldProps = c.props;\r\n\t\t\toldState = c.state;\r\n\r\n\t\t\t// Invoke pre-render lifecycle methods\r\n\t\t\tif (isNew) {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tc.componentWillMount != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillMount();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidMount != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (\r\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\r\n\t\t\t\t\tnewProps !== oldProps &&\r\n\t\t\t\t\tc.componentWillReceiveProps != null\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (\r\n\t\t\t\t\t(!c._force &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\r\n\t\t\t\t\t\tc.shouldComponentUpdate(\r\n\t\t\t\t\t\t\tnewProps,\r\n\t\t\t\t\t\t\tc._nextState,\r\n\t\t\t\t\t\t\tcomponentContext\r\n\t\t\t\t\t\t) === false) ||\r\n\t\t\t\t\tnewVNode._original === oldVNode._original\r\n\t\t\t\t) {\r\n\t\t\t\t\tc.props = newProps;\r\n\t\t\t\t\tc.state = c._nextState;\r\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\r\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\r\n\t\t\t\t\tc._vnode = newVNode;\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t\tnewVNode._children.forEach(vnode => {\r\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\t\t\tcommitQueue.push(c);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbreak outer;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentWillUpdate != null) {\r\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (c.componentDidUpdate != null) {\r\n\t\t\t\t\tc._renderCallbacks.push(() => {\r\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tc.context = componentContext;\r\n\t\t\tc.props = newProps;\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif ((tmp = options._render)) tmp(newVNode);\r\n\r\n\t\t\tc._dirty = false;\r\n\t\t\tc._vnode = newVNode;\r\n\t\t\tc._parentDom = parentDom;\r\n\r\n\t\t\ttmp = c.render(c.props, c.state, c.context);\r\n\r\n\t\t\t// Handle setState called in render, see #2553\r\n\t\t\tc.state = c._nextState;\r\n\r\n\t\t\tif (c.getChildContext != null) {\r\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\r\n\t\t\t}\r\n\r\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\r\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\r\n\t\t\t}\r\n\r\n\t\t\tlet isTopLevelFragment =\r\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\r\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\r\n\r\n\t\t\tdiffChildren(\r\n\t\t\t\tparentDom,\r\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\toldDom,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\tc.base = newVNode._dom;\r\n\r\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\r\n\t\t\tnewVNode._hydrating = null;\r\n\r\n\t\t\tif (c._renderCallbacks.length) {\r\n\t\t\t\tcommitQueue.push(c);\r\n\t\t\t}\r\n\r\n\t\t\tif (clearProcessingException) {\r\n\t\t\t\tc._pendingError = c._processingException = null;\r\n\t\t\t}\r\n\r\n\t\t\tc._force = false;\r\n\t\t} else if (\r\n\t\t\texcessDomChildren == null &&\r\n\t\t\tnewVNode._original === oldVNode._original\r\n\t\t) {\r\n\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t} else {\r\n\t\t\tnewVNode._dom = diffElementNodes(\r\n\t\t\t\toldVNode._dom,\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg,\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\r\n\t} catch (e) {\r\n\t\tnewVNode._original = null;\r\n\t\t// if hydrating or creating initial tree, bailout preserves DOM:\r\n\t\tif (isHydrating || excessDomChildren != null) {\r\n\t\t\tnewVNode._dom = oldDom;\r\n\t\t\tnewVNode._hydrating = !!isHydrating;\r\n\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\r\n\t\t\t// ^ could possibly be simplified to:\r\n\t\t\t// excessDomChildren.length = 0;\r\n\t\t}\r\n\t\toptions._catchError(e, newVNode, oldVNode);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {import('../internal').VNode} root\r\n */\r\nexport function commitRoot(commitQueue, root) {\r\n\tif (options._commit) options._commit(root, commitQueue);\r\n\r\n\tcommitQueue.some(c => {\r\n\t\ttry {\r\n\t\t\t// @ts-ignore Reuse the commitQueue variable here so the type changes\r\n\t\t\tcommitQueue = c._renderCallbacks;\r\n\t\t\tc._renderCallbacks = [];\r\n\t\t\tcommitQueue.some(cb => {\r\n\t\t\t\t// @ts-ignore See above ts-ignore on commitQueue\r\n\t\t\t\tcb.call(c);\r\n\t\t\t});\r\n\t\t} catch (e) {\r\n\t\t\toptions._catchError(e, c._vnode);\r\n\t\t}\r\n\t});\r\n}\r\n\r\n/**\r\n * Diff two virtual nodes representing DOM element\r\n * @param {import('../internal').PreactElement} dom The DOM element representing\r\n * the virtual nodes being diffed\r\n * @param {import('../internal').VNode} newVNode The new virtual node\r\n * @param {import('../internal').VNode} oldVNode The old virtual node\r\n * @param {object} globalContext The current context object\r\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\r\n * @param {*} excessDomChildren\r\n * @param {Array<import('../internal').Component>} commitQueue List of components\r\n * which have callbacks to invoke in commitRoot\r\n * @param {boolean} isHydrating Whether or not we are in hydration\r\n * @returns {import('../internal').PreactElement}\r\n */\r\nfunction diffElementNodes(\r\n\tdom,\r\n\tnewVNode,\r\n\toldVNode,\r\n\tglobalContext,\r\n\tisSvg,\r\n\texcessDomChildren,\r\n\tcommitQueue,\r\n\tisHydrating\r\n) {\r\n\tlet oldProps = oldVNode.props;\r\n\tlet newProps = newVNode.props;\r\n\tlet nodeType = newVNode.type;\r\n\tlet i = 0;\r\n\r\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\r\n\tif (nodeType === 'svg') isSvg = true;\r\n\r\n\tif (excessDomChildren != null) {\r\n\t\tfor (; i < excessDomChildren.length; i++) {\r\n\t\t\tconst child = excessDomChildren[i];\r\n\r\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\r\n\t\t\t// argument matches an element in excessDomChildren, remove it from\r\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\r\n\t\t\tif (\r\n\t\t\t\tchild &&\r\n\t\t\t\t'setAttribute' in child === !!nodeType &&\r\n\t\t\t\t(nodeType ? child.localName === nodeType : child.nodeType === 3)\r\n\t\t\t) {\r\n\t\t\t\tdom = child;\r\n\t\t\t\texcessDomChildren[i] = null;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (dom == null) {\r\n\t\tif (nodeType === null) {\r\n\t\t\t// @ts-ignore createTextNode returns Text, we expect PreactElement\r\n\t\t\treturn document.createTextNode(newProps);\r\n\t\t}\r\n\r\n\t\tif (isSvg) {\r\n\t\t\tdom = document.createElementNS(\r\n\t\t\t\t'http://www.w3.org/2000/svg',\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType\r\n\t\t\t);\r\n\t\t} else {\r\n\t\t\tdom = document.createElement(\r\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\r\n\t\t\t\tnodeType,\r\n\t\t\t\tnewProps.is && newProps\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// we created a new parent, so none of the previously attached children can be reused:\r\n\t\texcessDomChildren = null;\r\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\r\n\t\tisHydrating = false;\r\n\t}\r\n\r\n\tif (nodeType === null) {\r\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\r\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\r\n\t\t\tdom.data = newProps;\r\n\t\t}\r\n\t} else {\r\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\r\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\r\n\r\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\r\n\r\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\r\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\r\n\r\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\r\n\t\t// @TODO we should warn in debug mode when props don't match here.\r\n\t\tif (!isHydrating) {\r\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\r\n\t\t\t// we should read the existing DOM attributes to diff them\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\toldProps = {};\r\n\t\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\r\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (newHtml || oldHtml) {\r\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\r\n\t\t\t\tif (\r\n\t\t\t\t\t!newHtml ||\r\n\t\t\t\t\t((!oldHtml || newHtml.__html != oldHtml.__html) &&\r\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML)\r\n\t\t\t\t) {\r\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\r\n\r\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\r\n\t\tif (newHtml) {\r\n\t\t\tnewVNode._children = [];\r\n\t\t} else {\r\n\t\t\ti = newVNode.props.children;\r\n\t\t\tdiffChildren(\r\n\t\t\t\tdom,\r\n\t\t\t\tArray.isArray(i) ? i : [i],\r\n\t\t\t\tnewVNode,\r\n\t\t\t\toldVNode,\r\n\t\t\t\tglobalContext,\r\n\t\t\t\tisSvg && nodeType !== 'foreignObject',\r\n\t\t\t\texcessDomChildren,\r\n\t\t\t\tcommitQueue,\r\n\t\t\t\texcessDomChildren\r\n\t\t\t\t\t? excessDomChildren[0]\r\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\r\n\t\t\t\tisHydrating\r\n\t\t\t);\r\n\r\n\t\t\t// Remove children that are not part of any vnode.\r\n\t\t\tif (excessDomChildren != null) {\r\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\r\n\t\t\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// (as above, don't diff props during hydration)\r\n\t\tif (!isHydrating) {\r\n\t\t\tif (\r\n\t\t\t\t'value' in newProps &&\r\n\t\t\t\t(i = newProps.value) !== undefined &&\r\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\r\n\t\t\t\t// despite the attribute not being present. When the attribute\r\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\r\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\r\n\t\t\t\t(i !== oldProps.value ||\r\n\t\t\t\t\ti !== dom.value ||\r\n\t\t\t\t\t(nodeType === 'progress' && !i))\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\r\n\t\t\t}\r\n\t\t\tif (\r\n\t\t\t\t'checked' in newProps &&\r\n\t\t\t\t(i = newProps.checked) !== undefined &&\r\n\t\t\t\ti !== dom.checked\r\n\t\t\t) {\r\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn dom;\r\n}\r\n\r\n/**\r\n * Invoke or update a ref, depending on whether it is a function or object ref.\r\n * @param {object|function} ref\r\n * @param {any} value\r\n * @param {import('../internal').VNode} vnode\r\n */\r\nexport function applyRef(ref, value, vnode) {\r\n\ttry {\r\n\t\tif (typeof ref == 'function') ref(value);\r\n\t\telse ref.current = value;\r\n\t} catch (e) {\r\n\t\toptions._catchError(e, vnode);\r\n\t}\r\n}\r\n\r\n/**\r\n * Unmount a virtual node from the tree and apply DOM changes\r\n * @param {import('../internal').VNode} vnode The virtual node to unmount\r\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\r\n * initiated the unmount\r\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\r\n * current element is already detached from the DOM.\r\n */\r\nexport function unmount(vnode, parentVNode, skipRemove) {\r\n\tlet r;\r\n\tif (options.unmount) options.unmount(vnode);\r\n\r\n\tif ((r = vnode.ref)) {\r\n\t\tif (!r.current || r.current === vnode._dom) applyRef(r, null, parentVNode);\r\n\t}\r\n\r\n\tif ((r = vnode._component) != null) {\r\n\t\tif (r.componentWillUnmount) {\r\n\t\t\ttry {\r\n\t\t\t\tr.componentWillUnmount();\r\n\t\t\t} catch (e) {\r\n\t\t\t\toptions._catchError(e, parentVNode);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tr.base = r._parentDom = null;\r\n\t}\r\n\r\n\tif ((r = vnode._children)) {\r\n\t\tfor (let i = 0; i < r.length; i++) {\r\n\t\t\tif (r[i]) {\r\n\t\t\t\tunmount(r[i], parentVNode, typeof vnode.type != 'function');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (!skipRemove && vnode._dom != null) removeNode(vnode._dom);\r\n\r\n\t// Must be set to `undefined` to properly clean up `_nextDom`\r\n\t// for which `null` is a valid value. See comment in `create-element.js`\r\n\tvnode._dom = vnode._nextDom = undefined;\r\n}\r\n\r\n/** The `.render()` method for a PFC backing instance. */\r\nfunction doRender(props, state, context) {\r\n\treturn this.constructor(props, context);\r\n}\r\n", "import { EMPTY_OBJ } from './constants';\r\nimport { commitRoot, diff } from './diff/index';\r\nimport { createElement, Fragment } from './create-element';\r\nimport options from './options';\r\nimport { slice } from './util';\r\n\r\n/**\r\n * Render a Preact virtual node into a DOM element\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * render into\r\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\r\n * existing DOM tree rooted at `replaceNode`\r\n */\r\nexport function render(vnode, parentDom, replaceNode) {\r\n\tif (options._root) options._root(vnode, parentDom);\r\n\r\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\r\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\r\n\t// element..\r\n\tlet isHydrating = typeof replaceNode === 'function';\r\n\r\n\t// To be able to support calling `render()` multiple times on the same\r\n\t// DOM node, we need to obtain a reference to the previous tree. We do\r\n\t// this by assigning a new `_children` property to DOM nodes which points\r\n\t// to the last rendered tree. By default this property is not present, which\r\n\t// means that we are mounting a new tree for the first time.\r\n\tlet oldVNode = isHydrating\r\n\t\t? null\r\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\r\n\r\n\tvnode = (\r\n\t\t(!isHydrating && replaceNode) ||\r\n\t\tparentDom\r\n\t)._children = createElement(Fragment, null, [vnode]);\r\n\r\n\t// List of effects that need to be called after diffing.\r\n\tlet commitQueue = [];\r\n\tdiff(\r\n\t\tparentDom,\r\n\t\t// Determine the new vnode tree and store it on the DOM element on\r\n\t\t// our custom `_children` property.\r\n\t\tvnode,\r\n\t\toldVNode || EMPTY_OBJ,\r\n\t\tEMPTY_OBJ,\r\n\t\tparentDom.ownerSVGElement !== undefined,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? [replaceNode]\r\n\t\t\t: oldVNode\r\n\t\t\t? null\r\n\t\t\t: parentDom.firstChild\r\n\t\t\t? slice.call(parentDom.childNodes)\r\n\t\t\t: null,\r\n\t\tcommitQueue,\r\n\t\t!isHydrating && replaceNode\r\n\t\t\t? replaceNode\r\n\t\t\t: oldVNode\r\n\t\t\t? oldVNode._dom\r\n\t\t\t: parentDom.firstChild,\r\n\t\tisHydrating\r\n\t);\r\n\r\n\t// Flush all queued effects\r\n\tcommitRoot(commitQueue, vnode);\r\n}\r\n\r\n/**\r\n * Update an existing DOM element with data from a Preact virtual node\r\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\r\n * @param {import('./internal').PreactElement} parentDom The DOM element to\r\n * update\r\n */\r\nexport function hydrate(vnode, parentDom) {\r\n\trender(vnode, parentDom, hydrate);\r\n}\r\n", "import { assign, slice } from './util';\r\nimport { createVNode } from './create-element';\r\n\r\n/**\r\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\r\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\r\n * @param {object} props Attributes/props to add when cloning\r\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used as replacement children.\r\n * @returns {import('./internal').VNode}\r\n */\r\nexport function cloneElement(vnode, props, children) {\r\n\tlet normalizedProps = assign({}, vnode.props),\r\n\t\tkey,\r\n\t\tref,\r\n\t\ti;\r\n\tfor (i in props) {\r\n\t\tif (i == 'key') key = props[i];\r\n\t\telse if (i == 'ref') ref = props[i];\r\n\t\telse normalizedProps[i] = props[i];\r\n\t}\r\n\r\n\tif (arguments.length > 2) {\r\n\t\tnormalizedProps.children =\r\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\r\n\t}\r\n\r\n\treturn createVNode(\r\n\t\tvnode.type,\r\n\t\tnormalizedProps,\r\n\t\tkey || vnode.key,\r\n\t\tref || vnode.ref,\r\n\t\tnull\r\n\t);\r\n}\r\n", "/**\r\n * Find the closest error boundary to a thrown error and call it\r\n * @param {object} error The thrown value\r\n * @param {import('../internal').VNode} vnode The vnode that threw\r\n * the error that was caught (except for unmounting when this parameter\r\n * is the highest parent that was being unmounted)\r\n */\r\nexport function _catchError(error, vnode) {\r\n\t/** @type {import('../internal').Component} */\r\n\tlet component, ctor, handled;\r\n\r\n\tfor (; (vnode = vnode._parent); ) {\r\n\t\tif ((component = vnode._component) && !component._processingException) {\r\n\t\t\ttry {\r\n\t\t\t\tctor = component.constructor;\r\n\r\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\r\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (component.componentDidCatch != null) {\r\n\t\t\t\t\tcomponent.componentDidCatch(error);\r\n\t\t\t\t\thandled = component._dirty;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\r\n\t\t\t\tif (handled) {\r\n\t\t\t\t\treturn (component._pendingError = component);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\terror = e;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tthrow error;\r\n}\r\n", "import { SearchIndex } from './helpers'\n\nexport function deepEqual(a: any, b: any): boolean {\n  return (\n    Array.isArray(a) &&\n    Array.isArray(b) &&\n    a.length === b.length &&\n    a.every((val, index) => val == b[index])\n  )\n}\n\nexport async function sleep(frames = 1) {\n  for (let _ in [...Array(frames).keys()]) {\n    await new Promise(requestAnimationFrame)\n  }\n}\n\nexport function getEmojiData(emoji, { skinIndex = 0 } = {}) {\n  const skin =\n    emoji.skins[skinIndex] ||\n    (() => {\n      skinIndex = 0\n      return emoji.skins[skinIndex]\n    })()\n\n  const emojiData: any = {\n    id: emoji.id,\n    name: emoji.name,\n    native: skin.native,\n    unified: skin.unified,\n    keywords: emoji.keywords,\n    shortcodes: skin.shortcodes || emoji.shortcodes,\n  }\n\n  if (emoji.skins.length > 1) {\n    emojiData.skin = skinIndex + 1\n  }\n\n  if (skin.src) {\n    emojiData.src = skin.src\n  }\n\n  if (emoji.aliases && emoji.aliases.length) {\n    emojiData.aliases = emoji.aliases\n  }\n\n  if (emoji.emoticons && emoji.emoticons.length) {\n    emojiData.emoticons = emoji.emoticons\n  }\n\n  return emojiData\n}\n\nexport async function getEmojiDataFromNative(nativeString) {\n  const results = await SearchIndex.search(nativeString, {\n    maxResults: 1,\n    caller: 'getEmojiDataFromNative',\n  })\n  if (!results || !results.length) return null\n\n  const emoji = results[0]\n  let skinIndex = 0\n\n  for (let skin of emoji.skins) {\n    if (skin.native == nativeString) {\n      break\n    }\n\n    skinIndex++\n  }\n\n  return getEmojiData(emoji, { skinIndex })\n}\n", "export { default as Store } from './store'\n\nexport { default as NativeSupport } from './native-support'\nexport { default as FrequentlyUsed } from './frequently-used'\nexport { default as SearchIndex } from './search-index'\n\nexport const SafeFlags = [\n  'checkered_flag',\n  'crossed_flags',\n  'pirate_flag',\n  'rainbow-flag',\n  'transgender_flag',\n  'triangular_flag_on_post',\n  'waving_black_flag',\n  'waving_white_flag',\n]\n", "function set(key: string, value: string) {\n  try {\n    window.localStorage[`emoji-mart.${key}`] = JSON.stringify(value)\n  } catch (error) {}\n}\n\nfunction get(key: string): any {\n  try {\n    const value = window.localStorage[`emoji-mart.${key}`]\n\n    if (value) {\n      return JSON.parse(value)\n    }\n  } catch (error) {}\n}\n\nexport default { set, get }\n", "const CACHE = new Map()\nconst VERSIONS = [\n  { v: 15, emoji: '🫨' },\n  { v: 14, emoji: '🫠' },\n  { v: 13.1, emoji: '😶‍🌫️' },\n  { v: 13, emoji: '🥸' },\n  { v: 12.1, emoji: '🧑‍🦰' },\n  { v: 12, emoji: '🥱' },\n  { v: 11, emoji: '🥰' },\n  { v: 5, emoji: '🤩' },\n  { v: 4, emoji: '👱‍♀️' },\n  { v: 3, emoji: '🤣' },\n  { v: 2, emoji: '👋🏻' },\n  { v: 1, emoji: '🙃' },\n]\n\nfunction latestVersion() {\n  for (const { v, emoji } of VERSIONS) {\n    if (isSupported(emoji)) {\n      return v\n    }\n  }\n}\n\nfunction noCountryFlags() {\n  if (isSupported('🇨🇦')) {\n    return false\n  }\n\n  return true\n}\n\nfunction isSupported(emoji) {\n  if (CACHE.has(emoji)) {\n    return CACHE.get(emoji)\n  }\n\n  const supported = isEmojiSupported(emoji)\n  CACHE.set(emoji, supported)\n\n  return supported\n}\n\n// https://github.com/koala-interactive/is-emoji-supported\nconst isEmojiSupported = (() => {\n  let ctx = null\n  try {\n    if (!navigator.userAgent.includes('jsdom')) {\n      ctx = document\n        .createElement('canvas')\n        .getContext('2d', { willReadFrequently: true })\n    }\n  } catch {}\n\n  // Not in browser env\n  if (!ctx) {\n    return () => false\n  }\n\n  const CANVAS_HEIGHT = 25\n  const CANVAS_WIDTH = 20\n  const textSize = Math.floor(CANVAS_HEIGHT / 2)\n\n  // Initialize convas context\n  ctx.font = textSize + 'px Arial, Sans-Serif'\n  ctx.textBaseline = 'top'\n  ctx.canvas.width = CANVAS_WIDTH * 2\n  ctx.canvas.height = CANVAS_HEIGHT\n\n  return (unicode) => {\n    ctx.clearRect(0, 0, CANVAS_WIDTH * 2, CANVAS_HEIGHT)\n\n    // Draw in red on the left\n    ctx.fillStyle = '#FF0000'\n    ctx.fillText(unicode, 0, 22)\n\n    // Draw in blue on right\n    ctx.fillStyle = '#0000FF'\n    ctx.fillText(unicode, CANVAS_WIDTH, 22)\n\n    const a = ctx.getImageData(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT).data\n    const count = a.length\n    let i = 0\n\n    // Search the first visible pixel\n    for (; i < count && !a[i + 3]; i += 4);\n\n    // No visible pixel\n    if (i >= count) {\n      return false\n    }\n\n    // Emoji has immutable color, so we check the color of the emoji in two different colors\n    // the result show be the same.\n    const x = CANVAS_WIDTH + ((i / 4) % CANVAS_WIDTH)\n    const y = Math.floor(i / 4 / CANVAS_WIDTH)\n    const b = ctx.getImageData(x, y, 1, 1).data\n\n    if (a[i] !== b[0] || a[i + 2] !== b[2]) {\n      return false\n    }\n\n    // Some emojis are a contraction of different ones, so if it's not\n    // supported, it will show multiple characters\n    if (ctx.measureText(unicode).width >= CANVAS_WIDTH) {\n      return false\n    }\n\n    // Supported\n    return true\n  }\n})()\n\nexport default { latestVersion, noCountryFlags }\n", "// @ts-nocheck\nimport { Store } from '../helpers'\n\nconst DEFAULTS = [\n  '+1',\n  'grinning',\n  'kissing_heart',\n  'heart_eyes',\n  'laughing',\n  'stuck_out_tongue_winking_eye',\n  'sweat_smile',\n  'joy',\n  'scream',\n  'disappointed',\n  'unamused',\n  'weary',\n  'sob',\n  'sunglasses',\n  'heart',\n]\n\nlet Index: any | null = null\n\nfunction add(emoji: { id: string }) {\n  Index || (Index = Store.get('frequently') || {})\n\n  const emojiId = emoji.id || emoji\n  if (!emojiId) return\n\n  Index[emojiId] || (Index[emojiId] = 0)\n  Index[emojiId] += 1\n\n  Store.set('last', emojiId)\n  Store.set('frequently', Index)\n}\n\nfunction get({ maxFrequentRows, perLine }) {\n  if (!maxFrequentRows) return []\n\n  Index || (Index = Store.get('frequently'))\n  let emojiIds = []\n\n  if (!Index) {\n    Index = {}\n\n    for (let i in DEFAULTS.slice(0, perLine)) {\n      const emojiId = DEFAULTS[i]\n\n      Index[emojiId] = perLine - i\n      emojiIds.push(emojiId)\n    }\n\n    return emojiIds\n  }\n\n  const max = maxFrequentRows * perLine\n  const last = Store.get('last')\n\n  for (let emojiId in Index) {\n    emojiIds.push(emojiId)\n  }\n\n  emojiIds.sort((a, b) => {\n    const aScore = Index[b]\n    const bScore = Index[a]\n\n    if (aScore == bScore) {\n      return a.localeCompare(b)\n    }\n\n    return aScore - bScore\n  })\n\n  if (emojiIds.length > max) {\n    const removedIds = emojiIds.slice(max)\n    emojiIds = emojiIds.slice(0, max)\n\n    for (let removedId of removedIds) {\n      if (removedId == last) continue\n      delete Index[removedId]\n    }\n\n    if (last && emojiIds.indexOf(last) == -1) {\n      delete Index[emojiIds[emojiIds.length - 1]]\n      emojiIds.splice(-1, 1, last)\n    }\n\n    Store.set('frequently', Index)\n  }\n\n  return emojiIds\n}\n\nexport default { add, get, DEFAULTS }\n", "// @ts-nocheck\nimport { init, Data } from '../config'\n\nconst SHORTCODES_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/\nlet Pool = null\n\nfunction get(emojiId) {\n  if (emojiId.id) {\n    return emojiId\n  }\n\n  return (\n    Data.emojis[emojiId] ||\n    Data.emojis[Data.aliases[emojiId]] ||\n    Data.emojis[Data.natives[emojiId]]\n  )\n}\n\nfunction reset() {\n  Pool = null\n}\n\nasync function search(value, { maxResults, caller } = {}) {\n  if (!value || !value.trim().length) return null\n  maxResults || (maxResults = 90)\n\n  await init(null, { caller: caller || 'SearchIndex.search' })\n\n  const values = value\n    .toLowerCase()\n    .replace(/(\\w)-/, '$1 ')\n    .split(/[\\s|,]+/)\n    .filter((word, i, words) => {\n      return word.trim() && words.indexOf(word) == i\n    })\n\n  if (!values.length) return\n\n  let pool = Pool || (Pool = Object.values(Data.emojis))\n  let results, scores\n\n  for (const value of values) {\n    if (!pool.length) break\n\n    results = []\n    scores = {}\n\n    for (const emoji of pool) {\n      if (!emoji.search) continue\n      const score = emoji.search.indexOf(`,${value}`)\n      if (score == -1) continue\n\n      results.push(emoji)\n      scores[emoji.id] || (scores[emoji.id] = 0)\n      scores[emoji.id] += emoji.id == value ? 0 : score + 1\n    }\n\n    pool = results\n  }\n\n  if (results.length < 2) {\n    return results\n  }\n\n  results.sort((a, b) => {\n    const aScore = scores[a.id]\n    const bScore = scores[b.id]\n\n    if (aScore == bScore) {\n      return a.id.localeCompare(b.id)\n    }\n\n    return aScore - bScore\n  })\n\n  if (results.length > maxResults) {\n    results = results.slice(0, maxResults)\n  }\n\n  return results\n}\n\nexport default { search, get, reset, SHORTCODES_REGEX }\n", "// @ts-nocheck\nimport i18n_en from '@emoji-mart/data/i18n/en.json'\nimport PickerProps from './components/Picker/PickerProps'\nimport {\n  FrequentlyUsed,\n  NativeSupport,\n  SafeFlags,\n  SearchIndex,\n} from './helpers'\n\nexport let I18n = null\nexport let Data = null\n\nconst fetchCache = {}\nasync function fetchJSON(src) {\n  if (fetchCache[src]) {\n    return fetchCache[src]\n  }\n\n  const response = await fetch(src)\n  const json = await response.json()\n\n  fetchCache[src] = json\n  return json\n}\n\nlet promise: Promise<void> | null = null\nlet initiated = false\nlet initCallback = null\nlet initialized = false\n\nexport function init(options, { caller } = {}) {\n  promise ||\n    (promise = new Promise((resolve) => {\n      initCallback = resolve\n    }))\n\n  if (options) {\n    _init(options)\n  } else if (caller && !initialized) {\n    console.warn(\n      `\\`${caller}\\` requires data to be initialized first. Promise will be pending until \\`init\\` is called.`,\n    )\n  }\n\n  return promise\n}\n\nasync function _init(props) {\n  initialized = true\n\n  let { emojiVersion, set, locale } = props\n  emojiVersion || (emojiVersion = PickerProps.emojiVersion.value)\n  set || (set = PickerProps.set.value)\n  locale || (locale = PickerProps.locale.value)\n\n  if (!Data) {\n    Data =\n      (typeof props.data === 'function' ? await props.data() : props.data) ||\n      (await fetchJSON(\n        `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${emojiVersion}/${set}.json`,\n      ))\n\n    Data.emoticons = {}\n    Data.natives = {}\n\n    Data.categories.unshift({\n      id: 'frequent',\n      emojis: [],\n    })\n\n    for (const alias in Data.aliases) {\n      const emojiId = Data.aliases[alias]\n      const emoji = Data.emojis[emojiId]\n      if (!emoji) continue\n\n      emoji.aliases || (emoji.aliases = [])\n      emoji.aliases.push(alias)\n    }\n\n    Data.originalCategories = Data.categories\n  } else {\n    Data.categories = Data.categories.filter((c) => {\n      const isCustom = !!c.name\n      if (!isCustom) return true\n\n      return false\n    })\n  }\n\n  I18n =\n    (typeof props.i18n === 'function' ? await props.i18n() : props.i18n) ||\n    (locale == 'en'\n      ? i18n_en\n      : await fetchJSON(\n          `https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${locale}.json`,\n        ))\n\n  if (props.custom) {\n    for (let i in props.custom) {\n      i = parseInt(i)\n      const category = props.custom[i]\n      const prevCategory = props.custom[i - 1]\n\n      if (!category.emojis || !category.emojis.length) continue\n\n      category.id || (category.id = `custom_${i + 1}`)\n      category.name || (category.name = I18n.categories.custom)\n\n      if (prevCategory && !category.icon) {\n        category.target = prevCategory.target || prevCategory\n      }\n\n      Data.categories.push(category)\n\n      for (const emoji of category.emojis) {\n        Data.emojis[emoji.id] = emoji\n      }\n    }\n  }\n\n  if (props.categories) {\n    Data.categories = Data.originalCategories\n      .filter((c) => {\n        return props.categories.indexOf(c.id) != -1\n      })\n      .sort((c1, c2) => {\n        const i1 = props.categories.indexOf(c1.id)\n        const i2 = props.categories.indexOf(c2.id)\n\n        return i1 - i2\n      })\n  }\n\n  let latestVersionSupport = null\n  let noCountryFlags = null\n  if (set == 'native') {\n    latestVersionSupport = NativeSupport.latestVersion()\n    noCountryFlags = props.noCountryFlags || NativeSupport.noCountryFlags()\n  }\n\n  let categoryIndex = Data.categories.length\n  let resetSearchIndex = false\n  while (categoryIndex--) {\n    const category = Data.categories[categoryIndex]\n\n    if (category.id == 'frequent') {\n      let { maxFrequentRows, perLine } = props\n\n      maxFrequentRows =\n        maxFrequentRows >= 0\n          ? maxFrequentRows\n          : PickerProps.maxFrequentRows.value\n      perLine || (perLine = PickerProps.perLine.value)\n\n      category.emojis = FrequentlyUsed.get({ maxFrequentRows, perLine })\n    }\n\n    if (!category.emojis || !category.emojis.length) {\n      Data.categories.splice(categoryIndex, 1)\n      continue\n    }\n\n    const { categoryIcons } = props\n    if (categoryIcons) {\n      const icon = categoryIcons[category.id]\n      if (icon && !category.icon) {\n        category.icon = icon\n      }\n    }\n\n    let emojiIndex = category.emojis.length\n    while (emojiIndex--) {\n      const emojiId = category.emojis[emojiIndex]\n      const emoji = emojiId.id ? emojiId : Data.emojis[emojiId]\n\n      const ignore = () => {\n        category.emojis.splice(emojiIndex, 1)\n      }\n\n      if (\n        !emoji ||\n        (props.exceptEmojis && props.exceptEmojis.includes(emoji.id))\n      ) {\n        ignore()\n        continue\n      }\n\n      if (latestVersionSupport && emoji.version > latestVersionSupport) {\n        ignore()\n        continue\n      }\n\n      if (noCountryFlags && category.id == 'flags') {\n        if (!SafeFlags.includes(emoji.id)) {\n          ignore()\n          continue\n        }\n      }\n\n      if (!emoji.search) {\n        resetSearchIndex = true\n        emoji.search =\n          ',' +\n          [\n            [emoji.id, false],\n            [emoji.name, true],\n            [emoji.keywords, false],\n            [emoji.emoticons, false],\n          ]\n            .map(([strings, split]) => {\n              if (!strings) return\n              return (Array.isArray(strings) ? strings : [strings])\n                .map((string) => {\n                  return (split ? string.split(/[-|_|\\s]+/) : [string]).map(\n                    (s) => s.toLowerCase(),\n                  )\n                })\n                .flat()\n            })\n            .flat()\n            .filter((a) => a && a.trim())\n            .join(',')\n\n        if (emoji.emoticons) {\n          for (const emoticon of emoji.emoticons) {\n            if (Data.emoticons[emoticon]) continue\n            Data.emoticons[emoticon] = emoji.id\n          }\n        }\n\n        let skinIndex = 0\n        for (const skin of emoji.skins) {\n          if (!skin) continue\n          skinIndex++\n\n          const { native } = skin\n          if (native) {\n            Data.natives[native] = emoji.id\n            emoji.search += `,${native}`\n          }\n\n          const skinShortcodes =\n            skinIndex == 1 ? '' : `:skin-tone-${skinIndex}:`\n          skin.shortcodes = `:${emoji.id}:${skinShortcodes}`\n        }\n      }\n    }\n  }\n\n  if (resetSearchIndex) {\n    SearchIndex.reset()\n  }\n\n  initCallback()\n}\n\nexport function getProps(props, defaultProps, element) {\n  props || (props = {})\n\n  const _props = {}\n  for (let k in defaultProps) {\n    _props[k] = getProp(k, props, defaultProps, element)\n  }\n\n  return _props\n}\n\nexport function getProp(propName, props, defaultProps, element) {\n  const defaults = defaultProps[propName]\n  let value =\n    (element && element.getAttribute(propName)) ||\n    (props[propName] != null && props[propName] != undefined\n      ? props[propName]\n      : null)\n\n  if (!defaults) {\n    return value\n  }\n\n  if (\n    value != null &&\n    defaults.value &&\n    typeof defaults.value != typeof value\n  ) {\n    if (typeof defaults.value == 'boolean') {\n      value = value == 'false' ? false : true\n    } else {\n      value = defaults.value.constructor(value)\n    }\n  }\n\n  if (defaults.transform && value) {\n    value = defaults.transform(value)\n  }\n\n  if (\n    value == null ||\n    (defaults.choices && defaults.choices.indexOf(value) == -1)\n  ) {\n    value = defaults.value\n  }\n\n  return value\n}\n", "{\n  \"search\": \"Search\",\n  \"search_no_results_1\": \"Oh no!\",\n  \"search_no_results_2\": \"That emoji couldn’t be found\",\n  \"pick\": \"Pick an emoji…\",\n  \"add_custom\": \"Add custom emoji\",\n  \"categories\": {\n    \"activity\": \"Activity\",\n    \"custom\": \"Custom\",\n    \"flags\": \"Flags\",\n    \"foods\": \"Food & Drink\",\n    \"frequent\": \"Frequently used\",\n    \"nature\": \"Animals & Nature\",\n    \"objects\": \"Objects\",\n    \"people\": \"Smileys & People\",\n    \"places\": \"Travel & Places\",\n    \"search\": \"Search Results\",\n    \"symbols\": \"Symbols\"\n  },\n  \"skins\": {\n    \"choose\": \"Choose default skin tone\",\n    \"1\": \"Default\",\n    \"2\": \"Light\",\n    \"3\": \"Medium-Light\",\n    \"4\": \"Medium\",\n    \"5\": \"Medium-Dark\",\n    \"6\": \"Dark\"\n  }\n}\n", "export default {\n  autoFocus: {\n    value: false,\n  },\n  dynamicWidth: {\n    value: false,\n  },\n  emojiButtonColors: {\n    value: null,\n  },\n  emojiButtonRadius: {\n    value: '100%',\n  },\n  emojiButtonSize: {\n    value: 36,\n  },\n  emojiSize: {\n    value: 24,\n  },\n  emojiVersion: {\n    value: 15,\n    choices: [1, 2, 3, 4, 5, 11, 12, 12.1, 13, 13.1, 14, 15],\n  },\n  exceptEmojis: {\n    value: [],\n  },\n  icons: {\n    value: 'auto',\n    choices: ['auto', 'outline', 'solid'],\n  },\n  locale: {\n    value: 'en',\n    choices: [\n      'en',\n      'ar',\n      'be',\n      'cs',\n      'de',\n      'es',\n      'fa',\n      'fi',\n      'fr',\n      'hi',\n      'it',\n      'ja',\n      'ko',\n      'nl',\n      'pl',\n      'pt',\n      'ru',\n      'sa',\n      'tr',\n      'uk',\n      'vi',\n      'zh',\n    ],\n  },\n  maxFrequentRows: {\n    value: 4,\n  },\n  navPosition: {\n    value: 'top',\n    choices: ['top', 'bottom', 'none'],\n  },\n  noCountryFlags: {\n    value: false,\n  },\n  noResultsEmoji: {\n    value: null,\n  },\n  perLine: {\n    value: 9,\n  },\n  previewEmoji: {\n    value: null,\n  },\n  previewPosition: {\n    value: 'bottom',\n    choices: ['top', 'bottom', 'none'],\n  },\n  searchPosition: {\n    value: 'sticky',\n    choices: ['sticky', 'static', 'none'],\n  },\n  set: {\n    value: 'native',\n    choices: ['native', 'apple', 'facebook', 'google', 'twitter'],\n  },\n  skin: {\n    value: 1,\n    choices: [1, 2, 3, 4, 5, 6],\n  },\n  skinTonePosition: {\n    value: 'preview',\n    choices: ['preview', 'search', 'none'],\n  },\n  theme: {\n    value: 'auto',\n    choices: ['auto', 'light', 'dark'],\n  },\n\n  // Data\n  categories: null,\n  categoryIcons: null,\n  custom: null,\n  data: null,\n  i18n: null,\n\n  // Callbacks\n  getImageURL: null,\n  getSpritesheetURL: null,\n  onAddCustomEmoji: null,\n  onClickOutside: null,\n  onEmojiSelect: null,\n\n  // Deprecated\n  stickySearch: {\n    deprecated: true,\n    value: true,\n  },\n}\n", "const categories = {\n  activity: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z\" />\n      </svg>\n    ),\n  },\n\n  custom: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\">\n      <path d=\"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z\" />\n    </svg>\n  ),\n\n  flags: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z\" />\n      </svg>\n    ),\n  },\n\n  foods: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z\" />\n      </svg>\n    ),\n  },\n\n  frequent: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z\" />\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z\" />\n      </svg>\n    ),\n  },\n\n  nature: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8\" />\n        <path d=\"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\">\n        <path d=\"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z\" />\n      </svg>\n    ),\n  },\n\n  objects: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z\" />\n        <path d=\"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\">\n        <path d=\"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z\" />\n      </svg>\n    ),\n  },\n\n  people: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10\" />\n        <path d=\"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z\" />\n      </svg>\n    ),\n  },\n\n  places: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5\" />\n        <path d=\"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z\" />\n      </svg>\n    ),\n  },\n\n  symbols: {\n    outline: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n        <path d=\"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76\" />\n      </svg>\n    ),\n    solid: (\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\">\n        <path d=\"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z\" />\n      </svg>\n    ),\n  },\n}\n\nconst search = {\n  loupe: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z\" />\n    </svg>\n  ),\n\n  delete: (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\">\n      <path d=\"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z\" />\n    </svg>\n  ),\n}\n\nexport default { categories, search }\n", "export { default as Emoji } from './Emoji'\nexport { default as EmojiElement } from './EmojiElement'\n", "import { Data } from '../../config'\nimport { SearchIndex } from '../../helpers'\n\nexport default function Emoji(props) {\n  let { id, skin, emoji } = props\n\n  if (props.shortcodes) {\n    const matches = props.shortcodes.match(SearchIndex.SHORTCODES_REGEX)\n\n    if (matches) {\n      id = matches[1]\n\n      if (matches[2]) {\n        skin = matches[2]\n      }\n    }\n  }\n\n  emoji || (emoji = SearchIndex.get(id || props.native))\n  if (!emoji) return props.fallback\n\n  const emojiSkin = emoji.skins[skin - 1] || emoji.skins[0]\n\n  const imageSrc =\n    emojiSkin.src ||\n    (props.set != 'native' && !props.spritesheet\n      ? typeof props.getImageURL === 'function'\n        ? props.getImageURL(props.set, emojiSkin.unified)\n        : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/64/${emojiSkin.unified}.png`\n      : undefined)\n\n  const spritesheetSrc =\n    typeof props.getSpritesheetURL === 'function'\n      ? props.getSpritesheetURL(props.set)\n      : `https://cdn.jsdelivr.net/npm/emoji-datasource-${props.set}@15.0.1/img/${props.set}/sheets-256/64.png`\n\n  return (\n    <span class=\"emoji-mart-emoji\" data-emoji-set={props.set}>\n      {imageSrc ? (\n        <img\n          style={{\n            maxWidth: props.size || '1em',\n            maxHeight: props.size || '1em',\n            display: 'inline-block',\n          }}\n          alt={emojiSkin.native || emojiSkin.shortcodes}\n          src={imageSrc}\n        />\n      ) : props.set == 'native' ? (\n        <span\n          style={{\n            fontSize: props.size,\n            fontFamily:\n              '\"EmojiMart\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"Android Emoji\"',\n          }}\n        >\n          {emojiSkin.native}\n        </span>\n      ) : (\n        <span\n          style={{\n            display: 'block',\n            width: props.size,\n            height: props.size,\n            backgroundImage: `url(${spritesheetSrc})`,\n            backgroundSize: `${100 * Data.sheet.cols}% ${\n              100 * Data.sheet.rows\n            }%`,\n            backgroundPosition: `${\n              (100 / (Data.sheet.cols - 1)) * emojiSkin.x\n            }% ${(100 / (Data.sheet.rows - 1)) * emojiSkin.y}%`,\n          }}\n        ></span>\n      )}\n    </span>\n  )\n}\n", "import { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { HTMLElement } from '../HTMLElement'\nimport { Emoji } from '.'\nimport EmojiProps from './EmojiProps'\n\nexport default class EmojiElement extends HTMLElement {\n  static Props = EmojiProps\n\n  constructor(props) {\n    super(props)\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, EmojiProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init()\n    if (this.disconnected) return\n\n    render(<Emoji {...props} />, this)\n  }\n}\n\nif (typeof customElements !== 'undefined' && !customElements.get('em-emoji')) {\n  customElements.define('em-emoji', EmojiElement)\n}\n", "export { default as HTMLElement } from './HTMLElement'\nexport { default as ShadowElement } from './ShadowElement'\n", "// @ts-nocheck\nimport { getProp } from '../../config'\n\nconst WindowHTMLElement =\n  typeof window !== 'undefined' && window.HTMLElement\n    ? window.HTMLElement\n    : Object\n\nexport default class HTMLElement extends WindowHTMLElement {\n  static get observedAttributes() {\n    return Object.keys(this.Props)\n  }\n\n  constructor(props = {}) {\n    super()\n    this.props = props\n\n    if (props.parent || props.ref) {\n      let ref = null\n      const parent = props.parent || (ref = props.ref && props.ref.current)\n\n      if (ref) ref.innerHTML = ''\n      if (parent) parent.appendChild(this)\n    }\n  }\n\n  update(props = {}) {\n    for (let k in props) {\n      this.attributeChangedCallback(k, null, props[k])\n    }\n  }\n\n  attributeChangedCallback(attr, _, newValue) {\n    if (!this.component) return\n\n    const value = getProp(\n      attr,\n      { [attr]: newValue },\n      this.constructor.Props,\n      this,\n    )\n\n    if (this.component.componentWillReceiveProps) {\n      this.component.componentWillReceiveProps({ [attr]: value })\n    } else {\n      this.component.props[attr] = value\n      this.component.forceUpdate()\n    }\n  }\n\n  disconnectedCallback() {\n    this.disconnected = true\n\n    if (this.component && this.component.unregister) {\n      this.component.unregister()\n    }\n  }\n}\n", "// @ts-nocheck\nimport { HTMLElement } from '.'\n\nexport default class ShadowElement extends HTMLElement {\n  constructor(props, { styles } = {}) {\n    super(props)\n\n    this.setShadow()\n    this.injectStyles(styles)\n  }\n\n  setShadow() {\n    this.attachShadow({ mode: 'open' })\n  }\n\n  injectStyles(styles) {\n    if (!styles) return\n\n    const style = document.createElement('style')\n    style.textContent = styles\n\n    this.shadowRoot.insertBefore(style, this.shadowRoot.firstChild)\n  }\n}\n", "import PickerProps from '../Picker/PickerProps'\n\nexport default {\n  fallback: '',\n  id: '',\n  native: '',\n  shortcodes: '',\n  size: {\n    value: '',\n    transform: (value) => {\n      // If the value is a number, then we assume it’s a pixel value.\n      if (!/\\D/.test(value)) {\n        return `${value}px`\n      }\n\n      return value\n    },\n  },\n\n  // Shared\n  set: PickerProps.set,\n  skin: PickerProps.skin,\n}\n", "export { default as Navigation } from './Navigation'\n", "// @ts-nocheck\nimport { PureComponent } from 'preact/compat'\nimport { Data, I18n } from '../../config'\nimport Icons from '../../icons'\n\nconst THEME_ICONS = {\n  light: 'outline',\n  dark: 'solid',\n}\n\nexport default class Navigation extends PureComponent {\n  constructor() {\n    super()\n\n    this.categories = Data.categories.filter((category) => {\n      return !category.target\n    })\n\n    this.state = {\n      categoryId: this.categories[0].id,\n    }\n  }\n\n  renderIcon(category) {\n    const { icon } = category\n\n    if (icon) {\n      if (icon.svg) {\n        return (\n          <span\n            class=\"flex\"\n            dangerouslySetInnerHTML={{ __html: icon.svg }}\n          ></span>\n        )\n      }\n\n      if (icon.src) {\n        return <img src={icon.src} />\n      }\n    }\n\n    const categoryIcons =\n      Icons.categories[category.id] || Icons.categories.custom\n\n    const style =\n      this.props.icons == 'auto'\n        ? THEME_ICONS[this.props.theme]\n        : this.props.icons\n\n    return categoryIcons[style] || categoryIcons\n  }\n\n  render() {\n    let selectedCategoryIndex = null\n\n    return (\n      <nav\n        id=\"nav\"\n        class=\"padding\"\n        data-position={this.props.position}\n        dir={this.props.dir}\n      >\n        <div class=\"flex relative\">\n          {this.categories.map((category, i) => {\n            const title = category.name || I18n.categories[category.id]\n            const selected =\n              !this.props.unfocused && category.id == this.state.categoryId\n\n            if (selected) {\n              selectedCategoryIndex = i\n            }\n\n            return (\n              <button\n                aria-label={title}\n                aria-selected={selected || undefined}\n                title={title}\n                type=\"button\"\n                class=\"flex flex-grow flex-center\"\n                onMouseDown={(e) => e.preventDefault()}\n                onClick={() => {\n                  this.props.onClick({ category, i })\n                }}\n              >\n                {this.renderIcon(category)}\n              </button>\n            )\n          })}\n\n          <div\n            class=\"bar\"\n            style={{\n              width: `${100 / this.categories.length}%`,\n              opacity: selectedCategoryIndex == null ? 0 : 1,\n              transform:\n                this.props.dir === 'rtl'\n                  ? `scaleX(-1) translateX(${selectedCategoryIndex * 100}%)`\n                  : `translateX(${selectedCategoryIndex * 100}%)`,\n            }}\n          ></div>\n        </div>\n      </nav>\n    )\n  }\n}\n", "import{useState as n,useReducer as t,useEffect as e,useLayoutEffect as r,useRef as u,useImperative<PERSON>andle as o,useMemo as i,use<PERSON><PERSON>back as l,useContext as c,useDebugValue as f}from\"preact/hooks\";export*from\"preact/hooks\";import{Component as a,createElement as s,options as h,toChildArray as d,Fragment as v,render as p,hydrate as m,cloneElement as y,createRef as b,createContext as _}from\"preact\";export{createElement,createContext,createRef,Fragment,Component}from\"preact\";function S(n,t){for(var e in t)n[e]=t[e];return n}function C(n,t){for(var e in n)if(\"__source\"!==e&&!(e in t))return!0;for(var r in t)if(\"__source\"!==r&&n[r]!==t[r])return!0;return!1}function E(n){this.props=n}function g(n,t){function e(n){var e=this.props.ref,r=e==n.ref;return!r&&e&&(e.call?e(null):e.current=null),t?!t(this.props,n)||!r:C(this.props,n)}function r(t){return this.shouldComponentUpdate=e,s(n,t)}return r.displayName=\"Memo(\"+(n.displayName||n.name)+\")\",r.prototype.isReactComponent=!0,r.__f=!0,r}(E.prototype=new a).isPureReactComponent=!0,E.prototype.shouldComponentUpdate=function(n,t){return C(this.props,n)||C(this.state,t)};var w=h.__b;h.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),w&&w(n)};var R=\"undefined\"!=typeof Symbol&&Symbol.for&&Symbol.for(\"react.forward_ref\")||3911;function x(n){function t(t,e){var r=S({},t);return delete r.ref,n(r,(e=t.ref||e)&&(\"object\"!=typeof e||\"current\"in e)?e:null)}return t.$$typeof=R,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName=\"ForwardRef(\"+(n.displayName||n.name)+\")\",t}var N=function(n,t){return null==n?null:d(d(n).map(t))},k={map:N,forEach:N,count:function(n){return n?d(n).length:0},only:function(n){var t=d(n);if(1!==t.length)throw\"Children.only\";return t[0]},toArray:d},A=h.__e;h.__e=function(n,t,e){if(n.then)for(var r,u=t;u=u.__;)if((r=u.__c)&&r.__c)return null==t.__e&&(t.__e=e.__e,t.__k=e.__k),r.__c(n,t);A(n,t,e)};var O=h.unmount;function L(){this.__u=0,this.t=null,this.__b=null}function U(n){var t=n.__.__c;return t&&t.__e&&t.__e(n)}function F(n){var t,e,r;function u(u){if(t||(t=n()).then(function(n){e=n.default||n},function(n){r=n}),r)throw r;if(!e)throw t;return s(e,u)}return u.displayName=\"Lazy\",u.__f=!0,u}function M(){this.u=null,this.o=null}h.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&!0===n.__h&&(n.type=null),O&&O(n)},(L.prototype=new a).__c=function(n,t){var e=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(e);var u=U(r.__v),o=!1,i=function(){o||(o=!0,e.__R=null,u?u(l):l())};e.__R=i;var l=function(){if(!--r.__u){if(r.state.__e){var n=r.state.__e;r.__v.__k[0]=function n(t,e,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return n(t,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(n,n.__c.__P,n.__c.__O)}var t;for(r.setState({__e:r.__b=null});t=r.t.pop();)t.forceUpdate()}},c=!0===t.__h;r.__u++||c||r.setState({__e:r.__b=r.__v.__k[0]}),n.then(i,i)},L.prototype.componentWillUnmount=function(){this.t=[]},L.prototype.render=function(n,t){if(this.__b){if(this.__v.__k){var e=document.createElement(\"div\"),r=this.__v.__k[0].__c;this.__v.__k[0]=function n(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){\"function\"==typeof n.__c&&n.__c()}),t.__c.__H=null),null!=(t=S({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return n(t,e,r)})),t}(this.__b,e,r.__O=r.__P)}this.__b=null}var u=t.__e&&s(v,null,n.fallback);return u&&(u.__h=null),[s(v,null,t.__e?null:n.children),u]};var T=function(n,t,e){if(++e[1]===e[0]&&n.o.delete(t),n.props.revealOrder&&(\"t\"!==n.props.revealOrder[0]||!n.o.size))for(e=n.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.u=e=e[2]}};function D(n){return this.getChildContext=function(){return n.context},n.children}function I(n){var t=this,e=n.i;t.componentWillUnmount=function(){p(null,t.l),t.l=null,t.i=null},t.i&&t.i!==e&&t.componentWillUnmount(),n.__v?(t.l||(t.i=e,t.l={nodeType:1,parentNode:e,childNodes:[],appendChild:function(n){this.childNodes.push(n),t.i.appendChild(n)},insertBefore:function(n,e){this.childNodes.push(n),t.i.appendChild(n)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),t.i.removeChild(n)}}),p(s(D,{context:t.context},n.__v),t.l)):t.l&&t.componentWillUnmount()}function W(n,t){return s(I,{__v:n,i:t})}(M.prototype=new a).__e=function(n){var t=this,e=U(t.__v),r=t.o.get(n);return r[0]++,function(u){var o=function(){t.props.revealOrder?(r.push(u),T(t,n,r)):u()};e?e(o):o()}},M.prototype.render=function(n){this.u=null,this.o=new Map;var t=d(n.children);n.revealOrder&&\"b\"===n.revealOrder[0]&&t.reverse();for(var e=t.length;e--;)this.o.set(t[e],this.u=[1,0,this.u]);return n.children},M.prototype.componentDidUpdate=M.prototype.componentDidMount=function(){var n=this;this.o.forEach(function(t,e){T(n,e,t)})};var j=\"undefined\"!=typeof Symbol&&Symbol.for&&Symbol.for(\"react.element\")||60103,P=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,V=\"undefined\"!=typeof document,z=function(n){return(\"undefined\"!=typeof Symbol&&\"symbol\"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(n)};function B(n,t,e){return null==t.__k&&(t.textContent=\"\"),p(n,t),\"function\"==typeof e&&e(),n?n.__c:null}function $(n,t,e){return m(n,t),\"function\"==typeof e&&e(),n?n.__c:null}a.prototype.isReactComponent={},[\"componentWillMount\",\"componentWillReceiveProps\",\"componentWillUpdate\"].forEach(function(n){Object.defineProperty(a.prototype,n,{configurable:!0,get:function(){return this[\"UNSAFE_\"+n]},set:function(t){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:t})}})});var H=h.event;function Z(){}function Y(){return this.cancelBubble}function q(){return this.defaultPrevented}h.event=function(n){return H&&(n=H(n)),n.persist=Z,n.isPropagationStopped=Y,n.isDefaultPrevented=q,n.nativeEvent=n};var G,J={configurable:!0,get:function(){return this.class}},K=h.vnode;h.vnode=function(n){var t=n.type,e=n.props,r=e;if(\"string\"==typeof t){var u=-1===t.indexOf(\"-\");for(var o in r={},e){var i=e[o];V&&\"children\"===o&&\"noscript\"===t||\"value\"===o&&\"defaultValue\"in e&&null==i||(\"defaultValue\"===o&&\"value\"in e&&null==e.value?o=\"value\":\"download\"===o&&!0===i?i=\"\":/ondoubleclick/i.test(o)?o=\"ondblclick\":/^onchange(textarea|input)/i.test(o+t)&&!z(e.type)?o=\"oninput\":/^onfocus$/i.test(o)?o=\"onfocusin\":/^onblur$/i.test(o)?o=\"onfocusout\":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():u&&P.test(o)?o=o.replace(/[A-Z0-9]/,\"-$&\").toLowerCase():null===i&&(i=void 0),r[o]=i)}\"select\"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=d(e.children).forEach(function(n){n.props.selected=-1!=r.value.indexOf(n.props.value)})),\"select\"==t&&null!=r.defaultValue&&(r.value=d(e.children).forEach(function(n){n.props.selected=r.multiple?-1!=r.defaultValue.indexOf(n.props.value):r.defaultValue==n.props.value})),n.props=r,e.class!=e.className&&(J.enumerable=\"className\"in e,null!=e.className&&(r.class=e.className),Object.defineProperty(r,\"className\",J))}n.$$typeof=j,K&&K(n)};var Q=h.__r;h.__r=function(n){Q&&Q(n),G=n.__c};var X={ReactCurrentDispatcher:{current:{readContext:function(n){return G.__n[n.__c].props.value}}}},nn=\"17.0.2\";function tn(n){return s.bind(null,n)}function en(n){return!!n&&n.$$typeof===j}function rn(n){return en(n)?y.apply(null,arguments):n}function un(n){return!!n.__k&&(p(null,n),!0)}function on(n){return n&&(n.base||1===n.nodeType&&n)||null}var ln=function(n,t){return n(t)},cn=function(n,t){return n(t)},fn=v;export default{useState:n,useReducer:t,useEffect:e,useLayoutEffect:r,useRef:u,useImperativeHandle:o,useMemo:i,useCallback:l,useContext:c,useDebugValue:f,version:\"17.0.2\",Children:k,render:B,hydrate:$,unmountComponentAtNode:un,createPortal:W,createElement:s,createContext:_,createFactory:tn,cloneElement:rn,createRef:b,Fragment:v,isValidElement:en,findDOMNode:on,Component:a,PureComponent:E,memo:g,forwardRef:x,flushSync:cn,unstable_batchedUpdates:ln,StrictMode:v,Suspense:L,SuspenseList:M,lazy:F,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:X};export{nn as version,k as Children,B as render,$ as hydrate,un as unmountComponentAtNode,W as createPortal,tn as createFactory,rn as cloneElement,en as isValidElement,on as findDOMNode,E as PureComponent,g as memo,x as forwardRef,cn as flushSync,ln as unstable_batchedUpdates,fn as StrictMode,L as Suspense,M as SuspenseList,F as lazy,X as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED};\n//# sourceMappingURL=compat.module.js.map\n", "/**\r\n * Assign properties from `props` to `obj`\r\n * @template O, P The obj and props types\r\n * @param {O} obj The object to copy properties to\r\n * @param {P} props The object to copy properties from\r\n * @returns {O & P}\r\n */\r\nexport function assign(obj, props) {\r\n\tfor (let i in props) obj[i] = props[i];\r\n\treturn /** @type {O & P} */ (obj);\r\n}\r\n\r\n/**\r\n * Check if two objects have a different shape\r\n * @param {object} a\r\n * @param {object} b\r\n * @returns {boolean}\r\n */\r\nexport function shallowDiffers(a, b) {\r\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\r\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\r\n\treturn false;\r\n}\r\n\r\nexport function removeNode(node) {\r\n\tlet parentNode = node.parentNode;\r\n\tif (parentNode) parentNode.removeChild(node);\r\n}\r\n", "import { Component } from 'preact';\r\nimport { shallowDiffers } from './util';\r\n\r\n/**\r\n * Component class with a predefined `shouldComponentUpdate` implementation\r\n */\r\nexport function PureComponent(p) {\r\n\tthis.props = p;\r\n}\r\nPureComponent.prototype = new Component();\r\n// Some third-party libraries check if this property is present\r\nPureComponent.prototype.isPureReactComponent = true;\r\nPureComponent.prototype.shouldComponentUpdate = function(props, state) {\r\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\r\n};\r\n", "import { createElement } from 'preact';\r\nimport { shallowDiffers } from './util';\r\n\r\n/**\r\n * Memoize a component, so that it only updates when the props actually have\r\n * changed. This was previously known as `React.pure`.\r\n * @param {import('./internal').FunctionComponent} c functional component\r\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\r\n * @returns {import('./internal').FunctionComponent}\r\n */\r\nexport function memo(c, comparer) {\r\n\tfunction shouldUpdate(nextProps) {\r\n\t\tlet ref = this.props.ref;\r\n\t\tlet updateRef = ref == nextProps.ref;\r\n\t\tif (!updateRef && ref) {\r\n\t\t\tref.call ? ref(null) : (ref.current = null);\r\n\t\t}\r\n\r\n\t\tif (!comparer) {\r\n\t\t\treturn shallowDiffers(this.props, nextProps);\r\n\t\t}\r\n\r\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\r\n\t}\r\n\r\n\tfunction Memoed(props) {\r\n\t\tthis.shouldComponentUpdate = shouldUpdate;\r\n\t\treturn createElement(c, props);\r\n\t}\r\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\r\n\tMemoed.prototype.isReactComponent = true;\r\n\tMemoed._forwarded = true;\r\n\treturn Memoed;\r\n}\r\n", "import { options } from 'preact';\r\nimport { assign } from './util';\r\n\r\nlet oldDiffHook = options._diff;\r\noptions._diff = vnode => {\r\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\r\n\t\tvnode.props.ref = vnode.ref;\r\n\t\tvnode.ref = null;\r\n\t}\r\n\tif (oldDiffHook) oldDiffHook(vnode);\r\n};\r\n\r\nexport const REACT_FORWARD_SYMBOL =\r\n\t(typeof Symbol != 'undefined' &&\r\n\t\tSymbol.for &&\r\n\t\tSymbol.for('react.forward_ref')) ||\r\n\t0xf47;\r\n\r\n/**\r\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\r\n * wrap components. Using `forwardRef` there is an easy way to get a reference\r\n * of the wrapped component instead of one of the wrapper itself.\r\n * @param {import('./index').ForwardFn} fn\r\n * @returns {import('./internal').FunctionComponent}\r\n */\r\nexport function forwardRef(fn) {\r\n\t// We always have ref in props.ref, except for\r\n\t// mobx-react. It will call this function directly\r\n\t// and always pass ref as the second argument.\r\n\tfunction Forwarded(props, ref) {\r\n\t\tlet clone = assign({}, props);\r\n\t\tdelete clone.ref;\r\n\t\tref = props.ref || ref;\r\n\t\treturn fn(\r\n\t\t\tclone,\r\n\t\t\t!ref || (typeof ref === 'object' && !('current' in ref)) ? null : ref\r\n\t\t);\r\n\t}\r\n\r\n\t// mobx-react checks for this being present\r\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\r\n\t// mobx-react heavily relies on implementation details.\r\n\t// It expects an object here with a `render` property,\r\n\t// and prototype.render will fail. Without this\r\n\t// mobx-react throws.\r\n\tForwarded.render = Forwarded;\r\n\r\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\r\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\r\n\treturn Forwarded;\r\n}\r\n", "import { toChildArray } from 'preact';\r\n\r\nconst mapFn = (children, fn) => {\r\n\tif (children == null) return null;\r\n\treturn toChildArray(toChildArray(children).map(fn));\r\n};\r\n\r\n// This API is completely unnecessary for Preact, so it's basically passthrough.\r\nexport const Children = {\r\n\tmap: mapFn,\r\n\tforEach: mapFn,\r\n\tcount(children) {\r\n\t\treturn children ? toChildArray(children).length : 0;\r\n\t},\r\n\tonly(children) {\r\n\t\tconst normalized = toChildArray(children);\r\n\t\tif (normalized.length !== 1) throw 'Children.only';\r\n\t\treturn normalized[0];\r\n\t},\r\n\ttoArray: toChildArray\r\n};\r\n", "import { Component, createElement, options, Fragment } from 'preact';\r\nimport { assign } from './util';\r\n\r\nconst oldCatchError = options._catchError;\r\noptions._catchError = function(error, newVNode, oldVNode) {\r\n\tif (error.then) {\r\n\t\t/** @type {import('./internal').Component} */\r\n\t\tlet component;\r\n\t\tlet vnode = newVNode;\r\n\r\n\t\tfor (; (vnode = vnode._parent); ) {\r\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\r\n\t\t\t\tif (newVNode._dom == null) {\r\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\r\n\t\t\t\t\tnewVNode._children = oldVNode._children;\r\n\t\t\t\t}\r\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\r\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\toldCatchError(error, newVNode, oldVNode);\r\n};\r\n\r\nconst oldUnmount = options.unmount;\r\noptions.unmount = function(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tconst component = vnode._component;\r\n\tif (component && component._onResolve) {\r\n\t\tcomponent._onResolve();\r\n\t}\r\n\r\n\t// if the component is still hydrating\r\n\t// most likely it is because the component is suspended\r\n\t// we set the vnode.type as `null` so that it is not a typeof function\r\n\t// so the unmount will remove the vnode._dom\r\n\tif (component && vnode._hydrating === true) {\r\n\t\tvnode.type = null;\r\n\t}\r\n\r\n\tif (oldUnmount) oldUnmount(vnode);\r\n};\r\n\r\nfunction detachedClone(vnode, detachedParent, parentDom) {\r\n\tif (vnode) {\r\n\t\tif (vnode._component && vnode._component.__hooks) {\r\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\r\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\r\n\t\t\t});\r\n\r\n\t\t\tvnode._component.__hooks = null;\r\n\t\t}\r\n\r\n\t\tvnode = assign({}, vnode);\r\n\t\tif (vnode._component != null) {\r\n\t\t\tif (vnode._component._parentDom === parentDom) {\r\n\t\t\t\tvnode._component._parentDom = detachedParent;\r\n\t\t\t}\r\n\t\t\tvnode._component = null;\r\n\t\t}\r\n\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\r\n\t\t\t);\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\nfunction removeOriginal(vnode, detachedParent, originalParent) {\r\n\tif (vnode) {\r\n\t\tvnode._original = null;\r\n\t\tvnode._children =\r\n\t\t\tvnode._children &&\r\n\t\t\tvnode._children.map(child =>\r\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\r\n\t\t\t);\r\n\r\n\t\tif (vnode._component) {\r\n\t\t\tif (vnode._component._parentDom === detachedParent) {\r\n\t\t\t\tif (vnode._dom) {\r\n\t\t\t\t\toriginalParent.insertBefore(vnode._dom, vnode._nextDom);\r\n\t\t\t\t}\r\n\t\t\t\tvnode._component._force = true;\r\n\t\t\t\tvnode._component._parentDom = originalParent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn vnode;\r\n}\r\n\r\n// having custom inheritance instead of a class here saves a lot of bytes\r\nexport function Suspense() {\r\n\t// we do not call super here to golf some bytes...\r\n\tthis._pendingSuspensionCount = 0;\r\n\tthis._suspenders = null;\r\n\tthis._detachOnNextRender = null;\r\n}\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspense.prototype = new Component();\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {Promise} promise The thrown promise\r\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\r\n */\r\nSuspense.prototype._childDidSuspend = function(promise, suspendingVNode) {\r\n\tconst suspendingComponent = suspendingVNode._component;\r\n\r\n\t/** @type {import('./internal').SuspenseComponent} */\r\n\tconst c = this;\r\n\r\n\tif (c._suspenders == null) {\r\n\t\tc._suspenders = [];\r\n\t}\r\n\tc._suspenders.push(suspendingComponent);\r\n\r\n\tconst resolve = suspended(c._vnode);\r\n\r\n\tlet resolved = false;\r\n\tconst onResolved = () => {\r\n\t\tif (resolved) return;\r\n\r\n\t\tresolved = true;\r\n\t\tsuspendingComponent._onResolve = null;\r\n\r\n\t\tif (resolve) {\r\n\t\t\tresolve(onSuspensionComplete);\r\n\t\t} else {\r\n\t\t\tonSuspensionComplete();\r\n\t\t}\r\n\t};\r\n\r\n\tsuspendingComponent._onResolve = onResolved;\r\n\r\n\tconst onSuspensionComplete = () => {\r\n\t\tif (!--c._pendingSuspensionCount) {\r\n\t\t\t// If the suspension was during hydration we don't need to restore the\r\n\t\t\t// suspended children into the _children array\r\n\t\t\tif (c.state._suspended) {\r\n\t\t\t\tconst suspendedVNode = c.state._suspended;\r\n\t\t\t\tc._vnode._children[0] = removeOriginal(\r\n\t\t\t\t\tsuspendedVNode,\r\n\t\t\t\t\tsuspendedVNode._component._parentDom,\r\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\r\n\r\n\t\t\tlet suspended;\r\n\t\t\twhile ((suspended = c._suspenders.pop())) {\r\n\t\t\t\tsuspended.forceUpdate();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * We do not set `suspended: true` during hydration because we want the actual markup\r\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\r\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\r\n\t */\r\n\tconst wasHydrating = suspendingVNode._hydrating === true;\r\n\tif (!c._pendingSuspensionCount++ && !wasHydrating) {\r\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\r\n\t}\r\n\tpromise.then(onResolved, onResolved);\r\n};\r\n\r\nSuspense.prototype.componentWillUnmount = function() {\r\n\tthis._suspenders = [];\r\n};\r\n\r\n/**\r\n * @this {import('./internal').SuspenseComponent}\r\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\r\n * @param {import('./internal').SuspenseState} state\r\n */\r\nSuspense.prototype.render = function(props, state) {\r\n\tif (this._detachOnNextRender) {\r\n\t\t// When the Suspense's _vnode was created by a call to createVNode\r\n\t\t// (i.e. due to a setState further up in the tree)\r\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\r\n\t\tif (this._vnode._children) {\r\n\t\t\tconst detachedParent = document.createElement('div');\r\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\r\n\t\t\tthis._vnode._children[0] = detachedClone(\r\n\t\t\t\tthis._detachOnNextRender,\r\n\t\t\t\tdetachedParent,\r\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tthis._detachOnNextRender = null;\r\n\t}\r\n\r\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\r\n\t/** @type {import('./internal').VNode} */\r\n\tconst fallback =\r\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\r\n\tif (fallback) fallback._hydrating = null;\r\n\r\n\treturn [\r\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\r\n\t\tfallback\r\n\t];\r\n};\r\n\r\n/**\r\n * Checks and calls the parent component's _suspended method, passing in the\r\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\r\n * that one of its children/descendants suspended.\r\n *\r\n * The parent MAY return a callback. The callback will get called when the\r\n * suspension resolves, notifying the parent of the fact.\r\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\r\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\r\n * This is a way for the parent to delay unsuspending.\r\n *\r\n * If the parent does not return a callback then the resolved vnode\r\n * gets unsuspended immediately when it resolves.\r\n *\r\n * @param {import('./internal').VNode} vnode\r\n * @returns {((unsuspend: () => void) => void)?}\r\n */\r\nexport function suspended(vnode) {\r\n\t/** @type {import('./internal').Component} */\r\n\tlet component = vnode._parent._component;\r\n\treturn component && component._suspended && component._suspended(vnode);\r\n}\r\n\r\nexport function lazy(loader) {\r\n\tlet prom;\r\n\tlet component;\r\n\tlet error;\r\n\r\n\tfunction Lazy(props) {\r\n\t\tif (!prom) {\r\n\t\t\tprom = loader();\r\n\t\t\tprom.then(\r\n\t\t\t\texports => {\r\n\t\t\t\t\tcomponent = exports.default || exports;\r\n\t\t\t\t},\r\n\t\t\t\te => {\r\n\t\t\t\t\terror = e;\r\n\t\t\t\t}\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error;\r\n\t\t}\r\n\r\n\t\tif (!component) {\r\n\t\t\tthrow prom;\r\n\t\t}\r\n\r\n\t\treturn createElement(component, props);\r\n\t}\r\n\r\n\tLazy.displayName = 'Lazy';\r\n\tLazy._forwarded = true;\r\n\treturn Lazy;\r\n}\r\n", "import { Component, toChildArray } from 'preact';\r\nimport { suspended } from './suspense.js';\r\n\r\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\r\nconst SUSPENDED_COUNT = 0;\r\nconst RESOLVED_COUNT = 1;\r\nconst NEXT_NODE = 2;\r\n\r\n// Having custom inheritance instead of a class here saves a lot of bytes.\r\nexport function SuspenseList() {\r\n\tthis._next = null;\r\n\tthis._map = null;\r\n}\r\n\r\n// Mark one of child's earlier suspensions as resolved.\r\n// Some pending callbacks may become callable due to this\r\n// (e.g. the last suspended descendant gets resolved when\r\n// revealOrder === 'together'). Process those callbacks as well.\r\nconst resolve = (list, child, node) => {\r\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\r\n\t\t// The number a child (or any of its descendants) has been suspended\r\n\t\t// matches the number of times it's been resolved. Therefore we\r\n\t\t// mark the child as completely resolved by deleting it from ._map.\r\n\t\t// This is used to figure out when *all* children have been completely\r\n\t\t// resolved when revealOrder is 'together'.\r\n\t\tlist._map.delete(child);\r\n\t}\r\n\r\n\t// If revealOrder is falsy then we can do an early exit, as the\r\n\t// callbacks won't get queued in the node anyway.\r\n\t// If revealOrder is 'together' then also do an early exit\r\n\t// if all suspended descendants have not yet been resolved.\r\n\tif (\r\n\t\t!list.props.revealOrder ||\r\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\r\n\t) {\r\n\t\treturn;\r\n\t}\r\n\r\n\t// Walk the currently suspended children in order, calling their\r\n\t// stored callbacks on the way. Stop if we encounter a child that\r\n\t// has not been completely resolved yet.\r\n\tnode = list._next;\r\n\twhile (node) {\r\n\t\twhile (node.length > 3) {\r\n\t\t\tnode.pop()();\r\n\t\t}\r\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\r\n\t\t\tbreak;\r\n\t\t}\r\n\t\tlist._next = node = node[NEXT_NODE];\r\n\t}\r\n};\r\n\r\n// Things we do here to save some bytes but are not proper JS inheritance:\r\n// - call `new Component()` as the prototype\r\n// - do not set `Suspense.prototype.constructor` to `Suspense`\r\nSuspenseList.prototype = new Component();\r\n\r\nSuspenseList.prototype._suspended = function(child) {\r\n\tconst list = this;\r\n\tconst delegated = suspended(list._vnode);\r\n\r\n\tlet node = list._map.get(child);\r\n\tnode[SUSPENDED_COUNT]++;\r\n\r\n\treturn unsuspend => {\r\n\t\tconst wrappedUnsuspend = () => {\r\n\t\t\tif (!list.props.revealOrder) {\r\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\r\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\r\n\t\t\t\tunsuspend();\r\n\t\t\t} else {\r\n\t\t\t\tnode.push(unsuspend);\r\n\t\t\t\tresolve(list, child, node);\r\n\t\t\t}\r\n\t\t};\r\n\t\tif (delegated) {\r\n\t\t\tdelegated(wrappedUnsuspend);\r\n\t\t} else {\r\n\t\t\twrappedUnsuspend();\r\n\t\t}\r\n\t};\r\n};\r\n\r\nSuspenseList.prototype.render = function(props) {\r\n\tthis._next = null;\r\n\tthis._map = new Map();\r\n\r\n\tconst children = toChildArray(props.children);\r\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\r\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\r\n\t\t// then flip the child list around so that the last child will be\r\n\t\t// the first in the linked list.\r\n\t\tchildren.reverse();\r\n\t}\r\n\t// Build the linked list. Iterate through the children in reverse order\r\n\t// so that `_next` points to the first linked list node to be resolved.\r\n\tfor (let i = children.length; i--; ) {\r\n\t\t// Create a new linked list node as an array of form:\r\n\t\t// \t[suspended_count, resolved_count, next_node]\r\n\t\t// where suspended_count and resolved_count are numeric counters for\r\n\t\t// keeping track how many times a node has been suspended and resolved.\r\n\t\t//\r\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\r\n\t\t// processing callbacks until componentDidMount has been called. In a sense\r\n\t\t// node is suspended at least until componentDidMount gets called!\r\n\t\t//\r\n\t\t// Pending callbacks are added to the end of the node:\r\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\r\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\r\n\t}\r\n\treturn props.children;\r\n};\r\n\r\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function() {\r\n\t// Iterate through all children after mounting for two reasons:\r\n\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\r\n\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\r\n\t//    The nodes can now be completely consumed from the linked list.\r\n\t// 2. Handle nodes that might have gotten resolved between render and\r\n\t//    componentDidMount.\r\n\tthis._map.forEach((node, child) => {\r\n\t\tresolve(this, child, node);\r\n\t});\r\n};\r\n", "import { createElement, render } from 'preact';\r\n\r\n/**\r\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\r\n */\r\nfunction ContextProvider(props) {\r\n\tthis.getChildContext = () => props.context;\r\n\treturn props.children;\r\n}\r\n\r\n/**\r\n * Portal component\r\n * @this {import('./internal').Component}\r\n * @param {object | null | undefined} props\r\n *\r\n * TODO: use createRoot() instead of fake root\r\n */\r\nfunction Portal(props) {\r\n\tconst _this = this;\r\n\tlet container = props._container;\r\n\r\n\t_this.componentWillUnmount = function() {\r\n\t\trender(null, _this._temp);\r\n\t\t_this._temp = null;\r\n\t\t_this._container = null;\r\n\t};\r\n\r\n\t// When we change container we should clear our old container and\r\n\t// indicate a new mount.\r\n\tif (_this._container && _this._container !== container) {\r\n\t\t_this.componentWillUnmount();\r\n\t}\r\n\r\n\t// When props.vnode is undefined/false/null we are dealing with some kind of\r\n\t// conditional vnode. This should not trigger a render.\r\n\tif (props._vnode) {\r\n\t\tif (!_this._temp) {\r\n\t\t\t_this._container = container;\r\n\r\n\t\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\r\n\t\t\t_this._temp = {\r\n\t\t\t\tnodeType: 1,\r\n\t\t\t\tparentNode: container,\r\n\t\t\t\tchildNodes: [],\r\n\t\t\t\tappendChild(child) {\r\n\t\t\t\t\tthis.childNodes.push(child);\r\n\t\t\t\t\t_this._container.appendChild(child);\r\n\t\t\t\t},\r\n\t\t\t\tinsertBefore(child, before) {\r\n\t\t\t\t\tthis.childNodes.push(child);\r\n\t\t\t\t\t_this._container.appendChild(child);\r\n\t\t\t\t},\r\n\t\t\t\tremoveChild(child) {\r\n\t\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\r\n\t\t\t\t\t_this._container.removeChild(child);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Render our wrapping element into temp.\r\n\t\trender(\r\n\t\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\r\n\t\t\t_this._temp\r\n\t\t);\r\n\t}\r\n\t// When we come from a conditional render, on a mounted\r\n\t// portal we should clear the DOM.\r\n\telse if (_this._temp) {\r\n\t\t_this.componentWillUnmount();\r\n\t}\r\n}\r\n\r\n/**\r\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\r\n * @param {import('./internal').VNode} vnode The vnode to render\r\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\r\n */\r\nexport function createPortal(vnode, container) {\r\n\treturn createElement(Portal, { _vnode: vnode, _container: container });\r\n}\r\n", "import {\r\n\trender as preactRender,\r\n\thydrate as preactHydrate,\r\n\toptions,\r\n\ttoChildArray,\r\n\tComponent\r\n} from 'preact';\r\n\r\nexport const REACT_ELEMENT_TYPE =\r\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\r\n\t0xeac7;\r\n\r\nconst CAMEL_PROPS = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\r\n\r\nconst IS_DOM = typeof document !== 'undefined';\r\n\r\n// Input types for which onchange should not be converted to oninput.\r\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\r\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\r\nconst onChangeInputType = type =>\r\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\r\n\t\t? /fil|che|rad/i\r\n\t\t: /fil|che|ra/i\r\n\t).test(type);\r\n\r\n// Some libraries like `react-virtualized` explicitly check for this.\r\nComponent.prototype.isReactComponent = {};\r\n\r\n// `UNSAFE_*` lifecycle hooks\r\n// Preact only ever invokes the unprefixed methods.\r\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\r\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\r\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\r\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\r\n// See https://github.com/preactjs/preact/issues/1941\r\n[\r\n\t'componentWillMount',\r\n\t'componentWillReceiveProps',\r\n\t'componentWillUpdate'\r\n].forEach(key => {\r\n\tObject.defineProperty(Component.prototype, key, {\r\n\t\tconfigurable: true,\r\n\t\tget() {\r\n\t\t\treturn this['UNSAFE_' + key];\r\n\t\t},\r\n\t\tset(v) {\r\n\t\t\tObject.defineProperty(this, key, {\r\n\t\t\t\tconfigurable: true,\r\n\t\t\t\twritable: true,\r\n\t\t\t\tvalue: v\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n});\r\n\r\n/**\r\n * Proxy render() since React returns a Component reference.\r\n * @param {import('./internal').VNode} vnode VNode tree to render\r\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\r\n * @param {() => void} [callback] Optional callback that will be called after rendering\r\n * @returns {import('./internal').Component | null} The root component reference or null\r\n */\r\nexport function render(vnode, parent, callback) {\r\n\t// React destroys any existing DOM nodes, see #1727\r\n\t// ...but only on the first render, see #1828\r\n\tif (parent._children == null) {\r\n\t\tparent.textContent = '';\r\n\t}\r\n\r\n\tpreactRender(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nexport function hydrate(vnode, parent, callback) {\r\n\tpreactHydrate(vnode, parent);\r\n\tif (typeof callback == 'function') callback();\r\n\r\n\treturn vnode ? vnode._component : null;\r\n}\r\n\r\nlet oldEventHook = options.event;\r\noptions.event = e => {\r\n\tif (oldEventHook) e = oldEventHook(e);\r\n\te.persist = empty;\r\n\te.isPropagationStopped = isPropagationStopped;\r\n\te.isDefaultPrevented = isDefaultPrevented;\r\n\treturn (e.nativeEvent = e);\r\n};\r\n\r\nfunction empty() {}\r\n\r\nfunction isPropagationStopped() {\r\n\treturn this.cancelBubble;\r\n}\r\n\r\nfunction isDefaultPrevented() {\r\n\treturn this.defaultPrevented;\r\n}\r\n\r\nlet classNameDescriptor = {\r\n\tconfigurable: true,\r\n\tget() {\r\n\t\treturn this.class;\r\n\t}\r\n};\r\n\r\nlet oldVNodeHook = options.vnode;\r\noptions.vnode = vnode => {\r\n\tlet type = vnode.type;\r\n\tlet props = vnode.props;\r\n\tlet normalizedProps = props;\r\n\r\n\t// only normalize props on Element nodes\r\n\tif (typeof type === 'string') {\r\n\t\tconst nonCustomElement = type.indexOf('-') === -1;\r\n\t\tnormalizedProps = {};\r\n\r\n\t\tfor (let i in props) {\r\n\t\t\tlet value = props[i];\r\n\r\n\t\t\tif (IS_DOM && i === 'children' && type === 'noscript') {\r\n\t\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\telse if (i === 'value' && 'defaultValue' in props && value == null) {\r\n\t\t\t\t// Skip applying value if it is null/undefined and we already set\r\n\t\t\t\t// a default value\r\n\t\t\t\tcontinue;\r\n\t\t\t} else if (\r\n\t\t\t\ti === 'defaultValue' &&\r\n\t\t\t\t'value' in props &&\r\n\t\t\t\tprops.value == null\r\n\t\t\t) {\r\n\t\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\r\n\t\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\r\n\t\t\t\ti = 'value';\r\n\t\t\t} else if (i === 'download' && value === true) {\r\n\t\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\r\n\t\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\r\n\t\t\t\t// converts it to an empty string instead, otherwise the attribute\r\n\t\t\t\t// value will be used as the file name and the file will be called\r\n\t\t\t\t// \"true\" upon downloading it.\r\n\t\t\t\tvalue = '';\r\n\t\t\t} else if (/ondoubleclick/i.test(i)) {\r\n\t\t\t\ti = 'ondblclick';\r\n\t\t\t} else if (\r\n\t\t\t\t/^onchange(textarea|input)/i.test(i + type) &&\r\n\t\t\t\t!onChangeInputType(props.type)\r\n\t\t\t) {\r\n\t\t\t\ti = 'oninput';\r\n\t\t\t} else if (/^onfocus$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusin';\r\n\t\t\t} else if (/^onblur$/i.test(i)) {\r\n\t\t\t\ti = 'onfocusout';\r\n\t\t\t} else if (/^on(Ani|Tra|Tou|BeforeInp)/.test(i)) {\r\n\t\t\t\ti = i.toLowerCase();\r\n\t\t\t} else if (nonCustomElement && CAMEL_PROPS.test(i)) {\r\n\t\t\t\ti = i.replace(/[A-Z0-9]/, '-$&').toLowerCase();\r\n\t\t\t} else if (value === null) {\r\n\t\t\t\tvalue = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tnormalizedProps[i] = value;\r\n\t\t}\r\n\r\n\t\t// Add support for array select values: <select multiple value={[]} />\r\n\t\tif (\r\n\t\t\ttype == 'select' &&\r\n\t\t\tnormalizedProps.multiple &&\r\n\t\t\tArray.isArray(normalizedProps.value)\r\n\t\t) {\r\n\t\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tchild.props.selected =\r\n\t\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// Adding support for defaultValue in select tag\r\n\t\tif (type == 'select' && normalizedProps.defaultValue != null) {\r\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\r\n\t\t\t\tif (normalizedProps.multiple) {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tchild.props.selected =\r\n\t\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tvnode.props = normalizedProps;\r\n\r\n\t\tif (props.class != props.className) {\r\n\t\t\tclassNameDescriptor.enumerable = 'className' in props;\r\n\t\t\tif (props.className != null) normalizedProps.class = props.className;\r\n\t\t\tObject.defineProperty(normalizedProps, 'className', classNameDescriptor);\r\n\t\t}\r\n\t}\r\n\r\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\r\n\r\n\tif (oldVNodeHook) oldVNodeHook(vnode);\r\n};\r\n\r\n// Only needed for react-relay\r\nlet currentComponent;\r\nconst oldBeforeRender = options._render;\r\noptions._render = function(vnode) {\r\n\tif (oldBeforeRender) {\r\n\t\toldBeforeRender(vnode);\r\n\t}\r\n\tcurrentComponent = vnode._component;\r\n};\r\n\r\n// This is a very very private internal function for React it\r\n// is used to sort-of do runtime dependency injection. So far\r\n// only `react-relay` makes use of it. It uses it to read the\r\n// context value.\r\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\r\n\tReactCurrentDispatcher: {\r\n\t\tcurrent: {\r\n\t\t\treadContext(context) {\r\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n", "import {\r\n\tcreateElement,\r\n\trender as preactRender,\r\n\tcloneElement as preactCloneElement,\r\n\tcreateRef,\r\n\tComponent,\r\n\tcreateContext,\r\n\tFragment\r\n} from 'preact';\r\nimport {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue\r\n} from 'preact/hooks';\r\nimport { PureComponent } from './PureComponent';\r\nimport { memo } from './memo';\r\nimport { forwardRef } from './forwardRef';\r\nimport { Children } from './Children';\r\nimport { Suspense, lazy } from './suspense';\r\nimport { SuspenseList } from './suspense-list';\r\nimport { createPortal } from './portals';\r\nimport {\r\n\thydrate,\r\n\trender,\r\n\tREACT_ELEMENT_TYPE,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n} from './render';\r\n\r\nconst version = '17.0.2'; // trick libraries to think we are react\r\n\r\n/**\r\n * Legacy version of createElement.\r\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\r\n */\r\nfunction createFactory(type) {\r\n\treturn createElement.bind(null, type);\r\n}\r\n\r\n/**\r\n * Check if the passed element is a valid (p)react node.\r\n * @param {*} element The element to check\r\n * @returns {boolean}\r\n */\r\nfunction isValidElement(element) {\r\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\r\n}\r\n\r\n/**\r\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\r\n * all vnode normalizations.\r\n * @param {import('./internal').VNode} element The vnode to clone\r\n * @param {object} props Props to add when cloning\r\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\r\n */\r\nfunction cloneElement(element) {\r\n\tif (!isValidElement(element)) return element;\r\n\treturn preactCloneElement.apply(null, arguments);\r\n}\r\n\r\n/**\r\n * Remove a component tree from the DOM, including state and event handlers.\r\n * @param {import('./internal').PreactElement} container\r\n * @returns {boolean}\r\n */\r\nfunction unmountComponentAtNode(container) {\r\n\tif (container._children) {\r\n\t\tpreactRender(null, container);\r\n\t\treturn true;\r\n\t}\r\n\treturn false;\r\n}\r\n\r\n/**\r\n * Get the matching DOM node for a component\r\n * @param {import('./internal').Component} component\r\n * @returns {import('./internal').PreactElement | null}\r\n */\r\nfunction findDOMNode(component) {\r\n\treturn (\r\n\t\t(component &&\r\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\r\n\t\tnull\r\n\t);\r\n}\r\n\r\n/**\r\n * Deprecated way to control batched rendering inside the reconciler, but we\r\n * already schedule in batches inside our rendering code\r\n * @template Arg\r\n * @param {(arg: Arg) => void} callback function that triggers the updated\r\n * @param {Arg} [arg] Optional argument that can be passed to the callback\r\n */\r\n// eslint-disable-next-line camelcase\r\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\r\n * implmented here as a no-op.\r\n * @template Arg\r\n * @template Result\r\n * @param {(arg: Arg) => Result} callback function that runs before the flush\r\n * @param {Arg} [arg] Optional arugment that can be passed to the callback\r\n * @returns\r\n */\r\nconst flushSync = (callback, arg) => callback(arg);\r\n\r\n/**\r\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\r\n * that just renders its children without imposing any restrictions.\r\n */\r\nconst StrictMode = Fragment;\r\n\r\nexport * from 'preact/hooks';\r\nexport {\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\t// eslint-disable-next-line camelcase\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n\r\n// React copies the named exports to the default one.\r\nexport default {\r\n\tuseState,\r\n\tuseReducer,\r\n\tuseEffect,\r\n\tuseLayoutEffect,\r\n\tuseRef,\r\n\tuseImperativeHandle,\r\n\tuseMemo,\r\n\tuseCallback,\r\n\tuseContext,\r\n\tuseDebugValue,\r\n\tversion,\r\n\tChildren,\r\n\trender,\r\n\thydrate,\r\n\tunmountComponentAtNode,\r\n\tcreatePortal,\r\n\tcreateElement,\r\n\tcreateContext,\r\n\tcreateFactory,\r\n\tcloneElement,\r\n\tcreateRef,\r\n\tFragment,\r\n\tisValidElement,\r\n\tfindDOMNode,\r\n\tComponent,\r\n\tPureComponent,\r\n\tmemo,\r\n\tforwardRef,\r\n\tflushSync,\r\n\tunstable_batchedUpdates,\r\n\tStrictMode,\r\n\tSuspense,\r\n\tSuspenseList,\r\n\tlazy,\r\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\r\n};\r\n", "import{options as n}from\"preact\";var t,u,r,o=0,i=[],c=n.__b,f=n.__r,e=n.diffed,a=n.__c,v=n.unmount;function m(t,r){n.__h&&n.__h(u,t,o||r),o=0;var i=u.__H||(u.__H={__:[],__h:[]});return t>=i.__.length&&i.__.push({}),i.__[t]}function l(n){return o=1,p(w,n)}function p(n,r,o){var i=m(t++,2);return i.t=n,i.__c||(i.__=[o?o(r):w(void 0,r),function(n){var t=i.t(i.__[0],n);i.__[0]!==t&&(i.__=[t,i.__[1]],i.__c.setState({}))}],i.__c=u),i.__}function y(r,o){var i=m(t++,3);!n.__s&&k(i.__H,o)&&(i.__=r,i.__H=o,u.__H.__h.push(i))}function h(r,o){var i=m(t++,4);!n.__s&&k(i.__H,o)&&(i.__=r,i.__H=o,u.__h.push(i))}function s(n){return o=5,d(function(){return{current:n}},[])}function _(n,t,u){o=6,h(function(){\"function\"==typeof n?n(t()):n&&(n.current=t())},null==u?u:u.concat(n))}function d(n,u){var r=m(t++,7);return k(r.__H,u)&&(r.__=n(),r.__H=u,r.__h=n),r.__}function A(n,t){return o=8,d(function(){return n},t)}function F(n){var r=u.context[n.__c],o=m(t++,9);return o.c=n,r?(null==o.__&&(o.__=!0,r.sub(u)),r.props.value):n.__}function T(t,u){n.useDebugValue&&n.useDebugValue(u?u(t):t)}function q(n){var r=m(t++,10),o=l();return r.__=n,u.componentDidCatch||(u.componentDidCatch=function(n){r.__&&r.__(n),o[1](n)}),[o[0],function(){o[1](void 0)}]}function x(){var t;for(i.sort(function(n,t){return n.__v.__b-t.__v.__b});t=i.pop();)if(t.__P)try{t.__H.__h.forEach(g),t.__H.__h.forEach(j),t.__H.__h=[]}catch(u){t.__H.__h=[],n.__e(u,t.__v)}}n.__b=function(n){u=null,c&&c(n)},n.__r=function(n){f&&f(n),t=0;var r=(u=n.__c).__H;r&&(r.__h.forEach(g),r.__h.forEach(j),r.__h=[])},n.diffed=function(t){e&&e(t);var o=t.__c;o&&o.__H&&o.__H.__h.length&&(1!==i.push(o)&&r===n.requestAnimationFrame||((r=n.requestAnimationFrame)||function(n){var t,u=function(){clearTimeout(r),b&&cancelAnimationFrame(t),setTimeout(n)},r=setTimeout(u,100);b&&(t=requestAnimationFrame(u))})(x)),u=null},n.__c=function(t,u){u.some(function(t){try{t.__h.forEach(g),t.__h=t.__h.filter(function(n){return!n.__||j(n)})}catch(r){u.some(function(n){n.__h&&(n.__h=[])}),u=[],n.__e(r,t.__v)}}),a&&a(t,u)},n.unmount=function(t){v&&v(t);var u,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{g(n)}catch(n){u=n}}),u&&n.__e(u,r.__v))};var b=\"function\"==typeof requestAnimationFrame;function g(n){var t=u,r=n.__c;\"function\"==typeof r&&(n.__c=void 0,r()),u=t}function j(n){var t=u;n.__c=n.__(),u=t}function k(n,t){return!n||n.length!==t.length||t.some(function(t,u){return t!==n[u]})}function w(n,t){return\"function\"==typeof t?t(n):t}export{l as useState,p as useReducer,y as useEffect,h as useLayoutEffect,s as useRef,_ as useImperativeHandle,d as useMemo,A as useCallback,F as useContext,T as useDebugValue,q as useErrorBoundary};\n//# sourceMappingURL=hooks.module.js.map\n", "import { options } from 'preact';\r\n\r\n/** @type {number} */\r\nlet currentIndex;\r\n\r\n/** @type {import('./internal').Component} */\r\nlet currentComponent;\r\n\r\n/** @type {number} */\r\nlet currentHook = 0;\r\n\r\n/** @type {Array<import('./internal').Component>} */\r\nlet afterPaintEffects = [];\r\n\r\nlet oldBeforeDiff = options._diff;\r\nlet oldBeforeRender = options._render;\r\nlet oldAfterDiff = options.diffed;\r\nlet oldCommit = options._commit;\r\nlet oldBeforeUnmount = options.unmount;\r\n\r\nconst RAF_TIMEOUT = 100;\r\nlet prevRaf;\r\n\r\noptions._diff = vnode => {\r\n\tcurrentComponent = null;\r\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\r\n};\r\n\r\noptions._render = vnode => {\r\n\tif (oldBeforeRender) oldBeforeRender(vnode);\r\n\r\n\tcurrentComponent = vnode._component;\r\n\tcurrentIndex = 0;\r\n\r\n\tconst hooks = currentComponent.__hooks;\r\n\tif (hooks) {\r\n\t\thooks._pendingEffects.forEach(invokeCleanup);\r\n\t\thooks._pendingEffects.forEach(invokeEffect);\r\n\t\thooks._pendingEffects = [];\r\n\t}\r\n};\r\n\r\noptions.diffed = vnode => {\r\n\tif (oldAfterDiff) oldAfterDiff(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks && c.__hooks._pendingEffects.length) {\r\n\t\tafterPaint(afterPaintEffects.push(c));\r\n\t}\r\n\tcurrentComponent = null;\r\n};\r\n\r\noptions._commit = (vnode, commitQueue) => {\r\n\tcommitQueue.some(component => {\r\n\t\ttry {\r\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\r\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\r\n\t\t\t\tcb._value ? invokeEffect(cb) : true\r\n\t\t\t);\r\n\t\t} catch (e) {\r\n\t\t\tcommitQueue.some(c => {\r\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\r\n\t\t\t});\r\n\t\t\tcommitQueue = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t});\r\n\r\n\tif (oldCommit) oldCommit(vnode, commitQueue);\r\n};\r\n\r\noptions.unmount = vnode => {\r\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\r\n\r\n\tconst c = vnode._component;\r\n\tif (c && c.__hooks) {\r\n\t\tlet hasErrored;\r\n\t\tc.__hooks._list.forEach(s => {\r\n\t\t\ttry {\r\n\t\t\t\tinvokeCleanup(s);\r\n\t\t\t} catch (e) {\r\n\t\t\t\thasErrored = e;\r\n\t\t\t}\r\n\t\t});\r\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\r\n\t}\r\n};\r\n\r\n/**\r\n * Get a hook's state from the currentComponent\r\n * @param {number} index The index of the hook to get\r\n * @param {number} type The index of the hook to get\r\n * @returns {any}\r\n */\r\nfunction getHookState(index, type) {\r\n\tif (options._hook) {\r\n\t\toptions._hook(currentComponent, index, currentHook || type);\r\n\t}\r\n\tcurrentHook = 0;\r\n\r\n\t// Largely inspired by:\r\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\r\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\r\n\t// Other implementations to look at:\r\n\t// * https://codesandbox.io/s/mnox05qp8\r\n\tconst hooks =\r\n\t\tcurrentComponent.__hooks ||\r\n\t\t(currentComponent.__hooks = {\r\n\t\t\t_list: [],\r\n\t\t\t_pendingEffects: []\r\n\t\t});\r\n\r\n\tif (index >= hooks._list.length) {\r\n\t\thooks._list.push({});\r\n\t}\r\n\treturn hooks._list[index];\r\n}\r\n\r\n/**\r\n * @param {import('./index').StateUpdater<any>} [initialState]\r\n */\r\nexport function useState(initialState) {\r\n\tcurrentHook = 1;\r\n\treturn useReducer(invokeOrReturn, initialState);\r\n}\r\n\r\n/**\r\n * @param {import('./index').Reducer<any, any>} reducer\r\n * @param {import('./index').StateUpdater<any>} initialState\r\n * @param {(initialState: any) => void} [init]\r\n * @returns {[ any, (state: any) => void ]}\r\n */\r\nexport function useReducer(reducer, initialState, init) {\r\n\t/** @type {import('./internal').ReducerHookState} */\r\n\tconst hookState = getHookState(currentIndex++, 2);\r\n\thookState._reducer = reducer;\r\n\tif (!hookState._component) {\r\n\t\thookState._value = [\r\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\r\n\r\n\t\t\taction => {\r\n\t\t\t\tconst nextValue = hookState._reducer(hookState._value[0], action);\r\n\t\t\t\tif (hookState._value[0] !== nextValue) {\r\n\t\t\t\t\thookState._value = [nextValue, hookState._value[1]];\r\n\t\t\t\t\thookState._component.setState({});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\thookState._component = currentComponent;\r\n\t}\r\n\r\n\treturn hookState._value;\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 3);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').Effect} callback\r\n * @param {any[]} args\r\n */\r\nexport function useLayoutEffect(callback, args) {\r\n\t/** @type {import('./internal').EffectHookState} */\r\n\tconst state = getHookState(currentIndex++, 4);\r\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\r\n\t\tstate._value = callback;\r\n\t\tstate._args = args;\r\n\r\n\t\tcurrentComponent._renderCallbacks.push(state);\r\n\t}\r\n}\r\n\r\nexport function useRef(initialValue) {\r\n\tcurrentHook = 5;\r\n\treturn useMemo(() => ({ current: initialValue }), []);\r\n}\r\n\r\n/**\r\n * @param {object} ref\r\n * @param {() => object} createHandle\r\n * @param {any[]} args\r\n */\r\nexport function useImperativeHandle(ref, createHandle, args) {\r\n\tcurrentHook = 6;\r\n\tuseLayoutEffect(\r\n\t\t() => {\r\n\t\t\tif (typeof ref == 'function') ref(createHandle());\r\n\t\t\telse if (ref) ref.current = createHandle();\r\n\t\t},\r\n\t\targs == null ? args : args.concat(ref)\r\n\t);\r\n}\r\n\r\n/**\r\n * @param {() => any} factory\r\n * @param {any[]} args\r\n */\r\nexport function useMemo(factory, args) {\r\n\t/** @type {import('./internal').MemoHookState} */\r\n\tconst state = getHookState(currentIndex++, 7);\r\n\tif (argsChanged(state._args, args)) {\r\n\t\tstate._value = factory();\r\n\t\tstate._args = args;\r\n\t\tstate._factory = factory;\r\n\t}\r\n\r\n\treturn state._value;\r\n}\r\n\r\n/**\r\n * @param {() => void} callback\r\n * @param {any[]} args\r\n */\r\nexport function useCallback(callback, args) {\r\n\tcurrentHook = 8;\r\n\treturn useMemo(() => callback, args);\r\n}\r\n\r\n/**\r\n * @param {import('./internal').PreactContext} context\r\n */\r\nexport function useContext(context) {\r\n\tconst provider = currentComponent.context[context._id];\r\n\t// We could skip this call here, but than we'd not call\r\n\t// `options._hook`. We need to do that in order to make\r\n\t// the devtools aware of this hook.\r\n\t/** @type {import('./internal').ContextHookState} */\r\n\tconst state = getHookState(currentIndex++, 9);\r\n\t// The devtools needs access to the context object to\r\n\t// be able to pull of the default value when no provider\r\n\t// is present in the tree.\r\n\tstate._context = context;\r\n\tif (!provider) return context._defaultValue;\r\n\t// This is probably not safe to convert to \"!\"\r\n\tif (state._value == null) {\r\n\t\tstate._value = true;\r\n\t\tprovider.sub(currentComponent);\r\n\t}\r\n\treturn provider.props.value;\r\n}\r\n\r\n/**\r\n * Display a custom label for a custom hook for the devtools panel\r\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\r\n */\r\nexport function useDebugValue(value, formatter) {\r\n\tif (options.useDebugValue) {\r\n\t\toptions.useDebugValue(formatter ? formatter(value) : value);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {(error: any) => void} cb\r\n */\r\nexport function useErrorBoundary(cb) {\r\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\r\n\tconst state = getHookState(currentIndex++, 10);\r\n\tconst errState = useState();\r\n\tstate._value = cb;\r\n\tif (!currentComponent.componentDidCatch) {\r\n\t\tcurrentComponent.componentDidCatch = err => {\r\n\t\t\tif (state._value) state._value(err);\r\n\t\t\terrState[1](err);\r\n\t\t};\r\n\t}\r\n\treturn [\r\n\t\terrState[0],\r\n\t\t() => {\r\n\t\t\terrState[1](undefined);\r\n\t\t}\r\n\t];\r\n}\r\n\r\n/**\r\n * After paint effects consumer.\r\n */\r\nfunction flushAfterPaintEffects() {\r\n\tlet component;\r\n\t// sort the queue by depth (outermost to innermost)\r\n\tafterPaintEffects.sort((a, b) => a._vnode._depth - b._vnode._depth);\r\n\twhile (component = afterPaintEffects.pop()) {\r\n\t\tif (!component._parentDom) continue;\r\n\t\ttry {\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\r\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t} catch (e) {\r\n\t\t\tcomponent.__hooks._pendingEffects = [];\r\n\t\t\toptions._catchError(e, component._vnode);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\r\n\r\n/**\r\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\r\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\r\n * the next browser frame.\r\n *\r\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\r\n * even if RAF doesn't fire (for example if the browser tab is not visible)\r\n *\r\n * @param {() => void} callback\r\n */\r\nfunction afterNextFrame(callback) {\r\n\tconst done = () => {\r\n\t\tclearTimeout(timeout);\r\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\r\n\t\tsetTimeout(callback);\r\n\t};\r\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\r\n\r\n\tlet raf;\r\n\tif (HAS_RAF) {\r\n\t\traf = requestAnimationFrame(done);\r\n\t}\r\n}\r\n\r\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\r\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\r\n// Perhaps this is not such a big deal.\r\n/**\r\n * Schedule afterPaintEffects flush after the browser paints\r\n * @param {number} newQueueLength\r\n */\r\nfunction afterPaint(newQueueLength) {\r\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\r\n\t\tprevRaf = options.requestAnimationFrame;\r\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\r\n\t}\r\n}\r\n\r\n/**\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeCleanup(hook) {\r\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\tlet cleanup = hook._cleanup;\r\n\tif (typeof cleanup == 'function') {\r\n\t\thook._cleanup = undefined;\r\n\t\tcleanup();\r\n\t}\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * Invoke a Hook's effect\r\n * @param {import('./internal').EffectHookState} hook\r\n */\r\nfunction invokeEffect(hook) {\r\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\r\n\t// and move the currentComponent away.\r\n\tconst comp = currentComponent;\r\n\thook._cleanup = hook._value();\r\n\tcurrentComponent = comp;\r\n}\r\n\r\n/**\r\n * @param {any[]} oldArgs\r\n * @param {any[]} newArgs\r\n */\r\nfunction argsChanged(oldArgs, newArgs) {\r\n\treturn (\r\n\t\t!oldArgs ||\r\n\t\toldArgs.length !== newArgs.length ||\r\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\r\n\t);\r\n}\r\n\r\nfunction invokeOrReturn(arg, f) {\r\n\treturn typeof f == 'function' ? f(arg) : f;\r\n}\r\n", "export { default as PureInlineComponent } from './PureInlineComponent'\n", "import { PureComponent } from 'preact/compat'\n\nexport default class PureInlineComponent extends PureComponent {\n  shouldComponentUpdate(nextProps) {\n    for (let k in nextProps) {\n      if (k == 'children') continue\n\n      if (nextProps[k] != this.props[k]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n", "// @ts-nocheck\nimport { render } from 'preact'\n\nimport { init, getProps } from '../../config'\nimport { ShadowElement } from '../HTMLElement'\nimport { Picker, PickerStyles } from '.'\nimport PickerProps from './PickerProps'\n\nexport default class PickerElement extends ShadowElement {\n  static Props = PickerProps\n\n  constructor(props) {\n    super(props, { styles: PickerStyles })\n  }\n\n  async connectedCallback() {\n    const props = getProps(this.props, PickerProps, this)\n    props.element = this\n    props.ref = (component) => {\n      this.component = component\n    }\n\n    await init(props)\n    if (this.disconnected) return\n\n    render(<Picker {...props} />, this.shadowRoot)\n  }\n}\n\nif (\n  typeof customElements !== 'undefined' &&\n  !customElements.get('em-emoji-picker')\n) {\n  customElements.define('em-emoji-picker', PickerElement)\n}\n", "module.exports = \"565112e37e99085c\";", "// @ts-nocheck\nimport React, { useEffect, useRef } from 'react'\nimport { Picker } from 'emoji-mart'\n\nexport default function EmojiPicker(props) {\n  const ref = useRef(null)\n  const instance = useRef(null)\n\n  if (instance.current) {\n    instance.current.update(props)\n  }\n\n  useEffect(() => {\n    instance.current = new Picker({ ...props, ref })\n\n    return () => {\n      instance.current = null\n    }\n  }, [])\n\n  return React.createElement('div', { ref })\n}\n"], "mappings": ";;;;;;;;;;;;;;AGAe,SAAA,yCAAyB,KAAK,KAAK,OAAO;AACvD,MAAI,OAAO,IACT,QAAO,eAAe,KAAK,KAAK;IAC9B;IACA,YAAY;IACZ,cAAc;IACd,UAAU;GACX;MAED,KAAI,GAAG,IAAI;AAGb,SAAO;;AIZF,IC0BMA;AD1BN,IEWDC;AFXC,IGGHC;AHHG,IGgGMC;AHhGN,II0KHC;AJ1KG,IIkLDC;AJlLC,IIgMHC;AJhMG,IKEIC;ALFJ,IAAMC,0BAAY,CAAA;AAAlB,IACMC,0BAAY,CAAA;AADlB,IAEMC,0BAAAA;ACON,SAASC,wBAAOC,IAAKC,IAAAA;AAAAA,WAElBN,MAAKM,GAAOD,IAAIL,EAAAA,IAAKM,GAAMN,EAAAA;AAAAA,SACPK;;AASvB,SAASE,wBAAWC,IAAAA;AAAAA,MACtBC,KAAaD,GAAKC;AAClBA,QAAYA,GAAWC,YAAYF,EAAAA;;AEVxC,SAAgBG,0CAAcC,IAAMN,IAAOO,IAAAA;AAAAA,MAEzCC,IACAC,IACAf,IAHGgB,KAAkB,CAAA;AAAA,OAIjBhB,MAAKM,GACA,UAALN,KAAYc,KAAMR,GAAMN,EAAAA,IACd,SAALA,KAAYe,KAAMT,GAAMN,EAAAA,IAC5BgB,GAAgBhB,EAAAA,IAAKM,GAAMN,EAAAA;AAAAA,MAG7BiB,UAAUC,SAAS,MACtBF,GAAgBH,WACfI,UAAUC,SAAS,IAAIzB,wBAAM0B,KAAKF,WAAW,CAAA,IAAKJ,KAKjC,cAAA,OAARD,MAA2C,QAArBA,GAAKQ,aAAAA,MAChCpB,MAAKY,GAAKQ,aAAAA,YACVJ,GAAgBhB,EAAAA,MACnBgB,GAAgBhB,EAAAA,IAAKY,GAAKQ,aAAapB,EAAAA;AAAAA,SAKnCqB,wBAAYT,IAAMI,IAAiBF,IAAKC,IAAK,IAAA;;AAe9C,SAASM,wBAAYT,IAAMN,IAAOQ,IAAKC,IAAKO,IAAAA;AAAAA,MAG5CC,KAAQ;IACbX,MAAAA;IACAN,OAAAA;IACAQ,KAAAA;IACAC,KAAAA;IAAAA,KACW;IAAA,IACF;IAAA,KACD;IAAA,KACF;IAAA,KAAA;IAKIS,KACE;IAAA,KACA;IACZC,aAAAA;IAAaD,KACU,QAAZF,KAAAA,EAAqB3B,0BAAU2B;;AAAAA,SAI3B,QAAZA,MAAqC,QAAjB5B,0CAAQ6B,SAAe7B,0CAAQ6B,MAAMA,EAAAA,GAEtDA;;AAGR,SAAgBG,4CAAAA;AAAAA,SACR;IAAEC,SAAS;;;AAGZ,SAASC,0CAAStB,IAAAA;AAAAA,SACjBA,GAAMO;;AC5EP,SAASgB,0CAAUvB,IAAOwB,IAAAA;AAAAA,OAC3BxB,QAAQA,IAAAA,KACRwB,UAAUA;;AAyET,SAASC,wBAAcR,IAAOS,IAAAA;AAAAA,MAClB,QAAdA,GAAAA,QAEIT,GAAAA,KACJQ,wBAAcR,GAAAA,IAAeA,GAAAA,GAAAA,IAAwBU,QAAQV,EAAAA,IAAS,CAAA,IACtE;AAAA,WAGAW,IACGF,KAAaT,GAAAA,IAAgBL,QAAQc,KAAAA,KAG5B,SAFfE,KAAUX,GAAAA,IAAgBS,EAAAA,MAEa,QAAhBE,GAAAA,IAAAA,QAIfA,GAAAA;AAAAA,SASmB,cAAA,OAAdX,GAAMX,OAAqBmB,wBAAcR,EAAAA,IAAS;;AAuCjE,SAASY,wBAAwBZ,IAAAA;AAAjC,MAGWvB,IACJoC;AAAAA,MAHyB,SAA1Bb,KAAQA,GAAAA,OAA8C,QAApBA,GAAAA,KAA0B;AAAA,SAChEA,GAAAA,MAAaA,GAAAA,IAAiBc,OAAO,MAC5BrC,KAAI,GAAGA,KAAIuB,GAAAA,IAAgBL,QAAQlB,KAAAA,KAE9B,SADToC,KAAQb,GAAAA,IAAgBvB,EAAAA,MACO,QAAdoC,GAAAA,KAAoB;AACxCb,SAAAA,MAAaA,GAAAA,IAAiBc,OAAOD,GAAAA;AAAAA;;AAAAA,WAKhCD,wBAAwBZ,EAAAA;;;AAoC1B,SAASe,wBAAcC,IAAAA;AAAAA,GAAAA,CAE1BA,GAAAA,QACAA,GAAAA,MAAAA,SACD1C,wBAAc2C,KAAKD,EAAAA,KAAAA,CAClBE,wBAAAA,SACF1C,4BAAiBL,0CAAQgD,wBAEzB3C,0BAAeL,0CAAQgD,sBACN5C,yBAAO2C,uBAAAA;;AAK1B,SAASA,0BAAAA;AAAAA,WACJE,IACIF,wBAAAA,MAAyB5C,wBAAcqB,SAC9CyB,MAAQ9C,wBAAc+C,KAAK,SAACC,KAAGC,IAAAA;AAAAA,WAAMD,IAAAA,IAAAA,MAAkBC,GAAAA,IAAAA;GAAAA,GACvDjD,0BAAgB,CAAA,GAGhB8C,GAAMI,KAAK,SAAAR,KAAAA;AApGb,QAAyBS,IAMnBC,IACEC,IANH3B,IACH4B,IACAC;AAkGKb,QAAAA,QAnGLY,MADG5B,MADoByB,KAqGQT,KAAAA,KAAAA,MAlG/Ba,KAAYJ,GAAAA,SAGRC,KAAc,CAAA,IACZC,KAAW9C,wBAAO,CAAA,GAAImB,EAAAA,GAAAA,MACPA,GAAAA,MAAkB,GAEvC8B,wBACCD,IACA7B,IACA2B,IACAF,GAAAA,KAAAA,WACAI,GAAUE,iBACU,QAApB/B,GAAAA,MAA2B;MAAC4B;QAAU,MACtCF,IACU,QAAVE,KAAiBpB,wBAAcR,EAAAA,IAAS4B,IACxC5B,GAAAA,GAAAA,GAEDgC,wBAAWN,IAAa1B,EAAAA,GAEpBA,GAAAA,OAAc4B,MACjBhB,wBAAwBZ,EAAAA;GAAAA;;AEtH3B,SAAgBiC,wBACfJ,KACAK,IACAC,IACAC,IACAC,IACAC,IACAC,IACAb,IACAE,IACAY,IAAAA;AAVD,MAYK/D,IAAGgE,IAAGd,IAAUe,IAAYC,IAAQC,IAAeC,IAInDC,KAAeV,MAAkBA,GAAAA,OAA6BzD,yBAE9DoE,KAAoBD,GAAYnD;AAAAA,OAEpCwC,GAAAA,MAA2B,CAAA,GACtB1D,KAAI,GAAGA,KAAIyD,GAAavC,QAAQlB,KAAAA,KAgDlB,SA5CjBiE,KAAaP,GAAAA,IAAyB1D,EAAAA,IADrB,SAFlBiE,KAAaR,GAAazD,EAAAA,MAEqB,aAAA,OAAdiE,KACW,OAMtB,YAAA,OAAdA,MACc,YAAA,OAAdA,MAEc,YAAA,OAAdA,KAEoC5C,wBAC1C,MACA4C,IACA,MACA,MACAA,EAAAA,IAESM,MAAMC,QAAQP,EAAAA,IACmB5C,wBAC1CO,2CACA;IAAEf,UAAUoD;KACZ,MACA,MACA,IAAA,IAESA,GAAAA,MAAoB,IAKa5C,wBAC1C4C,GAAWrD,MACXqD,GAAW3D,OACX2D,GAAWnD,KACX,MACAmD,GAAAA,GAAAA,IAG0CA,KAAAA;AAAAA,QAS5CA,GAAAA,KAAqBP,IACrBO,GAAAA,MAAoBP,GAAAA,MAAwB,GAS9B,UAHdR,KAAWmB,GAAYrE,EAAAA,MAIrBkD,MACAe,GAAWnD,OAAOoC,GAASpC,OAC3BmD,GAAWrD,SAASsC,GAAStC,KAE9ByD,IAAYrE,EAAAA,IAAAA;QAAKwB,MAIZwC,KAAI,GAAGA,KAAIM,IAAmBN,MAAK;AAAA,WACvCd,KAAWmB,GAAYL,EAAAA,MAKtBC,GAAWnD,OAAOoC,GAASpC,OAC3BmD,GAAWrD,SAASsC,GAAStC,MAC5B;AACDyD,WAAYL,EAAAA,IAAAA;AAAKxC;;AAGlB0B,WAAW;;AAObG,4BACCD,KACAa,IALDf,KAAWA,MAAYjD,yBAOtB2D,IACAC,IACAC,IACAb,IACAE,IACAY,EAAAA,GAGDG,KAASD,GAAAA,MAEJD,KAAIC,GAAWlD,QAAQmC,GAASnC,OAAOiD,OACtCI,OAAMA,KAAO,CAAA,IACdlB,GAASnC,OAAKqD,GAAK5B,KAAKU,GAASnC,KAAK,MAAMkD,EAAAA,GAChDG,GAAK5B,KAAKwB,IAAGC,GAAAA,OAAyBC,IAAQD,EAAAA,IAGjC,QAAVC,MACkB,QAAjBC,OACHA,KAAgBD,KAIU,cAAA,OAAnBD,GAAWrD,QAClBqD,GAAAA,QAAyBf,GAAAA,MAEzBe,GAAAA,MAAsBd,KAASsB,wBAC9BR,IACAd,IACAC,GAAAA,IAGDD,KAASuB,wBACRtB,KACAa,IACAf,IACAmB,IACAH,IACAf,EAAAA,GAIgC,cAAA,OAAvBO,GAAe9C,SAQzB8C,GAAAA,MAA0BP,OAG3BA,MACAD,GAAAA,OAAiBC,MACjBA,GAAO1C,cAAc2C,QAIrBD,KAASpB,wBAAcmB,EAAAA;;AAAAA,OAIzBQ,GAAAA,MAAsBS,IAGjBnE,KAAIsE,IAAmBtE,OACL,SAAlBqE,GAAYrE,EAAAA,MAEgB,cAAA,OAAvB0D,GAAe9C,QACC,QAAvByD,GAAYrE,EAAAA,EAAAA,OACZqE,GAAYrE,EAAAA,EAAAA,OAAW0D,GAAAA,QAKvBA,GAAAA,MAA0B3B,wBAAc4B,IAAgB3D,KAAI,CAAA,IAG7D2E,wBAAQN,GAAYrE,EAAAA,GAAIqE,GAAYrE,EAAAA,CAAAA;AAAAA,MAKlCoE,GAAAA,MACEpE,KAAI,GAAGA,KAAIoE,GAAKlD,QAAQlB,KAC5B4E,yBAASR,GAAKpE,EAAAA,GAAIoE,GAAAA,EAAOpE,EAAAA,GAAIoE,GAAAA,EAAOpE,EAAAA,CAAAA;;AAKvC,SAASyE,wBAAgBR,KAAYd,KAAQC,IAAAA;AAAAA,WAKvC7B,IAHDgB,KAAI0B,IAAAA,KACJY,KAAM,GACHtC,MAAKsC,KAAMtC,GAAErB,QAAQ2D,KACvBtD,EAAAA,KAAQgB,GAAEsC,EAAAA,OAMbtD,GAAAA,KAAgB0C,KAGfd,MADwB,cAAA,OAAd5B,GAAMX,OACP6D,wBAAgBlD,IAAO4B,KAAQC,EAAAA,IAE/BsB,wBACRtB,IACA7B,IACAA,IACAgB,IACAhB,GAAAA,KACA4B,GAAAA;AAAAA,SAMGA;;AASD,SAAS2B,0CAAajE,KAAUkE,KAAAA;AAAAA,SACtCA,MAAMA,OAAO,CAAA,GACG,QAAZlE,OAAuC,aAAA,OAAZA,QACpB0D,MAAMC,QAAQ3D,GAAAA,IACxBA,IAASkC,KAAK,SAAAX,KAAAA;AACb0C,8CAAa1C,KAAO2C,GAAAA;GAAAA,IAGrBA,IAAIvC,KAAK3B,GAAAA,IAEHkE;;AAGR,SAASL,wBACRtB,KACAa,KACAf,IACAmB,IACAH,IACAf,IAAAA;AAND,MAQK6B,IAuBGC,IAAiBjB;AAAAA,MAAAA,WAtBpBC,IAAAA,IAIHe,MAAUf,IAAAA,KAMVA,IAAAA,MAAAA;WAEY,QAAZf,MACAgB,MAAUf,MACW,QAArBe,GAAOzD,WAEPyE,GAAO,KAAc,QAAV/B,MAAkBA,GAAO1C,eAAe2C,IAClDA,KAAU+B,YAAYjB,EAAAA,GACtBc,KAAU;OACJ;AAAA,SAGDC,KAAS9B,IAAQa,KAAI,IACxBiB,KAASA,GAAOG,gBAAgBpB,KAAIK,GAAYnD,QACjD8C,MAAK,EAAA,KAEDiB,MAAUf,GAAAA,OACPgB;AAGR9B,QAAUiC,aAAanB,IAAQf,EAAAA,GAC/B6B,KAAU7B;;AAAAA,SAAAA,WAOR6B,KACMA,KAEAd,GAAOkB;;AC9TX,SAASE,wBAAUC,KAAKC,KAAUC,IAAU5B,IAAO6B,IAAAA;AAAAA,MACrD1F;AAAAA,OAECA,MAAKyF,GACC,gBAANzF,MAA0B,UAANA,MAAiBA,MAAKwF,OAC7CG,wBAAYJ,KAAKvF,IAAG,MAAMyF,GAASzF,EAAAA,GAAI6D,EAAAA;AAAAA,OAIpC7D,MAAKwF,IAENE,OAAiC,cAAA,OAAfF,IAASxF,EAAAA,KACvB,eAANA,MACM,UAANA,MACM,YAANA,MACM,cAANA,MACAyF,GAASzF,EAAAA,MAAOwF,IAASxF,EAAAA,KAEzB2F,wBAAYJ,KAAKvF,IAAGwF,IAASxF,EAAAA,GAAIyF,GAASzF,EAAAA,GAAI6D,EAAAA;;AAKjD,SAAS+B,wBAASC,KAAO/E,KAAKgF,KAAAA;AACd,UAAXhF,IAAI,CAAA,IACP+E,IAAMF,YAAY7E,KAAKgF,GAAAA,IAEvBD,IAAM/E,GAAAA,IADa,QAATgF,MACG,KACa,YAAA,OAATA,OAAqB3F,wBAAmB4F,KAAKjF,GAAAA,IACjDgF,MAEAA,MAAQ;;AAYhB,SAASH,wBAAYJ,KAAKS,KAAMF,KAAOG,IAAUpC,IAAAA;AAAjD,MACFqC;AAEJC,IAAG,KAAa,YAATH,KAAAA;AAAAA,QACc,YAAA,OAATF,IACVP,KAAIM,MAAMO,UAAUN;SACd;AAAA,UACiB,YAAA,OAAZG,OACVV,IAAIM,MAAMO,UAAUH,KAAW,KAG5BA,GAAAA,MACED,OAAQC,GACNH,QAASE,OAAQF,OACtBF,wBAASL,IAAIM,OAAOG,KAAM,EAAA;AAAA,UAKzBF,IAAAA,MACEE,OAAQF,IACPG,OAAYH,IAAME,GAAAA,MAAUC,GAASD,GAAAA,KACzCJ,wBAASL,IAAIM,OAAOG,KAAMF,IAAME,GAAAA,CAAAA;;aAOhB,QAAZA,IAAK,CAAA,KAA0B,QAAZA,IAAK,CAAA,EAChCE,MAAaF,SAAUA,MAAOA,IAAKK,QAAAA,YAAoB,EAAA,IAGxBL,MAA3BA,IAAKM,YAAAA,KAAiBf,MAAYS,IAAKM,YAAAA,EAAc7G,MAAM,CAAA,IACnDuG,IAAKvG,MAAM,CAAA,GAElB8F,IAAIgB,MAAYhB,IAAIgB,IAAa,CAAA,IACtChB,IAAIgB,EAAWP,MAAOE,EAAAA,IAAcJ,KAEhCA,MACEG,MAEJV,IAAIiB,iBAAiBR,KADLE,KAAaO,0BAAoBC,yBACbR,EAAAA,IAIrCX,IAAIoB,oBAAoBX,KADRE,KAAaO,0BAAoBC,yBACVR,EAAAA;WAErB,8BAATF,KAAoC;AAAA,QAC1CnC,GAIHmC,OAAOA,IAAKK,QAAAA,cAAsB,GAAA,EAAKA,QAAAA,UAAkB,GAAA;aAEhD,WAATL,OACS,WAATA,OACS,WAATA,OAGS,eAATA,OACS,eAATA,OACAA,OAAQT,IAAAA,KAAAA;AAGPA,UAAIS,GAAAA,IAAiB,QAATF,MAAgB,KAAKA;AAAAA,YAE3BK;aACES,GAAAA;IAAAA;AAUW,kBAAA,OAAVd,QAGD,QAATA,QAAAA,UACCA,OAAgC,QAAZE,IAAK,CAAA,KAA0B,QAAZA,IAAK,CAAA,KAE7CT,IAAIsB,aAAab,KAAMF,GAAAA,IAEvBP,IAAIuB,gBAAgBd,GAAAA;;;AAUvB,SAASU,wBAAWE,KAAAA;AAAAA,OACdL,EAAWK,IAAEhG,OAAAA,KAAO,EAAOlB,0CAAQqH,QAAQrH,0CAAQqH,MAAMH,GAAAA,IAAKA,GAAAA;;AAGpE,SAASH,wBAAkBG,KAAAA;AAAAA,OACrBL,EAAWK,IAAEhG,OAAAA,IAAO,EAAMlB,0CAAQqH,QAAQrH,0CAAQqH,MAAMH,GAAAA,IAAKA,GAAAA;;ACpInE,SAAgBvD,wBACfD,KACA4D,KACA9D,IACAU,IACAC,IACAC,IACAb,IACAE,IACAY,IAAAA;AATD,MAWKc,IAoBEtC,IAAG0E,IAAOxB,IAAUyB,IAAUC,IAAUC,IACxC5B,IAKA6B,IACAC,IAqIA7D,IA/JL8D,KAAUP,IAASpG;AAAAA,MAAAA,WAIhBoG,IAASvF,YAA2B,QAAO;AAGpB,UAAvByB,GAAAA,QACHa,KAAcb,GAAAA,KACdC,KAAS6D,IAAAA,MAAgB9D,GAAAA,KAEzB8D,IAAAA,MAAsB,MACtBlD,KAAoB;IAACX;OAGjB0B,KAAMnF,0CAAAA,QAAgBmF,GAAImC,GAAAA;AAAAA,MAAAA;AAG9B9B,MAAO,KAAsB,cAAA,OAAXqC,IAAuB;AAAA,UAEpC/B,KAAWwB,IAAS1G,OAKpB+G,MADJxC,KAAM0C,GAAQC,gBACQ5D,GAAciB,GAAAA,GAAAA,GAChCyC,KAAmBzC,KACpBwC,KACCA,GAAS/G,MAAMwF,QACfjB,GAAAA,KACDjB,IAGCV,GAAAA,MAEHkE,MADA7E,KAAIyE,IAAAA,MAAsB9D,GAAAA,KAAAA,KAC0BX,GAAAA,OAGhD,eAAegF,MAAWA,GAAQE,UAAUC,SAE/CV,IAAAA,MAAsBzE,KAAI,IAAIgF,GAAQ/B,IAAU8B,EAAAA,KAGhDN,IAAAA,MAAsBzE,KAAI,IAAIV,0CAAU2D,IAAU8B,EAAAA,GAClD/E,GAAEd,cAAc8F,IAChBhF,GAAEmF,SAASC,0BAERN,MAAUA,GAASO,IAAIrF,EAAAA,GAE3BA,GAAEjC,QAAQkF,IACLjD,GAAEsF,UAAOtF,GAAEsF,QAAQ,CAAA,IACxBtF,GAAET,UAAUwF,IACZ/E,GAAAA,MAAmBqB,IACnBqD,KAAQ1E,GAAAA,MAAAA,MACRA,GAAAA,MAAqB,CAAA,IAIF,QAAhBA,GAAAA,QACHA,GAAAA,MAAeA,GAAEsF,QAEsB,QAApCN,GAAQO,6BACPvF,GAAAA,OAAgBA,GAAEsF,UACrBtF,GAAAA,MAAenC,wBAAO,CAAA,GAAImC,GAAAA,GAAAA,IAG3BnC,wBACCmC,GAAAA,KACAgF,GAAQO,yBAAyBtC,IAAUjD,GAAAA,GAAAA,CAAAA,IAI7CkD,KAAWlD,GAAEjC,OACb4G,KAAW3E,GAAEsF,OAGTZ,GAEkC,SAApCM,GAAQO,4BACgB,QAAxBvF,GAAEwF,sBAEFxF,GAAEwF,mBAAAA,GAGwB,QAAvBxF,GAAEyF,qBACLzF,GAAAA,IAAmBC,KAAKD,GAAEyF,iBAAAA;WAErB;AAAA,YAE+B,QAApCT,GAAQO,4BACRtC,OAAaC,MACkB,QAA/BlD,GAAE0F,6BAEF1F,GAAE0F,0BAA0BzC,IAAU8B,EAAAA,GAAAA,CAIpC/E,GAAAA,OAC0B,QAA3BA,GAAE2F,yBAAAA,UACF3F,GAAE2F,sBACD1C,IACAjD,GAAAA,KACA+E,EAAAA,KAEFN,IAAAA,QAAuB9D,GAAAA,KACtB;AACDX,aAAEjC,QAAQkF,IACVjD,GAAEsF,QAAQtF,GAAAA,KAENyE,IAAAA,QAAuB9D,GAAAA,QAAoBX,GAAAA,MAAAA,QAC/CA,GAAAA,MAAWyE,KACXA,IAAAA,MAAgB9D,GAAAA,KAChB8D,IAAAA,MAAqB9D,GAAAA,KACrB8D,IAAAA,IAAmBmB,QAAQ,SAAA5G,KAAAA;AACtBA,oBAAOA,IAAAA,KAAgByF;WAAAA,GAExBzE,GAAAA,IAAmBrB,UACtB+B,GAAYT,KAAKD,EAAAA;AAAAA,gBAGZ2C;;AAGsB,gBAAzB3C,GAAE6F,uBACL7F,GAAE6F,oBAAoB5C,IAAUjD,GAAAA,KAAc+E,EAAAA,GAGnB,QAAxB/E,GAAE8F,sBACL9F,GAAAA,IAAmBC,KAAK,WAAA;AACvBD,aAAE8F,mBAAmB5C,IAAUyB,IAAUC,EAAAA;SAAAA;;AAK5C5E,SAAET,UAAUwF,IACZ/E,GAAEjC,QAAQkF,IACVjD,GAAEsF,QAAQtF,GAAAA,MAELsC,KAAMnF,0CAAAA,QAAkBmF,GAAImC,GAAAA,GAEjCzE,GAAAA,MAAAA,OACAA,GAAAA,MAAWyE,KACXzE,GAAAA,MAAea,KAEfyB,KAAMtC,GAAEmF,OAAOnF,GAAEjC,OAAOiC,GAAEsF,OAAOtF,GAAET,OAAAA,GAGnCS,GAAEsF,QAAQtF,GAAAA,KAEe,QAArBA,GAAE+F,oBACL1E,KAAgBxD,wBAAOA,wBAAO,CAAA,GAAIwD,EAAAA,GAAgBrB,GAAE+F,gBAAAA,CAAAA,IAGhDrB,MAAsC,QAA7B1E,GAAEgG,4BACfpB,KAAW5E,GAAEgG,wBAAwB9C,IAAUyB,EAAAA,IAK5CzD,KADI,QAAPoB,MAAeA,GAAIjE,SAASgB,6CAAuB,QAAXiD,GAAI/D,MACL+D,GAAIvE,MAAMO,WAAWgE,IAE7DrB,wBACCJ,KACAmB,MAAMC,QAAQf,EAAAA,IAAgBA,KAAe;QAACA;SAC9CuD,KACA9D,IACAU,IACAC,IACAC,IACAb,IACAE,IACAY,EAAAA,GAGDxB,GAAEF,OAAO2E,IAAAA,KAGTA,IAAAA,MAAsB,MAElBzE,GAAAA,IAAmBrB,UACtB+B,GAAYT,KAAKD,EAAAA,GAGd6E,OACH7E,GAAAA,MAAkBA,GAAAA,KAAyB,OAG5CA,GAAAA,MAAAA;UAEqB,SAArBuB,MACAkD,IAAAA,QAAuB9D,GAAAA,OAEvB8D,IAAAA,MAAqB9D,GAAAA,KACrB8D,IAAAA,MAAgB9D,GAAAA,OAEhB8D,IAAAA,MAAgBwB,wBACftF,GAAAA,KACA8D,KACA9D,IACAU,IACAC,IACAC,IACAb,IACAc,EAAAA;AAIGc,KAAAA,KAAMnF,0CAAQ+I,WAAS5D,GAAImC,GAAAA;WACxBJ,KAAAA;AACRI,QAAAA,MAAqB,OAEjBjD,MAAoC,QAArBD,QAClBkD,IAAAA,MAAgB7D,IAChB6D,IAAAA,MAAAA,CAAAA,CAAwBjD,IACxBD,GAAkBA,GAAkB7B,QAAQkB,EAAAA,CAAAA,IAAW,OAIxDzD,0CAAAA,IAAoBkH,KAAGI,KAAU9D,EAAAA;;;AAS5B,SAASK,wBAAWN,KAAayF,KAAAA;AACnChJ,4CAAAA,OAAiBA,0CAAAA,IAAgBgJ,KAAMzF,GAAAA,GAE3CA,IAAYF,KAAK,SAAAR,KAAAA;AAAAA,QAAAA;AAGfU,YAAcV,IAAAA,KACdA,IAAAA,MAAqB,CAAA,GACrBU,IAAYF,KAAK,SAAA4F,KAAAA;AAEhBA,YAAGxH,KAAKoB,GAAAA;OAAAA;aAEDqE,KAAAA;AACRlH,gDAAAA,IAAoBkH,KAAGrE,IAAAA,GAAAA;;GAAAA;;AAmB1B,SAASiG,wBACRjD,KACAyB,KACA9D,KACAU,KACAC,KACAC,IACAb,IACAc,IAAAA;AARD,MAoBS3B,IAsDHwG,IACAC,IAjEDpD,KAAWvC,IAAS5C,OACpBkF,KAAWwB,IAAS1G,OACpBwI,KAAW9B,IAASpG,MACpBZ,KAAI;AAAA,MAGS,UAAb8I,OAAoBjF,MAAAA,OAEC,QAArBC,IAAAA;AAAAA,WACI9D,KAAI8D,GAAkB5C,QAAQlB,KAAAA,MAC9BoC,KAAQ0B,GAAkB9D,EAAAA,MAO/B,kBAAkBoC,MAAAA,CAAAA,CAAY0G,OAC7BA,KAAW1G,GAAM2G,cAAcD,KAA8B,MAAnB1G,GAAM0G,WAChD;AACDvD,YAAMnD,IACN0B,GAAkB9D,EAAAA,IAAK;AAAA;;;AAAA,MAMf,QAAPuF,KAAa;AAAA,QACC,SAAbuD,GAAAA,QAEIE,SAASC,eAAezD,EAAAA;AAI/BD,UADG1B,MACGmF,SAASE,gBACd,8BAEAJ,EAAAA,IAGKE,SAASrI,cAEdmI,IACAtD,GAAS2D,MAAM3D,EAAAA,GAKjB1B,KAAoB,MAEpBC,KAAAA;;AAAc,MAGE,SAAb+E,GAECrD,QAAaD,MAAczB,MAAewB,IAAI6D,SAAS5D,OAC1DD,IAAI6D,OAAO5D;OAEN;AAAA,QAEN1B,KAAoBA,MAAqBrE,wBAAM0B,KAAKoE,IAAI8D,UAAAA,GAIpDT,MAFJnD,KAAWvC,IAAS5C,SAASL,yBAENqJ,yBACnBT,KAAUrD,GAAS8D,yBAAAA,CAIlBvF,IAAa;AAAA,UAGQ,QAArBD,GAAAA,MACH2B,KAAW,CAAA,GACNzF,KAAI,GAAGA,KAAIuF,IAAIgE,WAAWrI,QAAQlB,KACtCyF,IAASF,IAAIgE,WAAWvJ,EAAAA,EAAGgG,IAAAA,IAAQT,IAAIgE,WAAWvJ,EAAAA,EAAG8F;AAInD+C,OAAAA,MAAWD,QAGZC,OACED,MAAWC,GAAAA,UAAkBD,GAAAA,UAC/BC,GAAAA,WAAmBtD,IAAIiE,eAExBjE,IAAIiE,YAAaX,MAAWA,GAAAA,UAAmB;;AAAA,QAKlDvD,wBAAUC,KAAKC,IAAUC,IAAU5B,KAAOE,EAAAA,GAGtC8E,GACH7B,KAAAA,MAAqB,CAAA;aAErBhH,KAAIgH,IAAS1G,MAAMO,UACnB2C,wBACC+B,KACAhB,MAAMC,QAAQxE,EAAAA,IAAKA,KAAI;MAACA;OACxBgH,KACA9D,KACAU,KACAC,OAAsB,oBAAbiF,IACThF,IACAb,IACAa,KACGA,GAAkB,CAAA,IAClBZ,IAAAA,OAAsBnB,wBAAcmB,KAAU,CAAA,GACjDa,EAAAA,GAIwB,QAArBD,GAAAA,MACE9D,KAAI8D,GAAkB5C,QAAQlB,OACN,SAAxB8D,GAAkB9D,EAAAA,KAAYO,wBAAWuD,GAAkB9D,EAAAA,CAAAA;AAM7D+D,WAEH,WAAWyB,MAAAA,YACVxF,KAAIwF,GAASM,WAKb9F,OAAMyF,GAASK,SACf9F,OAAMuF,IAAIO,SACI,eAAbgD,MAAAA,CAA4B9I,OAE9B2F,wBAAYJ,KAAK,SAASvF,IAAGyF,GAASK,OAAAA,KAAO,GAG7C,aAAaN,MAAAA,YACZxF,KAAIwF,GAASiE,YACdzJ,OAAMuF,IAAIkE,WAEV9D,wBAAYJ,KAAK,WAAWvF,IAAGyF,GAASgE,SAAAA,KAAS;;AAAA,SAK7ClE;;AASR,SAAgBX,wBAAS7D,KAAK+E,KAAOvE,KAAAA;AAAAA,MAAAA;AAEjB,kBAAA,OAAPR,MAAmBA,IAAI+E,GAAAA,IAC7B/E,IAAIY,UAAUmE;WACXc,KAAAA;AACRlH,8CAAAA,IAAoBkH,KAAGrF,GAAAA;;;AAYzB,SAAgBoD,wBAAQpD,KAAOmI,KAAaC,KAAAA;AAA5C,MACKC,KAoBM5J;AAAAA,MAnBNN,0CAAQiF,WAASjF,0CAAQiF,QAAQpD,GAAAA,IAEhCqI,MAAIrI,IAAMR,SACT6I,IAAEjI,WAAWiI,IAAEjI,YAAYJ,IAAAA,OAAYqD,wBAASgF,KAAG,MAAMF,GAAAA,IAGjC,SAAzBE,MAAIrI,IAAAA,MAA2B;AAAA,QAC/BqI,IAAEC,qBAAAA,KAAAA;AAEJD,UAAEC,qBAAAA;aACMjD,KAAAA;AACRlH,gDAAAA,IAAoBkH,KAAG8C,GAAAA;;AAIzBE,QAAEvH,OAAOuH,IAAAA,MAAe;;AAAA,MAGpBA,MAAIrI,IAAAA,IAAAA,MACCvB,MAAI,GAAGA,MAAI4J,IAAE1I,QAAQlB,MACzB4J,KAAE5J,GAAAA,KACL2E,wBAAQiF,IAAE5J,GAAAA,GAAI0J,KAAkC,cAAA,OAAdnI,IAAMX,IAAAA;AAKtC+I,SAA4B,QAAdpI,IAAAA,OAAoBhB,wBAAWgB,IAAAA,GAAAA,GAIlDA,IAAAA,MAAaA,IAAAA,MAAAA;;AAId,SAASoG,wBAASrH,KAAOuH,GAAO/F,KAAAA;AAAAA,SACxBgI,KAAKrI,YAAYnB,KAAOwB,GAAAA;;ACpfhC,SAAgB4F,0CAAOnG,KAAO6B,KAAW2G,KAAAA;AAAzC,MAMKhG,KAOAb,IAUAD;AAtBAvD,4CAAAA,MAAeA,0CAAAA,GAAc6B,KAAO6B,GAAAA,GAYpCF,MAPAa,MAAqC,cAAA,OAAhBgG,OAQtB,OACCA,OAAeA,IAAAA,OAA0B3G,IAAAA,KAQzCH,KAAc,CAAA,GAClBI,wBACCD,KARD7B,OAAAA,CACGwC,OAAegG,OACjB3G,KAAAA,MACazC,0CAAciB,2CAAU,MAAM;IAACL;GAAAA,GAS5C2B,MAAYjD,yBACZA,yBAAAA,WACAmD,IAAUE,iBAAAA,CACTS,OAAegG,MACb;IAACA;MACD7G,KACA,OACAE,IAAU4G,aACVvK,wBAAM0B,KAAKiC,IAAUiG,UAAAA,IACrB,MACHpG,IAAAA,CACCc,OAAegG,MACbA,MACA7G,KACAA,GAAAA,MACAE,IAAU4G,YACbjG,GAAAA,GAIDR,wBAAWN,IAAa1B,GAAAA;;ARrCZ0I,0BAAQC,wBAAUD,OCfzBE,4CAAU;EAAA,KSJT,SAAqBC,KAAOC,KAAAA;AAAAA,aAE9BC,KAAWC,KAAMC,KAEbH,MAAQA,IAAAA,KAAAA,MACVC,MAAYD,IAAAA,QAAAA,CAAsBC,IAAAA,GAAAA,KAAAA;AAAAA,WAErCC,MAAOD,IAAUG,gBAE4B,QAAjCF,IAAKG,6BAChBJ,IAAUK,SAASJ,IAAKG,yBAAyBN,GAAAA,CAAAA,GACjDI,MAAUF,IAAAA,MAGwB,QAA/BA,IAAUM,sBACbN,IAAUM,kBAAkBR,GAAAA,GAC5BI,MAAUF,IAAAA,MAIPE,IAAAA,QACKF,IAAAA,MAA0BA;aAE3BO,KAAAA;AACRT,YAAQS;;AAAAA,UAKLT;;GRjCHU,0BAAU,GA6FDC,4CAAiB,SAAAV,KAAAA;AAAAA,SACpB,QAATA,OAAAA,WAAiBA,IAAMI;GCvExBO,0CAAUC,UAAUN,WAAW,SAASO,KAAQC,KAAAA;AAAAA,MAE3CC;AAEHA,QADsB,QAAnBC,KAAAA,OAA2BA,KAAAA,QAAoBA,KAAKC,QACnDD,KAAAA,MAEAA,KAAAA,MAAkBE,wBAAO,CAAA,GAAIF,KAAKC,KAAAA,GAGlB,cAAA,OAAVJ,QAGVA,MAASA,IAAOK,wBAAO,CAAA,GAAIH,GAAAA,GAAIC,KAAKG,KAAAA,IAGjCN,OACHK,wBAAOH,KAAGF,GAAAA,GAIG,QAAVA,OAEAG,KAAAA,QACCF,OAAUE,KAAAA,IAAsBI,KAAKN,GAAAA,GACzCO,wBAAcL,IAAAA;GAUhBL,0CAAUC,UAAUU,cAAc,SAASR,KAAAA;AACtCE,OAAAA,QAAAA,KAAAA,MAAAA,MAKCF,OAAUE,KAAAA,IAAsBI,KAAKN,GAAAA,GACzCO,wBAAcL,IAAAA;GAchBL,0CAAUC,UAAUW,SAASC,2CAyFzBC,0BAAgB,CAAA,GAQdC,0BACa,cAAA,OAAXC,UACJA,QAAQf,UAAUgB,KAAKC,KAAKF,QAAQG,QAAAA,CAAAA,IACpCC,YA2CJC,wBAAAA,MAAyB,GC9NdC,0BAAI;APEf,IAAIxB,0BAAU;AAqBd,SAASyB,0CAAYC,IAAMhB,IAAOiB,GAAKC,GAAUC,GAAAA;AAAAA,MAK/CC,GACAN,GAFGO,IAAkB,CAAA;AAAA,OAGjBP,KAAKd,GACA,UAALc,IACHM,IAAMpB,GAAMc,CAAAA,IAEZO,EAAgBP,CAAAA,IAAKd,GAAMc,CAAAA;AAAAA,MAIvBjC,IAAQ;IACbmC,MAAAA;IACAhB,OAAOqB;IACPJ,KAAAA;IACAG,KAAAA;IAAAA,KACW;IAAA,IACF;IAAA,KACD;IAAA,KACF;IAAA,KAAA;IACIE,KACE;IAAA,KACA;IACZrC,aAAAA;IAAaqC,KAAAA,EACAhC;IACb4B,UAAAA;IACAC,QAAAA;;AAAAA,MAKmB,cAAA,OAATH,OAAwBI,IAAMJ,GAAKO,cAAAA,MACxCT,KAAKM,EAAAA,YACEC,EAAgBP,CAAAA,MAC1BO,EAAgBP,CAAAA,IAAKM,EAAIN,CAAAA;AAAAA,UAIxBnC,GAAAA,2CAAQE,UAAOF,GAAAA,2CAAQE,MAAMA,CAAAA,GAC1BA;;AgBpER,SAAS,0BAAI,KAAa,OAAe;AACvC,MAAI;AACF,WAAO,aAAa,cAAc,GAAG,EAAE,IAAI,KAAK,UAAU,KAAK;WACxD,OAAO;EAAA;;AAGlB,SAAS,0BAAI,KAAkB;AAC7B,MAAI;AACF,UAAM,QAAQ,OAAO,aAAa,cAAc,GAAG,EAAE;AAErD,QAAI,MACF,QAAO,KAAK,MAAM,KAAK;WAElB,OAAO;EAAA;;IAGlB,2CAAe;OAAE;OAAK;;AChBtB,IAAM,8BAAQ,oBAAI,IAAG;AACrB,IAAM,iCAAW;EACf;IAAE,GAAG;IAAI,OAAO;;EACb;IAAD,GAAG;IAAI,OAAO;;EACb;IAAD,GAAG;IAAM,OAAO;;EACR;IAAR,GAAG;IAAI,OAAO;;EACb;IAAD,GAAG;IAAM,OAAO;;EACV;IAAN,GAAG;IAAI,OAAO;;EACb;IAAD,GAAG;IAAI,OAAO;;EACb;IAAD,GAAG;IAAG,OAAO;;EACZ;IAAD,GAAG;IAAG,OAAO;;EACN;IAAP,GAAG;IAAG,OAAO;;EACZ;IAAD,GAAG;IAAG,OAAO;;EACT;IAAJ,GAAG;IAAG,OAAO;;;AAGjB,SAAS,sCAAgB;AACvB,aAAW,EAAA,GAAG,MAAO,KAAM,gCAAU;AACnC,QAAI,kCAAY,KAAK,EACnB,QAAO;;;AAKb,SAAS,uCAAiB;AACxB,MAAI,kCAAY,MAAI,EAClB,QAAO;AAGT,SAAO;;AAGT,SAAS,kCAAY,OAAO;AAC1B,MAAI,4BAAM,IAAI,KAAK,EACjB,QAAO,4BAAM,IAAI,KAAK;AAGxB,QAAM,YAAY,uCAAiB,KAAK;AACxC,8BAAM,IAAI,OAAO,SAAS;AAE1B,SAAO;;AAIT,IAAM,0CAAoB,MAAM;AAC9B,MAAI,MAAM;AACV,MAAI;AACF,QAAI,CAAC,UAAU,UAAU,SAAS,OAAO,EACvC,OAAM,SACH,cAAc,QAAQ,EACtB,WAAW,MAAM;MAAE,oBAAoB;KAAM;UAE5C;EAAA;AAGR,MAAI,CAAC,IACH,QAAO,MAAM;AAGf,QAAM,gBAAgB;AACtB,QAAM,eAAe;AACrB,QAAM,WAAW,KAAK,MAAM,gBAAgB,CAAC;AAG7C,MAAI,OAAO,WAAW;AACtB,MAAI,eAAe;AACnB,MAAI,OAAO,QAAQ,eAAe;AAClC,MAAI,OAAO,SAAS;AAEpB,SAAO,CAAC,YAAY;AAClB,QAAI,UAAU,GAAG,GAAG,eAAe,GAAG,aAAa;AAGnD,QAAI,YAAY;AAChB,QAAI,SAAS,SAAS,GAAG,EAAE;AAG3B,QAAI,YAAY;AAChB,QAAI,SAAS,SAAS,cAAc,EAAE;AAEtC,UAAM,IAAI,IAAI,aAAa,GAAG,GAAG,cAAc,aAAa,EAAE;AAC9D,UAAM,QAAQ,EAAE;AAChB,QAAI,IAAI;AAGR,WAAO,IAAI,SAAS,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,EAAC;AAGrC,QAAI,KAAK,MACP,QAAO;AAKT,UAAM,IAAI,eAAiB,IAAI,IAAK;AACpC,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,YAAY;AACzC,UAAM,IAAI,IAAI,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AAEvC,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EACnC,QAAO;AAKT,QAAI,IAAI,YAAY,OAAO,EAAE,SAAS,aACpC,QAAO;AAIT,WAAO;;GAEV;IAED,2CAAe;iBAAE;kBAAe;;AC9GhC,IAAM,iCAAW;EACf;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF,IAAI,8BAAoB;AAExB,SAAS,0BAAI,OAAuB;AAClC,kCAAU,+BAAQ,GAAA,0CAAM,IAAI,YAAY,KAAK,CAAA;AAE7C,QAAM,UAAU,MAAM,MAAM;AAC5B,MAAI,CAAC,QAAS;AAEd,8BAAM,OAAO,MAAM,4BAAM,OAAO,IAAI;AACpC,8BAAM,OAAO,KAAK;AAElB,GAAA,GAAA,0CAAM,IAAI,QAAQ,OAAO;AACzB,GAAA,GAAA,0CAAM,IAAI,cAAc,2BAAK;;AAG/B,SAAS,0BAAI,EAAA,iBAAiB,QAAS,GAAI;AACzC,MAAI,CAAC,gBAAiB,QAAO,CAAA;AAE7B,kCAAU,+BAAQ,GAAA,0CAAM,IAAI,YAAY;AACxC,MAAI,WAAW,CAAA;AAEf,MAAI,CAAC,6BAAO;AACV,kCAAQ,CAAA;AAER,aAAS,KAAK,+BAAS,MAAM,GAAG,OAAO,GAAG;AACxC,YAAM,UAAU,+BAAS,CAAC;AAE1B,kCAAM,OAAO,IAAI,UAAU;AAC3B,eAAS,KAAK,OAAO;;AAGvB,WAAO;;AAGT,QAAM,MAAM,kBAAkB;AAC9B,QAAM,QAAO,GAAA,0CAAM,IAAI,MAAM;AAE7B,WAAS,WAAW,4BAClB,UAAS,KAAK,OAAO;AAGvB,WAAS,KAAK,CAAC,GAAG,MAAM;AACtB,UAAM,SAAS,4BAAM,CAAC;AACtB,UAAM,SAAS,4BAAM,CAAC;AAEtB,QAAI,UAAU,OACZ,QAAO,EAAE,cAAc,CAAC;AAG1B,WAAO,SAAS;GACjB;AAED,MAAI,SAAS,SAAS,KAAK;AACzB,UAAM,aAAa,SAAS,MAAM,GAAG;AACrC,eAAW,SAAS,MAAM,GAAG,GAAG;AAEhC,aAAS,aAAa,YAAY;AAChC,UAAI,aAAa,KAAM;AACvB,aAAO,4BAAM,SAAS;;AAGxB,QAAI,QAAQ,SAAS,QAAQ,IAAI,KAAK,IAAI;AACxC,aAAO,4BAAM,SAAS,SAAS,SAAS,CAAC,CAAC;AAC1C,eAAS,OAAO,IAAI,GAAG,IAAI;;AAG7B,KAAA,GAAA,0CAAM,IAAI,cAAc,2BAAK;;AAG/B,SAAO;;IAGT,2CAAe;OAAE;OAAK;YAAK;;;AG7F3B,4BAAiB,KAAK,MAAM,sjBAAspB;ACAlrB,IAAA,2CAAe;EACb,WAAW;IACT,OAAO;;EAET,cAAc;IACZ,OAAO;;EAET,mBAAmB;IACjB,OAAO;;EAET,mBAAmB;IACjB,OAAO;;EAET,iBAAiB;IACf,OAAO;;EAET,WAAW;IACT,OAAO;;EAET,cAAc;IACZ,OAAO;IACP,SAAS;MAAC;MAAG;MAAG;MAAG;MAAG;MAAG;MAAI;MAAI;MAAM;MAAI;MAAM;MAAI;;;EAEvD,cAAc;IACZ,OAAO,CAAA;;EAET,OAAO;IACL,OAAO;IACP,SAAS;MAAC;MAAQ;MAAW;;;EAE/B,QAAQ;IACN,OAAO;IACP,SAAS;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;EAGJ,iBAAiB;IACf,OAAO;;EAET,aAAa;IACX,OAAO;IACP,SAAS;MAAC;MAAO;MAAU;;;EAE7B,gBAAgB;IACd,OAAO;;EAET,gBAAgB;IACd,OAAO;;EAET,SAAS;IACP,OAAO;;EAET,cAAc;IACZ,OAAO;;EAET,iBAAiB;IACf,OAAO;IACP,SAAS;MAAC;MAAO;MAAU;;;EAE7B,gBAAgB;IACd,OAAO;IACP,SAAS;MAAC;MAAU;MAAU;;;EAEhC,KAAK;IACH,OAAO;IACP,SAAS;MAAC;MAAU;MAAS;MAAY;MAAU;;;EAErD,MAAM;IACJ,OAAO;IACP,SAAS;MAAC;MAAG;MAAG;MAAG;MAAG;MAAG;;;EAE3B,kBAAkB;IAChB,OAAO;IACP,SAAS;MAAC;MAAW;MAAU;;;EAEjC,OAAO;IACL,OAAO;IACP,SAAS;MAAC;MAAQ;MAAS;;;;EAI7B,YAAY;EACZ,eAAe;EACf,QAAQ;EACR,MAAM;EACN,MAAM;;EAGN,aAAa;EACb,mBAAmB;EACnB,kBAAkB;EAClB,gBAAgB;EAChB,eAAe;;EAGf,cAAc;IACZ,YAAY;IACZ,OAAO;;;AF5GJ,IAAI,4CAAO;AACX,IAAI,4CAAO;AAElB,IAAM,mCAAa,CAAA;AACnB,eAAe,gCAAU,KAAK;AAC5B,MAAI,iCAAW,GAAG,EAChB,QAAO,iCAAW,GAAG;AAGvB,QAAM,WAAW,MAAM,MAAM,GAAG;AAChC,QAAM,OAAO,MAAM,SAAS,KAAI;AAEhC,mCAAW,GAAG,IAAI;AAClB,SAAO;;AAGT,IAAI,gCAAgC;AAEpC,IAAI,qCAAe;AACnB,IAAI,oCAAc;AAEX,SAAS,0CAAK,SAAS,EAAA,OAAQ,IAAK,CAAA,GAAI;AAC7C,oCACG,gCAAU,IAAI,QAAQ,CAAC,YAAY;AAClC,yCAAe;GAChB;AAEH,MAAI,QACF,6BAAM,OAAO;WACJ,UAAU,CAAC,kCACpB,SAAQ,KACN,KAAK,MAAM,6FAA6F;AAI5G,SAAO;;AAGT,eAAe,4BAAM,OAAO;AAC1B,sCAAc;AAEd,MAAI,EAAA,cAAc,KAAK,OAAQ,IAAK;AACpC,mBAAiB,gBAAe,GAAA,0CAAY,aAAa;AACzD,UAAQ,OAAM,GAAA,0CAAY,IAAI;AAC9B,aAAW,UAAS,GAAA,0CAAY,OAAO;AAEvC,MAAI,CAAC,2CAAM;AACT,iDACG,OAAO,MAAM,SAAS,aAAa,MAAM,MAAM,KAAI,IAAK,MAAM,SAC9D,MAAM,gCACL,6DAA6D,YAAY,IAAI,GAAG,OAAO;AAG3F,8CAAK,YAAY,CAAA;AACjB,8CAAK,UAAU,CAAA;AAEf,8CAAK,WAAW,QAAQ;MACtB,IAAI;MACJ,QAAQ,CAAA;KACT;AAED,eAAW,SAAS,0CAAK,SAAS;AAChC,YAAM,UAAU,0CAAK,QAAQ,KAAK;AAClC,YAAM,QAAQ,0CAAK,OAAO,OAAO;AACjC,UAAI,CAAC,MAAO;AAEZ,YAAM,YAAY,MAAM,UAAU,CAAA;AAClC,YAAM,QAAQ,KAAK,KAAK;;AAG1B,8CAAK,qBAAqB,0CAAK;QAE/B,2CAAK,aAAa,0CAAK,WAAW,OAAO,CAAC,MAAM;AAC9C,UAAM,WAAW,CAAC,CAAC,EAAE;AACrB,QAAI,CAAC,SAAU,QAAO;AAEtB,WAAO;GACR;AAGH,+CACG,OAAO,MAAM,SAAS,aAAa,MAAM,MAAM,KAAI,IAAK,MAAM,UAC9D,UAAU,QACP,GAAA,uBAAA,yBAAA,KACA,MAAM,gCACJ,6DAA6D,MAAM,OAAO;AAGlF,MAAI,MAAM,OACR,UAAS,KAAK,MAAM,QAAQ;AAC1B,QAAI,SAAS,CAAC;AACd,UAAM,WAAW,MAAM,OAAO,CAAC;AAC/B,UAAM,eAAe,MAAM,OAAO,IAAI,CAAC;AAEvC,QAAI,CAAC,SAAS,UAAU,CAAC,SAAS,OAAO,OAAQ;AAEjD,aAAS,OAAO,SAAS,KAAK,UAAU,IAAI,CAAC;AAC7C,aAAS,SAAS,SAAS,OAAO,0CAAK,WAAW;AAElD,QAAI,gBAAgB,CAAC,SAAS,KAC5B,UAAS,SAAS,aAAa,UAAU;AAG3C,8CAAK,WAAW,KAAK,QAAQ;AAE7B,eAAW,SAAS,SAAS,OAC3B,2CAAK,OAAO,MAAM,EAAE,IAAI;;AAK9B,MAAI,MAAM,WACR,2CAAK,aAAa,0CAAK,mBACpB,OAAO,CAAC,MAAM;AACb,WAAO,MAAM,WAAW,QAAQ,EAAE,EAAE,KAAK;GAC1C,EACA,KAAK,CAAC,IAAI,OAAO;AAChB,UAAM,KAAK,MAAM,WAAW,QAAQ,GAAG,EAAE;AACzC,UAAM,KAAK,MAAM,WAAW,QAAQ,GAAG,EAAE;AAEzC,WAAO,KAAK;GACb;AAGL,MAAI,uBAAuB;AAC3B,MAAI,iBAAiB;AACrB,MAAI,OAAO,UAAU;AACnB,4BAAuB,GAAA,0CAAc,cAAa;AAClD,qBAAiB,MAAM,mBAAkB,GAAA,0CAAc,eAAc;;AAGvE,MAAI,gBAAgB,0CAAK,WAAW;AACpC,MAAI,mBAAmB;AACvB,SAAO,iBAAiB;AACtB,UAAM,WAAW,0CAAK,WAAW,aAAa;AAE9C,QAAI,SAAS,MAAM,YAAY;AAC7B,UAAI,EAAA,iBAAiB,QAAS,IAAK;AAEnC,wBACE,mBAAmB,IACf,mBACA,GAAA,0CAAY,gBAAgB;AAClC,kBAAY,WAAU,GAAA,0CAAY,QAAQ;AAE1C,eAAS,UAAS,GAAA,0CAAe,IAAI;;;OAA4B;;AAGnE,QAAI,CAAC,SAAS,UAAU,CAAC,SAAS,OAAO,QAAQ;AAC/C,gDAAK,WAAW,OAAO,eAAe,CAAC;AACvC;;AAGF,UAAM,EAAA,cAAe,IAAK;AAC1B,QAAI,eAAe;AACjB,YAAM,OAAO,cAAc,SAAS,EAAE;AACtC,UAAI,QAAQ,CAAC,SAAS,KACpB,UAAS,OAAO;;AAIpB,QAAI,aAAa,SAAS,OAAO;AACjC,WAAO,cAAc;AACnB,YAAM,UAAU,SAAS,OAAO,UAAU;AAC1C,YAAM,QAAQ,QAAQ,KAAK,UAAU,0CAAK,OAAO,OAAO;AAExD,YAAM,SAAS,MAAM;AACnB,iBAAS,OAAO,OAAO,YAAY,CAAC;;AAGtC,UACE,CAAC,SACA,MAAM,gBAAgB,MAAM,aAAa,SAAS,MAAM,EAAE,GAC3D;AACA,eAAM;AACN;;AAGF,UAAI,wBAAwB,MAAM,UAAU,sBAAsB;AAChE,eAAM;AACN;;AAGF,UAAI,kBAAkB,SAAS,MAAM,SACnC;AAAA,YAAI,EAAC,GAAA,2CAAU,SAAS,MAAM,EAAE,GAAG;AACjC,iBAAM;AACN;;;AAIJ,UAAI,CAAC,MAAM,QAAQ;AACjB,2BAAmB;AACnB,cAAM,SACJ,MACA;UACE;YAAC,MAAM;YAAI;;UACX;YAAC,MAAM;YAAM;;UACb;YAAC,MAAM;YAAU;;UACjB;YAAC,MAAM;YAAW;;UAEjB,IAAI,CAAC,CAAC,SAAS,KAAK,MAAM;AACzB,cAAI,CAAC,QAAS;AACd,kBAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU;YAAC;aACzC,IAAI,CAAC,WAAW;AACf,oBAAQ,QAAQ,OAAO,MAAK,WAAA,IAAgB;cAAC;eAAS,IACpD,CAAC,MAAM,EAAE,YAAW,CAAE;WAEzB,EACA,KAAI;SACR,EACA,KAAI,EACJ,OAAO,CAAC,MAAM,KAAK,EAAE,KAAI,CAAE,EAC3B,KAAK,GAAG;AAEb,YAAI,MAAM,UACR,YAAW,YAAY,MAAM,WAAW;AACtC,cAAI,0CAAK,UAAU,QAAQ,EAAG;AAC9B,oDAAK,UAAU,QAAQ,IAAI,MAAM;;AAIrC,YAAI,YAAY;AAChB,mBAAW,QAAQ,MAAM,OAAO;AAC9B,cAAI,CAAC,KAAM;AACX;AAEA,gBAAM,EAAA,OAAQ,IAAK;AACnB,cAAI,QAAQ;AACV,sDAAK,QAAQ,MAAM,IAAI,MAAM;AAC7B,kBAAM,UAAU,IAAI,MAAM;;AAG5B,gBAAM,iBACJ,aAAa,IAAI,KAAK,cAAc,SAAS;AAC/C,eAAK,aAAa,IAAI,MAAM,EAAE,IAAI,cAAc;;;;;AAMxD,MAAI,iBACF,EAAA,GAAA,0CAAY,MAAK;AAGnB,qCAAY;;AAGP,SAAS,0CAAS,OAAO,cAAc,SAAS;AACrD,YAAU,QAAQ,CAAA;AAElB,QAAM,SAAS,CAAA;AACf,WAAS,KAAK,aACZ,QAAO,CAAC,IAAI,0CAAQ,GAAG,OAAO,cAAc,OAAO;AAGrD,SAAO;;AAGF,SAAS,0CAAQ,UAAU,OAAO,cAAc,SAAS;AAC9D,QAAM,WAAW,aAAa,QAAQ;AACtC,MAAI,QACD,WAAW,QAAQ,aAAa,QAAQ,MACxC,MAAM,QAAQ,KAAK,QAAQ,MAAM,QAAQ,KAAK,SAC3C,MAAM,QAAQ,IACd;AAEN,MAAI,CAAC,SACH,QAAO;AAGT,MACE,SAAS,QACT,SAAS,SACT,OAAO,SAAS,SAAS,OAAO,OAAK;AAErC,QAAI,OAAO,SAAS,SAAS,UAC3B,SAAQ,SAAS,UAAU,QAAQ;QAEnC,SAAQ,SAAS,MAAM,YAAY,KAAK;;AAI5C,MAAI,SAAS,aAAa,MACxB,SAAQ,SAAS,UAAU,KAAK;AAGlC,MACE,SAAS,QACR,SAAS,WAAW,SAAS,QAAQ,QAAQ,KAAK,KAAK,GAExD,SAAQ,SAAS;AAGnB,SAAO;;AD5ST,IAAM,yCAAgB;AACtB,IAAI,6BAAO;AAEX,SAAS,0BAAI,SAAS;AACpB,MAAI,QAAQ,GACV,QAAO;AAGT,UACE,GAAA,2CAAK,OAAO,OAAO,MACnB,GAAA,2CAAK,QAAO,GAAA,2CAAK,QAAQ,OAAO,CAAC,MACjC,GAAA,2CAAK,QAAO,GAAA,2CAAK,QAAQ,OAAO,CAAC;;AAIrC,SAAS,8BAAQ;AACf,+BAAO;;AAGT,eAAe,6BAAO,OAAO,EAAA,YAAY,OAAQ,IAAK,CAAA,GAAI;AACxD,MAAI,CAAC,SAAS,CAAC,MAAM,KAAI,EAAG,OAAQ,QAAO;AAC3C,iBAAe,aAAa;AAE5B,SAAM,GAAA,2CAAK,MAAM;IAAE,QAAQ,UAAU;GAAsB;AAE3D,QAAM,SAAS,MACZ,YAAW,EACX,QAAO,SAAU,KAAK,EACtB,MAAK,SAAA,EACL,OAAO,CAAC,MAAM,GAAG,UAAU;AAC1B,WAAO,KAAK,KAAI,KAAM,MAAM,QAAQ,IAAI,KAAK;GAC9C;AAEH,MAAI,CAAC,OAAO,OAAQ;AAEpB,MAAI,OAAO,+BAAS,6BAAO,OAAO,QAAO,GAAA,2CAAK,MAAM;AACpD,MAAI,SAAS;AAEb,aAAW,UAAS,QAAQ;AAC1B,QAAI,CAAC,KAAK,OAAQ;AAElB,cAAU,CAAA;AACV,aAAS,CAAA;AAET,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,MAAM,OAAQ;AACnB,YAAM,QAAQ,MAAM,OAAO,QAAQ,IAAI,MAAK,EAAE;AAC9C,UAAI,SAAS,GAAI;AAEjB,cAAQ,KAAK,KAAK;AAClB,aAAO,MAAM,EAAE,MAAM,OAAO,MAAM,EAAE,IAAI;AACxC,aAAO,MAAM,EAAE,KAAK,MAAM,MAAM,SAAQ,IAAI,QAAQ;;AAGtD,WAAO;;AAGT,MAAI,QAAQ,SAAS,EACnB,QAAO;AAGT,UAAQ,KAAK,CAAC,GAAG,MAAM;AACrB,UAAM,SAAS,OAAO,EAAE,EAAE;AAC1B,UAAM,SAAS,OAAO,EAAE,EAAE;AAE1B,QAAI,UAAU,OACZ,QAAO,EAAE,GAAG,cAAc,EAAE,EAAE;AAGhC,WAAO,SAAS;GACjB;AAED,MAAI,QAAQ,SAAS,WACnB,WAAU,QAAQ,MAAM,GAAG,UAAU;AAGvC,SAAO;;IAGT,2CAAe;UAAE;OAAQ;SAAK;oBAAO;;AJ5E9B,IAAM,4CAAY;EACvB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;ADZK,SAAS,0CAAU,GAAQ,GAAiB;AACjD,SACE,MAAM,QAAQ,CAAC,KACf,MAAM,QAAQ,CAAC,KACf,EAAE,WAAW,EAAE,UACf,EAAE,MAAM,CAAC,KAAK,UAAU,OAAO,EAAE,KAAK,CAAC;;AAIpC,eAAe,0CAAM,SAAS,GAAG;AACtC,WAAS,KAAK;OAAI,MAAM,MAAM,EAAE,KAAI;IAClC,OAAM,IAAI,QAAQ,qBAAqB;;AAIpC,SAAS,0CAAa,OAAO,EAAA,YAAc,EAAC,IAAK,CAAA,GAAI;AAC1D,QAAM,OACJ,MAAM,MAAM,SAAS,MACpB,MAAM;AACL,gBAAY;AACZ,WAAO,MAAM,MAAM,SAAS;KAC7B;AAEH,QAAM,YAAiB;IACrB,IAAI,MAAM;IACV,MAAM,MAAM;IACZ,QAAQ,KAAK;IACb,SAAS,KAAK;IACd,UAAU,MAAM;IAChB,YAAY,KAAK,cAAc,MAAM;;AAGvC,MAAI,MAAM,MAAM,SAAS,EACvB,WAAU,OAAO,YAAY;AAG/B,MAAI,KAAK,IACP,WAAU,MAAM,KAAK;AAGvB,MAAI,MAAM,WAAW,MAAM,QAAQ,OACjC,WAAU,UAAU,MAAM;AAG5B,MAAI,MAAM,aAAa,MAAM,UAAU,OACrC,WAAU,YAAY,MAAM;AAG9B,SAAO;;ASlDT,IAAM,mCAAa;EACjB,UAAU;IACR,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,SACE,GAAA,2CAAC,OAAG;IAAC,OAAM;IAA6B,SAAQ;eAC9C,GAAA,2CAAC,QAAI;MAAC,GAAE;;;EAIZ,OAAO;IACL,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,OAAO;IACL,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,UAAU;IACR,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;;SAC9C,GAAA,2CAAC,QAAI;UAAC,GAAE;;SACR,GAAA,2CAAC,QAAI;UAAC,GAAE;;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,QAAQ;IACN,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;;SAC9C,GAAA,2CAAC,QAAI;UAAC,GAAE;;SACR,GAAA,2CAAC,QAAI;UAAC,GAAE;;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,SAAS;IACP,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;;SAC9C,GAAA,2CAAC,QAAI;UAAC,GAAE;;SACR,GAAA,2CAAC,QAAI;UAAC,GAAE;;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,QAAQ;IACN,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;;SAC9C,GAAA,2CAAC,QAAI;UAAC,GAAE;;SACR,GAAA,2CAAC,QAAI;UAAC,GAAE;;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,QAAQ;IACN,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;;SAC9C,GAAA,2CAAC,QAAI;UAAC,GAAE;;SACR,GAAA,2CAAC,QAAI;UAAC,GAAE;;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;EAKd,SAAS;IACP,UACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;IAGZ,QACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAA6B,SAAQ;iBAC9C,GAAA,2CAAC,QAAI;QAAC,GAAE;;;;;AAMhB,IAAM,+BAAS;EACb,QACE,GAAA,2CAAC,OAAG;IAAC,OAAM;IAA6B,SAAQ;eAC9C,GAAA,2CAAC,QAAI;MAAC,GAAE;;;EAIZ,SACE,GAAA,2CAAC,OAAG;IAAC,OAAM;IAA6B,SAAQ;eAC9C,GAAA,2CAAC,QAAI;MAAC,GAAE;;;;IAKd,2CAAe;cAAE;UAAY;;AE7Id,SAAA,yCAAe,OAAO;AACnC,MAAI,EAAA,IAAI,MAAM,MAAO,IAAK;AAE1B,MAAI,MAAM,YAAY;AACpB,UAAM,UAAU,MAAM,WAAW,OAAM,GAAA,0CAAY,gBAAgB;AAEnE,QAAI,SAAS;AACX,WAAK,QAAQ,CAAC;AAEd,UAAI,QAAQ,CAAC,EACX,QAAO,QAAQ,CAAC;;;AAKtB,YAAU,SAAQ,GAAA,0CAAY,IAAI,MAAM,MAAM,MAAM;AACpD,MAAI,CAAC,MAAO,QAAO,MAAM;AAEzB,QAAM,YAAY,MAAM,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC;AAExD,QAAM,WACJ,UAAU,QACT,MAAM,OAAO,YAAY,CAAC,MAAM,cAC7B,OAAO,MAAM,gBAAgB,aAC3B,MAAM,YAAY,MAAM,KAAK,UAAU,OAAO,IAC9C,iDAAiD,MAAM,GAAG,eAAe,MAAM,GAAG,OAAO,UAAU,OAAO,SAC5G;AAEN,QAAM,iBACJ,OAAO,MAAM,sBAAsB,aAC/B,MAAM,kBAAkB,MAAM,GAAG,IACjC,iDAAiD,MAAM,GAAG,eAAe,MAAM,GAAG;AAExF,UACE,GAAA,2CAAC,QAAI;IAAC,OAAM;IAAmB,kBAAgB,MAAM;cAClD,YACC,GAAA,2CAAC,OAAG;MACF,OAAO;QACL,UAAU,MAAM,QAAQ;QACxB,WAAW,MAAM,QAAQ;QACzB,SAAS;;MAEX,KAAK,UAAU,UAAU,UAAU;MACnC,KAAK;SAEL,MAAM,OAAO,YACf,GAAA,2CAAC,QAAI;MACH,OAAO;QACL,UAAU,MAAM;QAChB,YACE;;gBAGH,UAAU;UAGb,GAAA,2CAAC,QAAI;MACH,OAAO;QACL,SAAS;QACT,OAAO,MAAM;QACb,QAAQ,MAAM;QACd,iBAAiB,OAAO,cAAc;QACtC,gBAAgB,GAAG,OAAM,GAAA,2CAAK,MAAM,IAAI,KACtC,OAAM,GAAA,2CAAK,MAAM,IAAI;QAEvB,oBAAoB,GACjB,QAAO,GAAA,2CAAK,MAAM,OAAO,KAAM,UAAU,CAAC,KACvC,QAAO,GAAA,2CAAK,MAAM,OAAO,KAAM,UAAU,CAAC;;;;;AGnE5D,IAAM,0CACJ,OAAO,WAAW,eAAe,OAAO,cACpC,OAAO,cACP;AAES,IAAM,2CAAN,cAA0B,wCAAiB;EACxD,WAAW,qBAAqB;AAC9B,WAAO,OAAO,KAAK,KAAK,KAAK;;EAgB/B,OAAO,QAAQ,CAAA,GAAI;AACjB,aAAS,KAAK,MACZ,MAAK,yBAAyB,GAAG,MAAM,MAAM,CAAC,CAAC;;EAInD,yBAAyB,MAAM,GAAG,UAAU;AAC1C,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAQ,GAAA,2CACZ,MACA;MAAE,CAAC,IAAI,GAAG;OACV,KAAK,YAAY,OACjB,IAAI;AAGN,QAAI,KAAK,UAAU,0BACjB,MAAK,UAAU,0BAA0B;MAAE,CAAC,IAAI,GAAG;KAAO;SACrD;AACL,WAAK,UAAU,MAAM,IAAI,IAAI;AAC7B,WAAK,UAAU,YAAW;;;EAI9B,uBAAuB;AACrB,SAAK,eAAe;AAEpB,QAAI,KAAK,aAAa,KAAK,UAAU,WACnC,MAAK,UAAU,WAAU;;EAzC7B,YAAY,QAAQ,CAAA,GAAI;AACtB,UAAK;AACL,SAAK,QAAQ;AAEb,QAAI,MAAM,UAAU,MAAM,KAAK;AAC7B,UAAI,MAAM;AACV,YAAM,SAAS,MAAM,WAAW,MAAM,MAAM,OAAO,MAAM,IAAI;AAE7D,UAAI,IAAK,KAAI,YAAY;AACzB,UAAI,OAAQ,QAAO,YAAY,IAAI;;;;ACnB1B,IAAM,2CAAN,eAA4B,GAAA,0CAAW;EAQpD,YAAY;AACV,SAAK,aAAa;MAAE,MAAM;KAAQ;;EAGpC,aAAa,QAAQ;AACnB,QAAI,CAAC,OAAQ;AAEb,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,UAAM,cAAc;AAEpB,SAAK,WAAW,aAAa,OAAO,KAAK,WAAW,UAAU;;EAjBhE,YAAY,OAAO,EAAA,OAAQ,IAAK,CAAA,GAAI;AAClC,UAAM,KAAK;AAEX,SAAK,UAAS;AACd,SAAK,aAAa,MAAM;;;ICN5B,2CAAe;EACb,UAAU;EACV,IAAI;EACJ,QAAQ;EACR,YAAY;EACZ,MAAM;IACJ,OAAO;IACP,WAAW,CAAC,UAAU;AAEpB,UAAI,CAAC,KAAK,KAAK,KAAK,EAClB,QAAO,GAAG,KAAK;AAGjB,aAAO;;;;EAKX,MAAK,GAAA,0CAAY;EACjB,OAAM,GAAA,0CAAY;;AJdL,IAAM,2CAAN,eAA2B,GAAA,0CAAW;EAOnD,MAAM,oBAAoB;AACxB,UAAM,SAAQ,GAAA,2CAAS,KAAK,QAAO,GAAA,2CAAY,IAAI;AACnD,UAAM,UAAU;AAChB,UAAM,MAAM,CAAC,cAAc;AACzB,WAAK,YAAY;;AAGnB,WAAM,GAAA,2CAAI;AACV,QAAI,KAAK,aAAc;AAEvB,KAAA,GAAA,4CAAO,GAAA,4CAAC,GAAA,2CAAK;MAAE,GAAG;QAAW,IAAI;;EAdnC,YAAY,OAAO;AACjB,UAAM,KAAK;;;CAHb,GAAA,0CADmB,0CACZ,UAAQ,GAAA,yCAAU;AAoB3B,IAAI,OAAO,mBAAmB,eAAe,CAAC,eAAe,IAAI,UAAU,EACzE,gBAAe,OAAO,YAAY,wCAAY;AmB1BhD,IAAI2C;AAAJ,IAGIC;AAHJ,IAkBIC;AAlBJ,IASIC,0BAAoB,CAAA;AATxB,IAWIC,2BAAgBC,GAAAA,2CAAAA;AAXpB,IAYIC,2BAAkBD,GAAAA,2CAAAA;AAZtB,IAaIE,2BAAeF,GAAAA,2CAAQG;AAb3B,IAcIC,2BAAYJ,GAAAA,2CAAAA;AAdhB,IAeIK,2BAAmBL,GAAAA,2CAAQM;AA+Q/B,SAASC,0BAAAA;AAAAA,MACJC;AAAAA,OAEJC,wBAAkBC,KAAK,SAACC,KAAGC,IAAAA;AAAAA,WAAMD,IAAAA,IAAAA,MAAkBC,GAAAA,IAAAA;GAAAA,GAC5CJ,KAAYC,wBAAkBI,IAAAA,IAAAA,KAC/BL,GAAAA,IAAAA,KAAAA;AAEJA,OAAAA,IAAAA,IAAkCM,QAAQC,uBAAAA,GAC1CP,GAAAA,IAAAA,IAAkCM,QAAQE,uBAAAA,GAC1CR,GAAAA,IAAAA,MAAoC,CAAA;WAC5BS,IAAAA;AACRT,OAAAA,IAAAA,MAAoC,CAAA,IACpCU,GAAAA,2CAAAA,IAAoBD,IAAGT,GAAAA,GAAAA;;;CAtR1BU,GAAAA,2CAAAA,MAAgB,SAAAC,KAAAA;AACfC,4BAAmB,MACfC,2BAAeA,wBAAcF,GAAAA;IAGlCD,GAAAA,2CAAAA,MAAkB,SAAAC,KAAAA;AACbG,6BAAiBA,wBAAgBH,GAAAA,GAGrCI,0BAAe;AAAA,MAETC,MAHNJ,0BAAmBD,IAAAA,KAAAA;AAIfK,SACHA,GAAAA,IAAsBV,QAAQC,uBAAAA,GAC9BS,GAAAA,IAAsBV,QAAQE,uBAAAA,GAC9BQ,GAAAA,MAAwB,CAAA;IAI1BN,GAAAA,2CAAQO,SAAS,SAAAN,IAAAA;AACZO,6BAAcA,wBAAaP,EAAAA;AAAAA,MAEzBQ,KAAIR,GAAAA;AACNQ,QAAKA,GAAAA,OAAaA,GAAAA,IAAAA,IAA0BC,WAsSzB,MArSXnB,wBAAkBoB,KAAKF,EAAAA,KAqSPG,6BAAYZ,GAAAA,2CAAQa,2BAC/CD,2BAAUZ,GAAAA,2CAAQa,0BAvBpB,SAAwBC,KAAAA;AAAAA,QAQnBC,IAPEC,KAAO,WAAA;AACZC,mBAAaC,EAAAA,GACTC,2BAASC,qBAAqBL,EAAAA,GAClCM,WAAWP,GAAAA;OAENI,KAAUG,WAAWL,IAhTR,GAAA;AAmTfG,gCACHJ,KAAMF,sBAAsBG,EAAAA;KAcA3B,uBAAAA,IArS7Ba,0BAAmB;IAGpBF,GAAAA,2CAAAA,MAAkB,SAACC,KAAOqB,IAAAA;AACzBA,KAAYC,KAAK,SAAAjC,KAAAA;AAAAA,QAAAA;AAEfA,UAAAA,IAA2BM,QAAQC,uBAAAA,GACnCP,IAAAA,MAA6BA,IAAAA,IAA2BkC,OAAO,SAAAC,KAAAA;AAAAA,eAAAA,CAC9DA,IAAAA,MAAY3B,wBAAa2B,GAAAA;OAAAA;aAElB1B,KAAAA;AACRuB,SAAYC,KAAK,SAAAd,KAAAA;AACZA,YAAAA,QAAoBA,IAAAA,MAAqB,CAAA;OAAA,GAE9Ca,KAAc,CAAA,IACdtB,GAAAA,2CAAAA,IAAoBD,KAAGT,IAAAA,GAAAA;;GAAAA,GAIrBoC,2BAAWA,wBAAUzB,KAAOqB,EAAAA;IAGjCtB,GAAAA,2CAAQ2B,UAAU,SAAA1B,KAAAA;AACb2B,6BAAkBA,wBAAiB3B,GAAAA;AAAAA,MAIlC4B,IAFCpB,MAAIR,IAAAA;AACNQ,SAAKA,IAAAA,QAERA,IAAAA,IAAAA,GAAgBb,QAAQ,SAAAkC,KAAAA;AAAAA,QAAAA;AAEtBjC,8BAAciC,GAAAA;aACN/B,KAAAA;AACR8B,WAAa9B;;GAAAA,GAGX8B,OAAY7B,GAAAA,2CAAAA,IAAoB6B,IAAYpB,IAAAA,GAAAA;;AA8NlD,IAAIU,0BAA0C,cAAA,OAAzBN;AA2CrB,SAAShB,wBAAckC,KAAAA;AAAAA,MAGhBC,MAAO9B,yBACT+B,MAAUF,IAAAA;AACQ,gBAAA,OAAXE,QACVF,IAAAA,MAAAA,QACAE,IAAAA,IAED/B,0BAAmB8B;;AAOpB,SAASlC,wBAAaiC,KAAAA;AAAAA,MAGfC,MAAO9B;AACb6B,MAAAA,MAAgBA,IAAAA,GAAAA,GAChB7B,0BAAmB8B;;AX3Wb,SAASE,wBAAOC,IAAKC,IAAAA;AAAAA,WAClBC,MAAKD,GAAOD,IAAIE,EAAAA,IAAKD,GAAMC,EAAAA;AAAAA,SACPF;;AASvB,SAASG,wBAAeC,IAAGC,IAAAA;AAAAA,WACxBH,MAAKE,GAAAA,KAAa,eAANF,MAAAA,EAAsBA,MAAKG,IAAI,QAAA;AAAO,WAClDH,MAAKG,GAAAA,KAAa,eAANH,MAAoBE,GAAEF,EAAAA,MAAOG,GAAEH,EAAAA,EAAI,QAAA;AAAO,SAAA;;ACdzD,SAASI,0CAAcC,IAAAA;AAAAA,OACxBN,QAAQM;;CAEdC,0CAAcC,YAAY,KAAIC,GAAAA,8CAENC,uBAAAA,MACxBH,0CAAcC,UAAUG,wBAAwB,SAASC,IAAOC,IAAAA;AAAAA,SACxDC,wBAAeC,KAAKH,OAAOA,EAAAA,KAAUE,wBAAeC,KAAKF,OAAOA,EAAAA;;AEVxE,IAAIG,2BAAcC,GAAAA,2CAAAA;CAClBA,GAAAA,2CAAAA,MAAgB,SAAAC,IAAAA;AACXA,KAAMC,QAAQD,GAAMC,KAAAA,OAAmBD,GAAME,QAChDF,GAAMN,MAAMQ,MAAMF,GAAME,KACxBF,GAAME,MAAM,OAETJ,2BAAaA,wBAAYE,EAAAA;;AAG9B,IAAaG,0BACM,eAAA,OAAVC,UACPA,OAAOC,OACPD,OAAOC,IAAI,mBAAA,KACZ;AAiCOC,IE9CFC,2BAAgBC,GAAAA,2CAAAA;CACtBA,GAAAA,2CAAAA,MAAsB,SAASC,KAAOC,KAAUC,IAAAA;AAAAA,MAC3CF,IAAMG,MAAAA;AAAAA,aAELC,IACAC,KAAQJ,KAEJI,KAAQA,GAAAA,KAAAA,MACVD,KAAYC,GAAAA,QAAqBD,GAAAA,IAAAA,QAChB,QAAjBH,IAAAA,QACHA,IAAAA,MAAgBC,GAAAA,KAChBD,IAAAA,MAAqBC,GAAAA,MAGfE,GAAAA,IAA2BJ,KAAOC,GAAAA;;AAI5CH,0BAAcE,KAAOC,KAAUC,EAAAA;;AAGhC,IAAMI,2BAAaP,GAAAA,2CAAQQ;AAuE3B,SAAgBC,4CAAAA;AAAAA,OAAAA,MAEgB,GAAA,KAC1BC,IAAc,MAAA,KAAA,MACQ;;AAoIrB,SAASC,wBAAUL,KAAAA;AAAAA,MAErBD,MAAYC,IAAAA,GAAAA;AAAAA,SACTD,OAAaA,IAAAA,OAAwBA,IAAAA,IAAqBC,GAAAA;;ACjOlE,SAAgBM,4CAAAA;AAAAA,OACVC,IAAQ,MAAA,KACRC,IAAO;;CDcbC,GAAAA,2CAAQC,UAAU,SAASC,KAAAA;AAAAA,MAEpBC,MAAYD,IAAAA;AACdC,SAAaA,IAAAA,OAChBA,IAAAA,IAAAA,GAOGA,OAAAA,SAAaD,IAAAA,QAChBA,IAAME,OAAO,OAGVC,2BAAYA,wBAAWH,GAAAA;IAiE5BI,0CAASC,YAAY,KAAIC,GAAAA,8CAAAA,MAOa,SAASC,KAASC,KAAAA;AAAAA,MACjDC,KAAsBD,IAAAA,KAGtBE,KAAIC;AAEW,UAAjBD,GAAEE,MACLF,GAAEE,IAAc,CAAA,IAEjBF,GAAEE,EAAYC,KAAKJ,EAAAA;AAAAA,MAEbK,KAAUC,wBAAUL,GAAAA,GAAAA,GAEtBM,KAAAA,OACEC,KAAa,WAAA;AACdD,WAEJA,KAAAA,MACAP,GAAAA,MAAiC,MAE7BK,KACHA,GAAQI,EAAAA,IAERA,GAAAA;;AAIFT,KAAAA,MAAiCQ;AAAAA,MAE3BC,KAAuB,WAAA;AAAA,QAAA,CAAA,EACrBR,GAAAA,KAA2B;AAAA,UAG7BA,GAAES,MAAAA,KAAkB;AAAA,YACjBC,MAAiBV,GAAES,MAAAA;AACzBT,WAAAA,IAAAA,IAAmB,CAAA,IA5EvB,SAASW,IAAerB,KAAOsB,IAAgBC,IAAAA;AAAAA,iBAC1CvB,QACHA,IAAAA,MAAkB,MAClBA,IAAAA,MACCA,IAAAA,OACAA,IAAAA,IAAgBwB,IAAI,SAAAC,KAAAA;AAAAA,mBACnBJ,IAAeI,KAAOH,IAAgBC,EAAAA;WAAAA,GAGpCvB,IAAAA,OACCA,IAAAA,IAAAA,QAAgCsB,OAC/BtB,IAAAA,OACHuB,GAAeG,aAAa1B,IAAAA,KAAYA,IAAAA,GAAAA,GAEzCA,IAAAA,IAAAA,MAAAA,MACAA,IAAAA,IAAAA,MAA8BuB,MAK1BvB;UAyDHoB,KACAA,IAAAA,IAAAA,KACAA,IAAAA,IAAAA,GAAAA;;AAAAA,UAMEL;AAAAA,WAFJL,GAAEiB,SAAS;QAAA,KAAejB,GAAAA,MAAwB;OAAA,GAG1CK,MAAYL,GAAEE,EAAYgB,IAAAA,IACjCb,KAAUc,YAAAA;;KAUPC,KAAAA,SAAetB,IAAAA;AAChBE,EAAAA,GAAAA,SAAgCoB,MACpCpB,GAAEiB,SAAS;IAAA,KAAejB,GAAAA,MAAwBA,GAAAA,IAAAA,IAAmB,CAAA;GAAA,GAEtEH,IAAQwB,KAAKd,IAAYA,EAAAA;GAG1Bb,0CAASC,UAAU2B,uBAAuB,WAAA;AAAA,OACpCpB,IAAc,CAAA;GAQpBR,0CAASC,UAAU4B,SAAS,SAASC,KAAOf,KAAAA;AAAAA,MACvCR,KAAAA,KAA0B;AAAA,QAIzBA,KAAAA,IAAAA,KAAuB;AAAA,UACpBW,MAAiBa,SAASC,cAAc,KAAA,GACxCC,KAAoB1B,KAAAA,IAAAA,IAAsB,CAAA,EAAA;AAAA,WAAA,IAAA,IAC1B,CAAA,IArJzB,SAAS2B,IAActC,KAAOsB,KAAgBiB,KAAAA;AAAAA,eACzCvC,QACCA,IAAAA,OAAoBA,IAAAA,IAAAA,QACvBA,IAAAA,IAAAA,IAAAA,GAA+BwC,QAAQ,SAAAC,KAAAA;AACR,wBAAA,OAAnBA,IAAAA,OAA+BA,IAAAA,IAAAA;SAAAA,GAG3CzC,IAAAA,IAAAA,MAA2B,OAIJ,SADxBA,MAAQ0C,wBAAO,CAAA,GAAI1C,GAAAA,GAAAA,QAEdA,IAAAA,IAAAA,QAAgCuC,QACnCvC,IAAAA,IAAAA,MAA8BsB,MAE/BtB,IAAAA,MAAmB,OAGpBA,IAAAA,MACCA,IAAAA,OACAA,IAAAA,IAAgBwB,IAAI,SAAAC,KAAAA;AAAAA,iBACnBa,IAAcb,KAAOH,KAAgBiB,GAAAA;SAAAA,IAIjCvC;QA6HJW,KAAAA,KACAW,KACCe,GAAAA,MAAuCA,GAAAA,GAAAA;;AAAAA,SAAAA,MAIf;;AAAA,MAKtBM,KACLxB,IAAAA,QAAoBiB,GAAAA,4CAAcQ,GAAAA,4CAAU,MAAMV,IAAMS,QAAAA;AAAAA,SACrDA,OAAUA,GAAAA,MAAsB,OAE7B;KACNP,GAAAA,4CAAcQ,GAAAA,4CAAU,MAAMzB,IAAAA,MAAmB,OAAOe,IAAMW,QAAAA;IAC9DF;;;AChMF,IAAM7B,0BAAU,SAACgC,KAAMrB,KAAOsB,KAAAA;AAAAA,MAAAA,EACvBA,IAdgB,CAAA,MAcSA,IAfR,CAAA,KAqBtBD,IAAKjD,EAAKmD,OAAOvB,GAAAA,GAQhBqB,IAAKZ,MAAMe,gBACmB,QAA9BH,IAAKZ,MAAMe,YAAY,CAAA,KAAA,CAAcH,IAAKjD,EAAKqD,MAAAA,MAQjDH,MAAOD,IAAKlD,GACLmD,OAAM;AAAA,WACLA,IAAKI,SAAS,IACpBJ,KAAKnB,IAAAA,EAALmB;AAAAA,QAEGA,IA1CiB,CAAA,IA0CMA,IA3CL,CAAA,EAAA;AA8CtBD,QAAKlD,IAAQmD,MAAOA,IA5CJ,CAAA;;;CAmDlBK,0CAAaC,YAAY,KAAIC,GAAAA,8CAAAA,MAEO,SAASC,KAAAA;AAAAA,MACtCC,MAAOC,MACPC,MAAYC,wBAAUH,IAAAA,GAAAA,GAExBI,MAAOJ,IAAKK,EAAKC,IAAIP,GAAAA;AAAAA,SACzBK,IA5DuB,CAAA,KA8DhB,SAAAG,IAAAA;AAAAA,QACAC,KAAmB,WAAA;AACnBR,UAAKS,MAAMC,eAKfN,IAAKO,KAAKJ,EAAAA,GACVK,wBAAQZ,KAAMD,KAAOK,GAAAA,KAHrBG,GAAAA;;AAMEL,UACHA,IAAUM,EAAAA,IAEVA,GAAAA;;GAKHZ,0CAAaC,UAAUgB,SAAS,SAASJ,KAAAA;AAAAA,OACnCK,IAAQ,MAAA,KACRT,IAAO,oBAAIU;AAAAA,MAEVC,OAAWC,GAAAA,2CAAaR,IAAMO,QAAAA;AAChCP,MAAMC,eAAwC,QAAzBD,IAAMC,YAAY,CAAA,KAI1CM,IAASE,QAAAA;AAAAA,WAIDC,MAAIH,IAASI,QAAQD,QAAAA,MAYxBd,EAAKgB,IAAIL,IAASG,GAAAA,GAAKlB,KAAKa,IAAQ;IAAC;IAAG;IAAGb,KAAKa;GAAAA;AAAAA,SAE/CL,IAAMO;GAGdpB,0CAAaC,UAAUyB,qBAAqB1B,0CAAaC,UAAU0B,oBAAoB,WAAA;AAAA,MAAA,MAAA;AAAA,OAOjFlB,EAAKmB,QAAQ,SAACpB,KAAML,KAAAA;AACxBa,4BAAQa,KAAM1B,KAAOK,GAAAA;GAAAA;;AAAAA,IEnHVsB,0BACM,eAAA,OAAVC,UAAyBA,OAAOC,OAAOD,OAAOC,IAAI,eAAA,KAC1D;AFiHsBxB,IE/GjByB,0BAAAA;AF+GiBzB,IE7GjB0B,0BAA6B,eAAA,OAAbC;AF6GC3B,IExGjB4B,0BAAoB,SAAAC,KAAAA;AAAAA,UACP,eAAA,OAAVN,UAA4C,YAAA,OAAZA,OAAAA,IAAAA,iBAAAA,eAGtCO,KAAKD,GAAAA;;CAGRE,GAAAA,2CAAUC,UAAUC,mBAAmB,CAAA,GASvC;EACC;EACA;EACA;EACCC,QAAQ,SAAAC,KAAAA;AACTC,SAAOC,gBAAeN,GAAAA,2CAAUC,WAAWG,KAAK;IAC/CG,cAAAA;IACAC,KAAAA,WAAAA;AAAAA,aACQC,KAAK,YAAYL,GAAAA;;IAEzBM,KAAAA,SAAIC,KAAAA;AACHN,aAAOC,eAAeG,MAAML,KAAK;QAChCG,cAAAA;QACAK,UAAAA;QACAC,OAAOF;OAAAA;;GAAAA;CAAAA;AAiCX,IAAIG,2BAAeC,GAAAA,2CAAQC;AAS3B,SAASC,0BAAAA;AAAAA;AAET,SAASC,0BAAAA;AAAAA,SACDT,KAAKU;;AAGb,SAASC,0BAAAA;AAAAA,SACDX,KAAKY;;CAfbN,GAAAA,2CAAQC,QAAQ,SAAAM,KAAAA;AAAAA,SACXR,4BAAcQ,MAAIR,wBAAaQ,GAAAA,IACnCA,IAAEC,UAAUN,yBACZK,IAAEJ,uBAAuBA,yBACzBI,IAAEF,qBAAqBA,yBACfE,IAAEE,cAAcF;;AAazB,IA2GIG;AA3GJ,IAAIC,0BAAsB;EACzBnB,cAAAA;EACAC,KAAAA,WAAAA;AAAAA,WACQC,KAAKkB;;;AAHd,IAOIC,2BAAeb,GAAAA,2CAAQc;CAC3Bd,GAAAA,2CAAQc,QAAQ,SAAAA,KAAAA;AAAAA,MACXC,MAAOD,IAAMC,MACbC,MAAQF,IAAME,OACdC,MAAkBD;AAAAA,MAGF,YAAA,OAATD,KAAmB;AAAA,QACvBG,KAAAA,OAAmBH,IAAKI,QAAQ,GAAA;AAAA,aAG7BC,MAFTH,MAAkB,CAAA,GAEJD,KAAO;AAAA,UAChBlB,KAAQkB,IAAMI,EAAAA;AAEdC,iCAAgB,eAAND,MAA6B,eAATL,OAInB,YAANK,MAAiB,kBAAkBJ,OAAkB,QAATlB,OAK9C,mBAANsB,MACA,WAAWJ,OACI,QAAfA,IAAMlB,QAINsB,KAAI,UACY,eAANA,MAAAA,SAAoBtB,KAM9BA,KAAQ,KACE,iBAAiBwB,KAAKF,EAAAA,IAChCA,KAAI,eAEJ,6BAA6BE,KAAKF,KAAIL,GAAAA,KAAAA,CACrCQ,wBAAkBP,IAAMD,IAAAA,IAEzBK,KAAI,YACM,aAAaE,KAAKF,EAAAA,IAC5BA,KAAI,cACM,YAAYE,KAAKF,EAAAA,IAC3BA,KAAI,eACM,6BAA6BE,KAAKF,EAAAA,IAC5CA,KAAIA,GAAEI,YAAAA,IACIN,MAAoBO,wBAAYH,KAAKF,EAAAA,IAC/CA,KAAIA,GAAEM,QAAAA,YAAoB,KAAA,EAAOF,YAAAA,IACb,SAAV1B,OACVA,KAAAA,SAGDmB,IAAgBG,EAAAA,IAAKtB;;AAKb,gBAARiB,OACAE,IAAgBU,YAChBC,MAAMC,QAAQZ,IAAgBnB,KAAAA,MAG9BmB,IAAgBnB,SAAQgC,GAAAA,2CAAad,IAAMe,QAAAA,EAAU3C,QAAQ,SAAA4C,KAAAA;AAC5DA,UAAMhB,MAAMiB,WAAAA,MACXhB,IAAgBnB,MAAMqB,QAAQa,IAAMhB,MAAMlB,KAAAA;KAAAA,IAKjC,YAARiB,OAAoD,QAAhCE,IAAgBiB,iBACvCjB,IAAgBnB,SAAQgC,GAAAA,2CAAad,IAAMe,QAAAA,EAAU3C,QAAQ,SAAA4C,KAAAA;AAE3DA,UAAMhB,MAAMiB,WADThB,IAAgBU,WAAAA,MAElBV,IAAgBiB,aAAaf,QAAQa,IAAMhB,MAAMlB,KAAAA,IAGjDmB,IAAgBiB,gBAAgBF,IAAMhB,MAAMlB;KAAAA,IAKhDgB,IAAME,QAAQC,KAEVD,IAAMJ,SAASI,IAAMmB,cACxBxB,wBAAoByB,aAAa,eAAepB,KACzB,QAAnBA,IAAMmB,cAAmBlB,IAAgBL,QAAQI,IAAMmB,YAC3D7C,OAAOC,eAAe0B,KAAiB,aAAaN,uBAAAA;;AAItDG,MAAMuB,WAAWC,yBAEbzB,2BAAcA,wBAAaC,GAAAA;;AAKhC,IAAMyB,2BAAkBvC,GAAAA,2CAAAA;CACxBA,GAAAA,2CAAAA,MAAkB,SAASc,KAAAA;AACtByB,6BACHA,wBAAgBzB,GAAAA,GAEjBJ,0BAAmBI,IAAAA;;AVjNpB,IAAM,oCAAc;EAClB,OAAO;EACP,MAAM;;AAGO,IAAM,2CAAN,eAAyB,GAAA,2CAAa;EAanD,WAAW,UAAU;AACnB,UAAM,EAAA,KAAM,IAAK;AAEjB,QAAI,MAAM;AACR,UAAI,KAAK,IACP,SACE,GAAA,2CAAC,QAAI;QACH,OAAM;QACN,yBAAyB;UAAE,QAAQ,KAAK;;;AAK9C,UAAI,KAAK,IACP,SAAO,GAAA,2CAAC,OAAG;QAAC,KAAK,KAAK;;;AAI1B,UAAM,iBACJ,GAAA,0CAAM,WAAW,SAAS,EAAE,MAAK,GAAA,0CAAM,WAAW;AAEpD,UAAM,QACJ,KAAK,MAAM,SAAS,SAChB,kCAAY,KAAK,MAAM,KAAK,IAC5B,KAAK,MAAM;AAEjB,WAAO,cAAc,KAAK,KAAK;;EAGjC,SAAS;AACP,QAAI,wBAAwB;AAE5B,YACE,GAAA,2CAAC,OAAG;MACF,IAAG;MACH,OAAM;MACN,iBAAe,KAAK,MAAM;MAC1B,KAAK,KAAK,MAAM;iBAEhB,GAAA,2CAAC,OAAG;QAAC,OAAM;;UACR,KAAK,WAAW,IAAI,CAAC,UAAU,MAAM;AACpC,kBAAM,QAAQ,SAAS,SAAQ,GAAA,2CAAK,WAAW,SAAS,EAAE;AAC1D,kBAAM,WACJ,CAAC,KAAK,MAAM,aAAa,SAAS,MAAM,KAAK,MAAM;AAErD,gBAAI,SACF,yBAAwB;AAG1B,oBACE,GAAA,2CAAC,UAAM;cACL,cAAY;cACZ,iBAAe,YAAY;cAC3B;cACA,MAAK;cACL,OAAM;cACN,aAAa,CAAC,MAAM,EAAE,eAAc;cACpC,SAAS,MAAM;AACb,qBAAK,MAAM,QAAQ;;;iBAAe;;wBAGnC,KAAK,WAAW,QAAQ;;WAG9B;WAED,GAAA,2CAAC,OAAG;YACF,OAAM;YACN,OAAO;cACL,OAAO,GAAG,MAAM,KAAK,WAAW,MAAM;cACtC,SAAS,yBAAyB,OAAO,IAAI;cAC7C,WACE,KAAK,MAAM,QAAQ,QACf,yBAAyB,wBAAwB,GAAG,OACpD,cAAc,wBAAwB,GAAG;;;;;;;EAtF3D,cAAc;AACZ,UAAK;AAEL,SAAK,cAAa,GAAA,2CAAK,WAAW,OAAO,CAAC,aAAa;AACrD,aAAO,CAAC,SAAS;KAClB;AAED,SAAK,QAAQ;MACX,YAAY,KAAK,WAAW,CAAC,EAAE;;;;AejBtB,IAAM,2CAAN,eAAkC,GAAA,2CAAa;EAC5D,sBAAsB,WAAW;AAC/B,aAAS,KAAK,WAAW;AACvB,UAAI,KAAK,WAAY;AAErB,UAAI,UAAU,CAAC,KAAK,KAAK,MAAM,CAAC,EAC9B,QAAO;;AAIX,WAAO;;EAGT,SAAS;AACP,WAAO,KAAK,MAAM;;;AlDJtB,IAAM,oCAAc;EAClB,eAAe;;AAGF,IAAM,2CAAN,eAAqB,GAAA,2CAAS;EAc3C,gBAAgB,QAAQ,KAAK,OAAO;AAClC,WAAO;MACL,OAAM,GAAA,0CAAM,IAAI,MAAM,KAAK,MAAM;MACjC,OAAO,KAAK,UAAU,MAAM,KAAK;;;EAIrC,qBAAqB;AACnB,SAAK,OAAM,GAAA,2CAAK,MAAM,QAAQ;AAC9B,SAAK,OAAO;MACV,OAAM,GAAA,2CAAS;MACf,aAAY,GAAA,2CAAS;MACrB,SAAQ,GAAA,2CAAS;MACjB,SAAQ,GAAA,2CAAS;MACjB,cAAa,GAAA,2CAAS;MACtB,iBAAgB,GAAA,2CAAS;MACzB,gBAAe,GAAA,2CAAS;;AAG1B,SAAK,SAAQ;AAEb,QACE,KAAK,MAAM,gBAAgB,SAC3B,KAAK,MAAM,kBAAkB,UAC7B;AACA,cAAQ,KACN,oFAAoF;AAGtF,WAAK,MAAM,iBAAiB;;;EAIhC,oBAAoB;AAClB,SAAK,SAAQ;AAEb,SAAK,aAAa,KAAK,KAAK;AAE5B,QAAI,KAAK,MAAM,WAAW;AACxB,YAAM,EAAA,YAAa,IAAK,KAAK;AAC7B,UAAI,YAAY,QACd,aAAY,QAAQ,MAAK;;;EAK/B,0BAA0B,WAAW;AACnC,SAAK,cAAc,KAAK,YAAY,CAAA;AAEpC,eAAW,MAAK,UACd,MAAK,UAAU,EAAC,IAAI,UAAU,EAAC;AAGjC,iBAAa,KAAK,cAAc;AAChC,SAAK,iBAAiB,WAAW,MAAM;AACrC,UAAI,oBAAoB;AAExB,iBAAW,KAAK,KAAK,WAAW;AAC9B,aAAK,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;AAEhC,YAAI,MAAM,YAAY,MAAM,aAC1B,qBAAoB;;AAIxB,aAAO,KAAK;AACZ,YAAM,YAAY,KAAK,gBAAe;AAEtC,UAAI,kBACF,QAAO,KAAK,MAAM,SAAS;AAG7B,WAAK,SAAS,SAAS;KACxB;;EAGH,uBAAuB;AACrB,SAAK,WAAU;;EAGjB,MAAM,MAAM,YAAY,CAAA,GAAI;AAC1B,WAAM,GAAA,2CAAK,KAAK,KAAK;AAErB,SAAK,SAAQ;AACb,SAAK,UAAS;AAEd,SAAK,SAAS,WAAW,MAAM;AAC7B,WAAK,kBAAiB;AACtB,WAAK,YAAW;KACjB;;EAGH,WAAW;AACT,aAAS,iBAAiB,SAAS,KAAK,kBAAkB;AAC1D,SAAK,QAAO;;EAGd,aAAa;;AACX,aAAS,oBAAoB,SAAS,KAAK,kBAAkB;AAC7D,eAAK,cAAL,mBAAgB,oBAAoB,UAAU,KAAK;AACnD,SAAK,UAAS;;EAGhB,UAAU;AACR,SAAK,kBAAiB;AACtB,SAAK,YAAW;;EAGlB,UAAU,EAAA,SAAW,CAAA,EAAE,IAAK,CAAA,GAAI;AAC9B,QAAI,CAAC,MAAM,QAAQ,MAAM,EACvB,UAAS;MAAC;;AAGZ,eAAW,YAAY,KAAK,WAAW;AACrC,UAAI,OAAO,SAAS,QAAQ,EAAG;AAC/B,eAAS,WAAU;;AAGrB,SAAK,YAAY,CAAA,EAAG,OAAO,MAAM;;EAGnC,WAAW;AACT,UAAM,EAAA,WAAY,KAAK,GAAA;AAEvB,SAAK,KAAK,aAAa,oBAAI,IAAG;AAE9B,UAAM,UAAS,GAAA,2CAAK,WAAW,IAAI,CAAC,aAAa,SAAS,EAAE,EAAE,KAAK,GAAG;AACtE,QAAI,KAAK,UAAU,KAAK,UAAU,OAChC,MAAK,KAAK,OAAO,YAAY,KAAK,KAAK,OAAO,QAAQ,YAAY;AAEpE,SAAK,SAAS;AAEd,SAAK,OAAO,CAAA;AACZ,SAAK,KAAK,UAAU;AAEpB,UAAM,SAAS,CAAC,MAAM,aAAa;AACjC,YAAM,MAAM,CAAA;AACZ,UAAI,eAAe,SAAS;AAC5B,UAAI,UAAU,KAAK;AACnB,WAAK,KAAK,KAAK,GAAG;AAElB,YAAM,WAAW,KAAK,KAAK,SAAS;AACpC,YAAM,SAAS,WAAW,kCAAY,gBAAgB,CAAA,KAAK,GAAA,2CAAS;AACpE,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,KAAK,UAAU;AACtC,WAAK,KAAK,MAAM;AAEhB,aAAO;;AAGT,aAAS,aAAY,YAAY;AAC/B,YAAM,OAAO,CAAA;AACb,UAAI,MAAM,OAAO,MAAM,SAAQ;AAE/B,eAAS,SAAS,UAAS,QAAQ;AACjC,YAAI,IAAI,UAAU,KAAK,WAAU,EAC/B,OAAM,OAAO,MAAM,SAAQ;AAG7B,aAAK,KAAK,WAAW;AACrB,YAAI,KAAK,KAAK;;AAGhB,WAAK,KAAK,WAAW,IAAI,UAAS,IAAI;QAAE,OAAM,GAAA,2CAAS;;OAAU;;;EASrE,UAAU,OAAO;AACf,QAAI,SAAS,OAAQ,QAAO;AAE5B,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,WAAW,8BAA8B;AAC1D,UAAI,KAAK,UAAU,MAAM,MAAK,MAAA,EAAU,QAAO;AAE/C,WAAK,UAAU,iBAAiB,UAAU,KAAK,iBAAiB;;AAGlE,WAAO,KAAK,UAAU,UAAU,SAAS;;EAqC3C,mBAAmB,QAAQ,KAAK,OAAO;AACrC,QAAI,CAAC,MAAM,aAAc;AACzB,UAAM,EAAA,SAAS,gBAAiB,IAAK;AAErC,UAAM,mBAAmB,MAAM;AAC7B,YAAM,EAAA,MAAO,IAAK,QAAQ,sBAAqB;AAC/C,aAAO,KAAK,MAAM,QAAQ,eAAe;;AAG3C,UAAM,WAAW,IAAI,eAAe,MAAM;AACxC,WAAK,UAAU;QAAE,QAAQ;OAAU;AACnC,WAAK,SAAS;QAAE,SAAS,iBAAgB;SAAM,MAAM;AACnD,aAAK,SAAQ;AACb,aAAK,YAAY,MAAM;AACrB,eAAK,kBAAiB;AACtB,eAAK,YAAW;SACjB;OACF;KACF;AAED,aAAS,QAAQ,OAAO;AACxB,SAAK,UAAU,KAAK,QAAQ;AAE5B,WAAO,iBAAgB;;EAGzB,aAAa;AACX,WAAO,KAAK,MAAM,WAAW,KAAK,MAAM;;EAG1C,cAAc,CAAC,IAAI,EAAE,GAAG;AACtB,UAAM,OAAO,KAAK,MAAM,iBAAiB,KAAK;AAC9C,UAAM,QAAQ,KAAK,EAAE,KAAK,KAAK,EAAE,EAAE,EAAE;AAErC,QAAI,CAAC,MAAO;AACZ,YAAO,GAAA,0CAAY,IAAI,KAAK;;EAG9B,oBAAoB;AAClB,UAAM,aAAa,KAAK,KAAK,WAAW;AACxC,QAAI,CAAC,WAAY;AAEjB,UAAM,oBAAoB,oBAAI,IAAG;AACjC,UAAM,qBAAqB,CAAC,eAAe;AACzC,UAAI,cAAc,WAAW,MAAM,WACjC,YAAW,SAAS;;OAAc;;AAItC,UAAM,kBAAkB;MACtB,MAAM,KAAK,KAAK,OAAO;MACvB,WAAW;QAAC;QAAK;;;AAGnB,UAAM,WAAW,IAAI,qBAAqB,CAAC,YAAY;AACrD,iBAAW,SAAS,SAAS;AAC3B,cAAM,KAAK,MAAM,OAAO,QAAQ;AAChC,0BAAkB,IAAI,IAAI,MAAM,iBAAiB;;AAGnD,YAAM,SAAS;WAAI;;AACnB,iBAAW,CAAC,IAAI,KAAK,KAAK,OACxB,KAAI,OAAO;AACT,2BAAmB,EAAE;AACrB;;OAGH,eAAe;AAElB,eAAW,EAAA,KAAM,KAAM,KAAK,KAAK,WAAW,OAAM,EAChD,UAAS,QAAQ,KAAK,OAAO;AAG/B,SAAK,UAAU,KAAK,QAAQ;;EAG9B,cAAc;AACZ,UAAM,cAAc;MAAE,GAAG,KAAK,MAAM;;AAEpC,UAAM,WAAW,IAAI,qBACnB,CAAC,YAAY;AACX,iBAAW,SAAS,SAAS;AAC3B,cAAM,QAAQ,SAAS,MAAM,OAAO,QAAQ,KAAK;AAEjD,YAAI,MAAM,eACR,aAAY,KAAK,IAAI;YAErB,QAAO,YAAY,KAAK;;AAI5B,WAAK,SAAS;;OAAe;OAE/B;MACE,MAAM,KAAK,KAAK,OAAO;MACvB,YAAY,GACV,KAAK,MAAM,mBAAmB,kCAAY,gBAAgB,EAAE,UACpD,KAAK,MAAM,kBAAkB,kCAAY,aAAa;KACjE;AAGH,eAAW,EAAA,KAAM,KAAM,KAAK,KAAK,WAAW,OAAM,GAAI;AACpD,iBAAW,OAAO,KAChB,KAAI,IAAI,QACN,UAAS,QAAQ,IAAI,OAAO;;AAKlC,SAAK,UAAU,KAAK,QAAQ;;EAG9B,eAAe,GAAG;AAChB,MAAE,eAAc;;EAwGlB,gBAAgB;AACd,UAAM,QAAQ,KAAK,KAAK,YAAY;AACpC,QAAI,CAAC,MAAO;AAEZ,UAAM,KAAI;;EAGZ,SAAS,EAAA,GAAG,OAAO,MAAM,OAAO,IAAI,KAAM,GAAI;AAC5C,UAAM,OAAO,KAAK,MAAM,iBAAiB,KAAK;AAC9C,QAAI,CAAC,KAAK,OAAQ;AAElB,QAAI,CAAC,IAAI,EAAE,IAAI,KAAK,MAAM;AAE1B,UAAM,OAAO,MAAM;AACjB,UAAI,MAAM,GAAG;AACX,YAAI,MAAM,KAAK,CAAC,EAAE,WAAW,QAAQ,IACnC,QAAO;;AAIX,UAAI,MAAM,IAAI;AACZ,YACE,CAAC,EAAE,WACF,SAAS,SACV,MAAM,kBAAkB,MAAM,MAAM,OAEpC,QAAO;UAAC;UAAG;;AAGb,eAAO;;AAGT,UAAI,QAAQ,OAAO;AACjB,YAAI,MAAM,KAAK,EAAE;AACjB,cAAM,YAAY,OAAO,KAAK;AAE9B,cAAM;AACN,YAAI,CAAC,IAAI,EAAE,GAAG;AACZ,gBAAM;AACN,gBAAM,KAAK,EAAE;AAEb,cAAI,CAAC,KAAK;AACR,iBAAK,OAAO,IAAI,KAAK,SAAS;AAC9B,iBAAK,OAAO,IAAI,KAAK,EAAE,EAAE,SAAS;AAElC,mBAAO;cAAC;cAAI;;;AAGd,eAAK,OAAO,IAAI,SAAS,IAAI;;AAG/B,eAAO;UAAC;UAAI;;;AAGd,UAAI,MAAM,MAAM;AACd,cAAM,KAAK,KAAK;AAChB,cAAM,MAAM,KAAK,EAAE;AAEnB,YAAI,CAAC,KAAK;AACR,eAAK,KAAK,IAAI,KAAK,SAAS;AAC5B,eAAK,KAAK,IAAI,KAAK,EAAE,EAAE,SAAS;AAEhC,iBAAO;YAAC;YAAI;;;AAGd,YAAI,CAAC,IAAI,EAAE,EACT,MAAK,IAAI,SAAS;AAGpB,eAAO;UAAC;UAAI;;;OAEf;AAED,QAAI,IACF,GAAE,eAAc;SACX;AACL,UAAI,KAAK,MAAM,IAAI,CAAC,IAAI,GACtB,MAAK,SAAS;QAAE,KAAK;UAAC;UAAI;;OAAK;AAGjC;;AAGF,SAAK,SAAS;;MAAO,UAAU;OAAQ,MAAM;AAC3C,WAAK,SAAS;QAAE,KAAK,IAAI,CAAC;OAAG;KAC9B;;EAGH,SAAS,EAAA,YAAY,IAAK,GAAI;AAC5B,UAAM,OAAO,KAAK,MAAM,iBAAiB,KAAK;AAC9C,QAAI,CAAC,KAAK,OAAQ;AAElB,UAAM,SAAS,KAAK,KAAK,OAAO;AAChC,UAAM,aAAa,OAAO,sBAAqB;AAE/C,QAAI,YAAY;AAEhB,QAAI,OAAO,EACT,cAAa,KAAK,GAAG,EAAE;AAGzB,QAAI,YAAY;AACd,YAAM,MACJ,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,WAAW,IAAI,UAAU,EAAE;AAChE,YAAM,eAAe,IAAI,QAAQ,sBAAqB;AAEtD,kBAAY,aAAa,OAAO,WAAW,MAAM,OAAO,aAAa;;AAGvE,QAAI,OAAO,GAAC;AACV,UAAI,CAAC,IACH,aAAY;WACP;AACL,cAAM,WAAW,KAAK,GAAG,EAAE;AAC3B,cAAM,SAAS,YAAY,WAAW,KAAK,MAAM;AACjD,cAAM,SACJ,SACA,KAAK,MAAM,kBACX,KAAK,MAAM,kBAAkB;AAE/B,YAAI,SAAS,OAAO,UAClB,aAAY;iBACH,SAAS,OAAO,YAAY,WAAW,OAChD,aAAY,SAAS,WAAW;YAEhC;;;AAKN,SAAK,YAAW;AAChB,WAAO,YAAY;;EAGrB,cAAc;AACZ,SAAK,iBAAiB;AACtB,iBAAa,KAAK,gBAAgB;AAClC,SAAK,mBAAmB,WAAW,MAAM;AACvC,aAAO,KAAK;OACX,GAAG;;EAOR,gBAAgB,KAAK;AACnB,QAAI,KAAK,kBAAkB,KAAK,MAAM,UAAW;AACjD,SAAK,SAAS;MAAE,KAAK,OAAO;QAAC;QAAI;;MAAK,UAAU;KAAO;;EAGzD,iBAAiB,EAAA,GAAG,OAAO,IAAK,GAAI;AAClC,QAAI,CAAC,KAAK,MAAM,cAAe;AAE/B,QAAI,CAAC,SAAS,IACZ,SAAQ,KAAK,cAAc,GAAG;AAGhC,QAAI,OAAO;AACT,YAAM,aAAY,GAAA,2CAAa,OAAO;QAAE,WAAW,KAAK,MAAM,OAAO;OAAG;AAExE,UAAI,KAAK,MAAM,gBACb,EAAA,GAAA,0CAAe,IAAI,WAAW,KAAK,KAAK;AAG1C,WAAK,MAAM,cAAc,WAAW,CAAC;;;EAuBzC,aAAa;AACX,QAAI,CAAC,KAAK,MAAM,UAAW;AAC3B,SAAK,SAAS;MAAE,WAAW;MAAM,UAAU;KAAM;AAEjD,SAAK,KAAK,oBAAoB,SAAS,KAAK,eAAe;AAC3D,SAAK,KAAK,oBAAoB,WAAW,KAAK,iBAAiB;;EAGjE,oBAAoB,UAAU;AAC5B,SAAK,SAAS;;KAAY;;EAG5B,gBAAgB,MAAM;AACpB,SAAK,YAAW;AAChB,SAAK,WAAU;AAEf,SAAK,SAAS;;MAAQ,UAAU;KAAM;AACtC,KAAA,GAAA,0CAAM,IAAI,QAAQ,IAAI;;EAGxB,YAAY;AACV,YACE,GAAA,4CAAC,GAAA,2CAAU;MAET,KAAK,KAAK,KAAK;MACf,OAAO,KAAK,MAAM;MAClB,OAAO,KAAK,MAAM;MAClB,KAAK,KAAK;MACV,WAAW,CAAC,CAAC,KAAK,MAAM;MACxB,UAAU,KAAK,MAAM;MACrB,SAAS,KAAK;OAPT,KAAK,MAAM;;EAYtB,gBAAgB;AACd,UAAM,QAAQ,KAAK,cAAc,KAAK,MAAM,GAAG;AAC/C,UAAM,kBACJ,KAAK,MAAM,iBAAiB,CAAC,KAAK,MAAM,cAAc;AAExD,YACE,GAAA,2CAAC,OAAG;MACF,IAAG;MACH,OAAM;MACN,KAAK,KAAK;MACV,iBAAe,KAAK,MAAM;;SAE1B,GAAA,2CAAC,OAAG;UAAC,OAAM;;aACT,GAAA,2CAAC,OAAG;cACF,OAAM;cACN,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,MAAM;;yBAGvB,GAAA,4CAAC,GAAA,2CAAK;gBACJ;gBACA,IACE,kBACI,KAAK,MAAM,kBAAkB,QAC7B,KAAK,MAAM,iBACV,KAAK,MAAM,mBAAmB,QAC3B,eACA;gBAEV,KAAK,KAAK,MAAM;gBAChB,MAAM,KAAK,MAAM;gBACjB,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM;gBACxC,aAAa;gBACb,mBAAmB,KAAK,MAAM;;;aAIlC,GAAA,2CAAC,OAAG;cAAC,OAAO,UAAU,KAAK,IAAI,CAAC,CAAC;wBAC9B,SAAS,mBACR,GAAA,2CAAC,OAAG;gBAAC,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC;;mBACrD,GAAA,2CAAC,OAAG;oBAAC,OAAM;8BACR,QAAQ,MAAM,QAAO,GAAA,2CAAK;;mBAE7B,GAAA,2CAAC,OAAG;oBAAC,OAAM;8BACR,QAAQ,MAAM,MAAM,CAAC,EAAE,cAAa,GAAA,2CAAK;;;oBAI9C,GAAA,2CAAC,OAAG;gBAAC,OAAM;2BAA+B,GAAA,2CAAK;;;;;QAKpD,CAAC,SACA,KAAK,MAAM,oBAAoB,aAC/B,KAAK,qBAAoB;;;;EAKjC,kBAAkB,OAAO,EAAA,KAAK,UAAU,KAAM,GAAI;AAChD,UAAM,OAAO,KAAK,MAAM;AACxB,UAAM,OAAO,KAAK,MAAM,YAAY,KAAK,MAAM;AAC/C,UAAM,YAAY,MAAM,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC;AACxD,UAAM,SAAS,UAAU;AACzB,UAAM,YAAW,GAAA,2CAAU,KAAK,MAAM,KAAK,GAAG;AAC9C,UAAM,MAAM,IAAI,OAAO,MAAM,EAAE,EAAE,KAAK,EAAE;AAExC,YACE,GAAA,4CAAC,GAAA,2CAAmB;;;;iBAClB,GAAA,2CAAC,UAAM;QACL,cAAY;QACZ,iBAAe,YAAY;QAC3B,iBAAe;QACf,gBAAc,KAAK;QACnB,iBAAe,KAAK,MAAM;QAC1B,OAAO,KAAK,MAAM,mBAAmB,SAAS,MAAM,OAAO;QAC3D,MAAK;QACL,OAAM;QACN,UAAS;QACT,SAAS,CAAC,MAAM,KAAK,iBAAiB;;;SAAY;QAClD,cAAc,MAAM,KAAK,gBAAgB,GAAG;QAC5C,cAAc,MAAM,KAAK,gBAAe;QACxC,OAAO;UACL,OAAO,KAAK,MAAM;UAClB,QAAQ,KAAK,MAAM;UACnB,UAAU,KAAK,MAAM;UACrB,YAAY;;;WAGd,GAAA,2CAAC,OAAG;YACF,eAAY;YACZ,OAAM;YACN,OAAO;cACL,cAAc,KAAK,MAAM;cACzB,iBAAiB,KAAK,MAAM,oBACxB,KAAK,MAAM,mBACR,WAAW,KAAK,KAAK,MAAM,kBAAkB,MAAM,IAEtD;;;WAGR,GAAA,4CAAC,GAAA,2CAAK;YACJ;YACA,KAAK,KAAK,MAAM;YAChB,MAAM,KAAK,MAAM;YACjB;YACA,aAAa;YACb,mBAAmB,KAAK,MAAM;;;;OAvCV,GAAG;;EA8CjC,eAAe;AACb,UAAM,iBACJ,KAAK,MAAM,mBAAmB,UAC9B,KAAK,MAAM,oBAAoB;AAEjC,YACE,GAAA,2CAAC,OAAG;;SACF,GAAA,2CAAC,OAAG;UAAC,OAAM;;SACX,GAAA,2CAAC,OAAG;UAAC,OAAM;;aACT,GAAA,2CAAC,OAAG;cAAC,OAAM;;iBACT,GAAA,2CAAC,SAAK;kBACJ,MAAK;kBACL,KAAK,KAAK,KAAK;kBACf,cAAa,GAAA,2CAAK;kBAClB,SAAS,KAAK;kBACd,SAAS,KAAK;kBACd,WAAW,KAAK;kBAChB,cAAa;;iBAEf,GAAA,2CAAC,QAAI;kBAAC,OAAM;6BAAmB,GAAA,0CAAM,OAAO;;gBAC3C,KAAK,MAAM,kBACV,GAAA,2CAAC,UAAM;kBACL,OAAM;kBACN,cAAW;kBACX,MAAK;kBACL,OAAM;kBACN,SAAS,KAAK;kBACd,aAAa,KAAK;6BAEjB,GAAA,0CAAM,OAAO;;;;YAKnB,kBAAkB,KAAK,qBAAoB;;;;;;EAMpD,sBAAsB;AACpB,UAAM,EAAA,cAAe,IAAK,KAAK;AAC/B,QAAI,CAAC,cAAe,QAAO;AAE3B,YACE,GAAA,2CAAC,OAAG;MAAC,OAAM;MAAW,KAAK,KAAK,KAAK;;SACnC,GAAA,2CAAC,OAAG;UAAC,OAAO,8BAA8B,KAAK,IAAI,CAAC,CAAC;qBAClD,GAAA,2CAAK,WAAW;;SAEnB,GAAA,2CAAC,OAAG;oBACD,CAAC,cAAc,UACd,GAAA,2CAAC,OAAG;YAAC,OAAO,uBAAuB,KAAK,IAAI,CAAC,CAAC;sBAC3C,KAAK,MAAM,qBACV,GAAA,2CAAC,KAAC;cAAC,SAAS,KAAK,MAAM;yBAAmB,GAAA,2CAAK;;eAInD,cAAc,IAAI,CAAC,KAAK,MAAM;AAC5B,oBACE,GAAA,2CAAC,OAAG;cAAC,OAAM;wBACR,IAAI,IAAI,CAAC,OAAO,OAAO;AACtB,uBAAO,KAAK,kBAAkB,OAAO;kBACnC,KAAK;oBAAC;oBAAG;;kBACT,UAAU,IAAI,KAAK,MAAM,UAAU,KAAK;kBACxC,MAAM;iBACP;eACF;;WAGN;;;;;EAOX,mBAAmB;AACjB,UAAM,EAAA,WAAY,KAAK,GAAA;AACvB,UAAM,SAAS,CAAC,CAAC,KAAK,MAAM;AAC5B,UAAM,UAAU,KAAK,WAAU;AAE/B,YACE,GAAA,2CAAC,OAAG;MACF,OAAO;QACL,YAAY,SAAS,WAAW;QAChC,SAAS,SAAS,SAAS;QAC3B,QAAQ;;gBAGT,WAAW,IAAI,CAAC,aAAa;AAC5B,cAAM,EAAA,MAAM,KAAM,IAAK,KAAK,KAAK,WAAW,IAAI,SAAS,EAAE;AAE3D,gBACE,GAAA,2CAAC,OAAG;UACF,WAAS,SAAS,SAAS,SAAS,OAAO,KAAK,SAAS;UACzD,OAAM;UACN,KAAK;;aAEL,GAAA,2CAAC,OAAG;cAAC,OAAO,8BAA8B,KAAK,IAAI,CAAC,CAAC;wBAClD,SAAS,SAAQ,GAAA,2CAAK,WAAW,SAAS,EAAE;;aAE/C,GAAA,2CAAC,OAAG;cACF,OAAM;cACN,OAAO;gBACL,QAAQ,KAAK,SAAS,KAAK,MAAM;;wBAGlC,KAAK,IAAI,CAAC,KAAK,MAAM;AACpB,sBAAM,YACJ,IAAI,QAAS,IAAI,QAAQ,kCAAY;AACvC,sBAAM,UAAU,KAAK,MAAM,YAAY,SAAS;AAChD,sBAAM,MAAM,aAAa,MAAM,MAAM;AAErC,oBAAI,CAAC,WAAW,CAAC,IACf,QAAO;AAGT,sBAAM,QAAQ,IAAI;AAClB,sBAAM,MAAM,QAAQ;AACpB,sBAAM,WAAW,SAAS,OAAO,MAAM,OAAO,GAAG;AAEjD,oBAAI,SAAS,SAAS,QACpB,UAAS,KAAI,GAAI,IAAI,MAAM,UAAU,SAAS,MAAM,CAAC;AAGvD,wBACE,GAAA,2CAAC,OAAG;kBAEF,cAAY,IAAI;kBAChB;kBACA,OAAM;kBACN,OAAO;oBAAE,KAAK,IAAI,KAAK,MAAM;;4BAE5B,WACC,SAAS,IAAI,CAAC,SAAS,OAAO;AAC5B,wBAAI,CAAC,QACH,SACE,GAAA,2CAAC,OAAG;sBACF,OAAO;wBACL,OAAO,KAAK,MAAM;wBAClB,QAAQ,KAAK,MAAM;;;AAM3B,0BAAM,SAAQ,GAAA,0CAAY,IAAI,OAAO;AAErC,2BAAO,KAAK,kBAAkB,OAAO;sBACnC,KAAK;wBAAC,IAAI;wBAAO;;sBACjB,UAAU,IAAI,WAAW;sBACzB,MAAM,KAAK;qBACZ;mBACF;mBA1BE,IAAI,KAAK;eA6BnB;;;;OAIR;;;EAKP,uBAAuB;AACrB,QAAI,KAAK,MAAM,oBAAoB,OACjC,QAAO;AAGT,YACE,GAAA,2CAAC,OAAG;MACF,OAAM;MACN,OAAO;QACL,UAAU;QACV,OAAO,KAAK,MAAM;QAClB,QAAQ,KAAK,MAAM;;iBAGrB,GAAA,2CAAC,UAAM;QACL,MAAK;QACL,KAAK,KAAK,KAAK;QACf,OAAM;QACN,iBAAe,KAAK,MAAM,YAAY,KAAK;QAC3C,eAAY,GAAA,2CAAK,MAAM;QACvB,QAAO,GAAA,2CAAK,MAAM;QAClB,SAAS,KAAK;QACd,OAAO;UACL,OAAO,KAAK,MAAM;UAClB,QAAQ,KAAK,MAAM;;mBAGrB,GAAA,2CAAC,QAAI;UAAC,OAAO,uBAAuB,KAAK,MAAM,IAAI;;;;;EAM3D,mBAAmB;AACjB,UAAM,QAAQ,KAAK,cAAc,KAAK,MAAM,GAAG;AAC/C,UAAM,WAAW,QAAQ,MAAM,OAAO;AAEtC,YACE,GAAA,2CAAC,OAAG;MAAC,aAAU;MAAS,OAAM;gBAC3B;;;EAKP,cAAc;AACZ,UAAM,iBAAiB,KAAK,KAAK,eAAe;AAChD,UAAM,qBAAqB,eAAe,sBAAqB;AAC/D,UAAM,WAAW,KAAK,KAAK,sBAAqB;AAEhD,UAAM,WAAW,CAAA;AAEjB,QAAI,KAAK,OAAO,MACd,UAAS,QAAQ,SAAS,QAAQ,mBAAmB,QAAQ;QAE7D,UAAS,OAAO,mBAAmB,OAAO,SAAS,OAAO;AAG5D,QACE,KAAK,MAAM,mBAAmB,YAC9B,KAAK,MAAM,oBAAoB,UAE/B,UAAS,SAAS,SAAS,SAAS,mBAAmB,MAAM;SACxD;AACL,eAAS,MAAM,mBAAmB,SAAS,SAAS,MAAM;AAC1D,eAAS,SAAS;;AAGpB,YACE,GAAA,2CAAC,OAAG;MACF,KAAK,KAAK,KAAK;MACf,MAAK;MACL,KAAK,KAAK;MACV,eAAY,GAAA,2CAAK,MAAM;MACvB,OAAM;MACN,iBAAe,SAAS,MAAM,QAAQ;MACtC,OAAO;gBAEN;WAAI,MAAM,CAAC,EAAE,KAAI;QAAI,IAAI,CAAC,MAAM;AAC/B,cAAM,OAAO,IAAI;AACjB,cAAM,UAAU,KAAK,MAAM,QAAQ;AAEnC,gBACE,GAAA,2CAAC,OAAG;;aACF,GAAA,2CAAC,SAAK;cACJ,MAAK;cACL,MAAK;cACL,OAAO;cACP,eAAY,GAAA,2CAAK,MAAM,IAAI;cAC3B,KAAK,UAAU,KAAK,KAAK,gBAAgB;cACzC,gBAAgB;cAChB,UAAU,MAAM,KAAK,oBAAoB,IAAI;cAC7C,WAAW,CAAC,MAAM;AAChB,oBACE,EAAE,QAAQ,WACV,EAAE,QAAQ,WACV,EAAE,QAAQ,OACV;AACA,oBAAE,eAAc;AAChB,uBAAK,gBAAgB,IAAI;;;;aAK/B,GAAA,2CAAC,UAAM;cACL,eAAY;cACZ,UAAS;cACT,SAAS,MAAM,KAAK,gBAAgB,IAAI;cACxC,cAAc,MAAM,KAAK,oBAAoB,IAAI;cACjD,cAAc,MAAM,KAAK,oBAAmB;cAC5C,OAAM;;iBAEN,GAAA,2CAAC,QAAI;kBAAC,OAAO,uBAAuB,IAAI;;iBACxC,GAAA,2CAAC,QAAI;kBAAC,OAAM;6BAAmB,GAAA,2CAAK,MAAM,IAAI;;;;;;OAIrD;;;EAKP,SAAS;AACP,UAAM,YAAY,KAAK,MAAM,UAAU,KAAK,MAAM;AAElD,YACE,GAAA,2CAAC,WAAO;MACN,IAAG;MACH,OAAM;MACN,KAAK,KAAK;MACV,OAAO;QACL,OAAO,KAAK,MAAM,eACd,SACA,QAAQ,SAAS;;MAEvB,kBAAgB,KAAK,MAAM;MAC3B,cAAY,KAAK,MAAM;MACvB,aAAW,KAAK,MAAM,YAAY,KAAK;;QAEtC,KAAK,MAAM,mBAAmB,SAAS,KAAK,cAAa;QACzD,KAAK,MAAM,eAAe,SAAS,KAAK,UAAS;QACjD,KAAK,MAAM,kBAAkB,aAC5B,GAAA,2CAAC,OAAG;UAAC,OAAM;oBAAc,KAAK,aAAY;;SAG5C,GAAA,2CAAC,OAAG;UAAC,KAAK,KAAK,KAAK;UAAQ,OAAM;qBAChC,GAAA,2CAAC,OAAG;YACF,OAAO;cACL,OAAO,KAAK,MAAM,eAAe,SAAS;cAC1C,QAAQ;;;cAGT,KAAK,MAAM,kBAAkB,YAAY,KAAK,aAAY;cAC1D,KAAK,oBAAmB;cACxB,KAAK,iBAAgB;;;;QAIzB,KAAK,MAAM,eAAe,YAAY,KAAK,UAAS;QACpD,KAAK,MAAM,mBAAmB,YAAY,KAAK,cAAa;QAC5D,KAAK,MAAM,aAAa,KAAK,YAAW;QACxC,KAAK,iBAAgB;;;;EAzlC5B,YAAY,OAAO;AACjB,UAAK;AAmLP,KAAA,GAAA,0CAAA,MAAA,qBAAoB,MAAM;AACxB,UAAI,KAAK,MAAM,SAAS,OAAQ;AAChC,WAAK,SAAS;QAAE,OAAO,KAAK,UAAU,UAAU,SAAS;OAAS;KACnE;AAeD,KAAA,GAAA,0CAAA,MAAA,sBAAqB,CAAC,MAAM;AAC1B,YAAM,EAAA,QAAS,IAAK,KAAK;AAEzB,UAAI,EAAE,UAAU,SAAS;AACvB,YAAI,KAAK,MAAM,UACb,MAAK,WAAU;AAGjB,YAAI,KAAK,MAAM,eACb,MAAK,MAAM,eAAe,CAAC;;KAGhC;AAED,KAAA,GAAA,0CAAA,MAAA,mBAAkB,CAAC,MAAM;AACvB,UAAI,CAAC,KAAK,MAAM,UAAW;AAC3B,UAAI,CAAC,EAAE,OAAO,QAAQ,OAAO,GAAG;AAC9B,UAAE,eAAc;AAChB,UAAE,yBAAwB;AAE1B,aAAK,WAAU;;KAElB;AAED,KAAA,GAAA,0CAAA,MAAA,qBAAoB,CAAC,MAAM;AACzB,UAAI,CAAC,KAAK,MAAM,UAAW;AAC3B,UAAI,EAAE,OAAO,UAAU;AACrB,UAAE,eAAc;AAChB,UAAE,yBAAwB;AAE1B,aAAK,WAAU;;KAElB;AAsHD,KAAA,GAAA,0CAAA,MAAA,qBAAoB,MAAM;AACxB,YAAM,QAAQ,KAAK,cAAc,KAAK,MAAM,GAAG;AAC/C,UAAI,CAAC,MAAO;AAEZ,WAAK,SAAS;QAAE,KAAK;UAAC;UAAI;;OAAK;KAChC;AAED,KAAA,GAAA,0CAAA,MAAA,qBAAoB,YAAY;AAC9B,YAAM,QAAQ,KAAK,KAAK,YAAY;AACpC,UAAI,CAAC,MAAO;AAEZ,YAAM,EAAA,MAAO,IAAK;AAClB,YAAM,gBAAgB,OAAM,GAAA,0CAAY,OAAO,KAAK;AACpD,YAAM,cAAc,MAAM;AACxB,YAAI,CAAC,KAAK,KAAK,OAAO,QAAS;AAC/B,aAAK,KAAK,OAAO,QAAQ,YAAY;;AAGvC,UAAI,CAAC,cACH,QAAO,KAAK,SAAS;;QAAiB,KAAK;UAAC;UAAI;;SAAO,WAAW;AAGpE,YAAM,MAAM,MAAM,kBAAkB,MAAM,MAAM,SAAS;QAAC;QAAG;UAAK;QAAC;QAAI;;AACvE,YAAM,OAAO,CAAA;AACb,WAAK,UAAU,cAAc;AAC7B,UAAI,MAAM;AAEV,eAAS,SAAS,eAAe;AAC/B,YAAI,CAAC,KAAK,UAAU,IAAI,UAAU,KAAK,WAAU,GAAI;AACnD,gBAAM,CAAA;AACN,cAAI,eAAe;AACnB,cAAI,UAAU,KAAK;AACnB,eAAK,KAAK,GAAG;;AAGf,YAAI,KAAK,KAAK;;AAGhB,WAAK,YAAW;AAChB,WAAK,SAAS;QAAE,eAAe;;SAAa,WAAW;KACxD;AAED,KAAA,GAAA,0CAAA,MAAA,uBAAsB,CAAC,MAAM;AAE3B,YAAM,QAAQ,EAAE;AAChB,QAAE,yBAAwB;AAE1B,cAAQ,EAAE,KAAG;QACX,KAAK;AAGH,eAAK,SAAS;;;YAAY,MAAM;WAAM;AACtC;QAEF,KAAK;AAGH,eAAK,SAAS;;;YAAY,OAAO;WAAM;AACvC;QAEF,KAAK;AAGH,eAAK,SAAS;;;YAAY,IAAI;WAAM;AACpC;QAEF,KAAK;AAGH,eAAK,SAAS;;;YAAY,MAAM;WAAM;AACtC;QAEF,KAAK;AACH,YAAE,eAAc;AAChB,eAAK,iBAAiB;;YAAK,KAAK,KAAK,MAAM;WAAK;AAChD;QAEF,KAAK;AACH,YAAE,eAAc;AAChB,cAAI,KAAK,MAAM,cACb,MAAK,YAAW;cAEhB,MAAK,cAAa;AAEpB;QAEF;AACE;;KAEL;AAED,KAAA,GAAA,0CAAA,MAAA,eAAc,MAAM;AAClB,YAAM,QAAQ,KAAK,KAAK,YAAY;AACpC,UAAI,CAAC,MAAO;AAEZ,YAAM,QAAQ;AACd,YAAM,MAAK;AAEX,WAAK,kBAAiB;KACvB;AAgJD,KAAA,GAAA,0CAAA,MAAA,uBAAsB,CAAC,EAAA,UAAU,EAAG,MAAO;AACzC,WAAK,SAAS,KAAK,IAAI;QAAE,KAAK;UAAO;QAAE,YAAY,SAAS;OAAI;KACjE;AAyBD,KAAA,GAAA,0CAAA,MAAA,aAAY,CAAC,MAAM;AACjB,YAAM,EAAA,cAAe,IAAK;AAC1B,YAAM,OAAO,cAAc,sBAAqB;AAEhD,WAAK,SAAS;QAAE,WAAW;SAAQ,YAAY;AAE7C,eAAM,GAAA,2CAAM,CAAC;AAEb,cAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,YAAI,CAAC,KAAM;AAEX,aAAK,UAAU,OAAO,QAAQ;AAC9B,aAAK,KAAK,cAAc,QAAQ,MAAK;AAErC,aAAK,KAAK,iBAAiB,SAAS,KAAK,iBAAiB,IAAI;AAC9D,aAAK,KAAK,iBAAiB,WAAW,KAAK,mBAAmB,IAAI;OACnE;KACF;AAxnBC,SAAK,YAAY,CAAA;AAEjB,SAAK,QAAQ;MACX,KAAK;QAAC;QAAI;;MACV,SAAS,KAAK,mBAAmB,KAAK;MACtC,aAAa;QAAE,GAAG;;MAClB,GAAG,KAAK,gBAAgB,KAAK;;;;AmDlBpB,IAAM,2CAAN,eAA4B,GAAA,0CAAa;EAOtD,MAAM,oBAAoB;AACxB,UAAM,SAAQ,GAAA,2CAAS,KAAK,QAAO,GAAA,2CAAa,IAAI;AACpD,UAAM,UAAU;AAChB,UAAM,MAAM,CAAC,cAAc;AACzB,WAAK,YAAY;;AAGnB,WAAM,GAAA,2CAAK,KAAK;AAChB,QAAI,KAAK,aAAc;AAEvB,KAAA,GAAA,4CAAO,GAAA,4CAAC,GAAA,2CAAM;MAAE,GAAG;QAAW,KAAK,UAAU;;EAd/C,YAAY,OAAO;AACjB,UAAM,OAAO;MAAE,SAAQ,GAAA,uBAAA,yBAAA;KAAc;;;CAHvC,GAAA,0CADmB,0CACZ,UAAQ,GAAA,yCAAW;AAoB5B,IACE,OAAO,mBAAmB,eAC1B,CAAC,eAAe,IAAI,iBAAiB,EAErC,gBAAe,OAAO,mBAAmB,wCAAa;;ACjCxD,4BAAiB;;;ACIF,SAAA,yCAAqB,OAAO;AACzC,QAAM,OAAM,GAAA,aAAA0B,QAAO,IAAI;AACvB,QAAM,YAAW,GAAA,aAAAA,QAAO,IAAI;AAE5B,MAAI,SAAS,QACX,UAAS,QAAQ,OAAO,KAAK;AAG/B,GAAA,GAAA,aAAAC,WAAU,MAAM;AACd,aAAS,UAAU,KAAI,GAAA,0CAAO;MAAE,GAAG;;KAAY;AAE/C,WAAO,MAAM;AACX,eAAS,UAAU;;KAEpB,CAAA,CAAE;AAEL,UAAO,GAAA,aAAAC,SAAM,cAAc,OAAO;;GAAO;;", "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "defer", "prevDebounce", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "createVNode", "original", "vnode", "undefined", "constructor", "createRef", "current", "Fragment", "Component", "context", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "debounceRendering", "queue", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "oldDom", "parentDom", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "newDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "reorderC<PERSON>dren", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "applyRef", "tmp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "nextDom", "sibDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "diffProps", "dom", "newProps", "oldProps", "hydrate", "setProperty", "setStyle", "style", "value", "test", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "_listeners", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "setAttribute", "removeAttribute", "event", "newVNode", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "newType", "contextType", "prototype", "render", "doR<PERSON>", "sub", "state", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "for<PERSON>ach", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "diffElementNodes", "diffed", "root", "cb", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "innerHTML", "checked", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "this", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "slice", "EMPTY_ARR", "options", "error", "vnode", "component", "ctor", "handled", "constructor", "getDerivedStateFromError", "setState", "componentDidCatch", "e", "vnodeId", "isValidElement", "Component", "prototype", "update", "callback", "s", "this", "state", "assign", "props", "push", "enqueueRender", "forceUpdate", "render", "Fragment", "rerenderQueue", "defer", "Promise", "then", "bind", "resolve", "setTimeout", "process", "i", "createVNode", "type", "key", "__source", "__self", "ref", "normalizedProps", "undefined", "defaultProps", "currentIndex", "currentComponent", "prevRaf", "afterPaintEffects", "oldBeforeDiff", "options", "oldBeforeRender", "oldAfterDiff", "diffed", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "unmount", "flushAfterPaintEffects", "component", "afterPaintEffects", "sort", "a", "b", "pop", "for<PERSON>ach", "invokeCleanup", "invokeEffect", "e", "options", "vnode", "currentComponent", "oldBeforeDiff", "oldBeforeRender", "currentIndex", "hooks", "diffed", "oldAfterDiff", "c", "length", "push", "prevRaf", "requestAnimationFrame", "callback", "raf", "done", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "setTimeout", "commitQueue", "some", "filter", "cb", "old<PERSON><PERSON><PERSON>", "unmount", "oldBeforeUnmount", "hasErrored", "s", "hook", "comp", "cleanup", "assign", "obj", "props", "i", "shallow<PERSON>iffers", "a", "b", "PureComponent", "p", "PureComponent", "prototype", "Component", "isPureReactComponent", "shouldComponentUpdate", "props", "state", "shallow<PERSON>iffers", "this", "oldDiffHook", "options", "vnode", "type", "ref", "REACT_FORWARD_SYMBOL", "Symbol", "for", "Forwarded", "oldCatchError", "options", "error", "newVNode", "oldVNode", "then", "component", "vnode", "oldUnmount", "unmount", "Suspense", "_suspenders", "suspended", "SuspenseList", "_next", "_map", "options", "unmount", "vnode", "component", "type", "oldUnmount", "Suspense", "prototype", "Component", "promise", "suspendingVNode", "suspendingComponent", "c", "this", "_suspenders", "push", "resolve", "suspended", "resolved", "onResolved", "onSuspensionComplete", "state", "suspendedVNode", "removeOriginal", "detachedParent", "originalParent", "map", "child", "insertBefore", "setState", "pop", "forceUpdate", "wasHydrating", "then", "componentWillUnmount", "render", "props", "document", "createElement", "detachedComponent", "detachedClone", "parentDom", "for<PERSON>ach", "effect", "assign", "fallback", "Fragment", "children", "list", "node", "delete", "revealOrder", "size", "length", "SuspenseList", "prototype", "Component", "child", "list", "this", "delegated", "suspended", "node", "_map", "get", "unsuspend", "wrappedUnsuspend", "props", "revealOrder", "push", "resolve", "render", "_next", "Map", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "i", "length", "set", "componentDidUpdate", "componentDidMount", "for<PERSON>ach", "_this", "REACT_ELEMENT_TYPE", "Symbol", "for", "CAMEL_PROPS", "IS_DOM", "document", "onChangeInputType", "type", "test", "Component", "prototype", "isReactComponent", "for<PERSON>ach", "key", "Object", "defineProperty", "configurable", "get", "this", "set", "v", "writable", "value", "oldEventHook", "options", "event", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "e", "persist", "nativeEvent", "currentComponent", "classNameDescriptor", "class", "oldVNodeHook", "vnode", "type", "props", "normalizedProps", "nonCustomElement", "indexOf", "i", "IS_DOM", "test", "onChangeInputType", "toLowerCase", "CAMEL_PROPS", "replace", "multiple", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "child", "selected", "defaultValue", "className", "enumerable", "$$typeof", "REACT_ELEMENT_TYPE", "oldBeforeRender", "$dvPge$useRef", "$dvPge$useEffect", "$dvPge$react"]}