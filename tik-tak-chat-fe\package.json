{"name": "frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "^2.0.9", "@chakra-ui/react": "^2.2.8", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/icons-material": "^5.11.11", "@mui/material": "^7.0.2", "axios": "^1.8.4", "framer-motion": "^7.2.0", "lottie-react": "^2.4.1", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.0", "react-scrollable-feed": "^1.3.1", "socket.io-client": "^4.5.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.2"}}