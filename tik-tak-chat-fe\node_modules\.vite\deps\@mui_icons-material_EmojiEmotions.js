"use client";
import {
  require_createSvgIcon,
  require_interopRequireDefault
} from "./chunk-HC5WEUO7.js";
import "./chunk-Y63K6H4B.js";
import {
  require_jsx_runtime
} from "./chunk-DZ4FQTDE.js";
import "./chunk-UAZK5JW2.js";
import "./chunk-6JY7F3B5.js";
import "./chunk-QP4RLAFO.js";
import {
  __commonJS
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/icons-material/EmojiEmotions.js
var require_EmojiEmotions = __commonJS({
  "node_modules/@mui/icons-material/EmojiEmotions.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M8.5 8c.83 0 1.5.67 1.5 1.5S9.33 11 8.5 11 7 10.33 7 9.5 7.67 8 8.5 8M12 18c-2.28 0-4.22-1.66-5-4h10c-.78 2.34-2.72 4-5 4m3.5-7c-.83 0-1.5-.67-1.5-1.5S14.67 8 15.5 8s1.5.67 1.5 1.5-.67 1.5-1.5 1.5"
    }), "EmojiEmotions");
  }
});
export default require_EmojiEmotions();
//# sourceMappingURL=@mui_icons-material_EmojiEmotions.js.map
