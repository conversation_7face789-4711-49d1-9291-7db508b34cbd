{"name": "@chakra-ui/icon", "version": "3.0.9", "description": "A base React component for icons", "keywords": ["react", "icon", "svg", "ui", "chakra ui"], "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/icon"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "dependencies": {"@chakra-ui/shared-utils": "2.0.1"}, "devDependencies": {"@chakra-ui/system": "2.2.7", "react-icons": "^4.2.0", "react": "^18.0.0"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}, "scripts": {"build": "JSX=1 tsup src/index.ts --dts", "dev": "pnpm build -- --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "JSX=1 tsup src/index.ts"}}