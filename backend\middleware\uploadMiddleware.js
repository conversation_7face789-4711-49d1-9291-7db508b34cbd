const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');

// <PERSON><PERSON><PERSON> thư mục uploads nếu chưa tồn tại
const uploadsDir = path.join(__dirname, '../uploads');
const imagesDir = path.join(uploadsDir, 'images');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Cấu hình multer storage
const storage = multer.memoryStorage();

// File filter để chỉ cho phép ảnh
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.'), false);
  }
};

// Cấu hình multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Middleware để xử lý và compress ảnh
const processImage = async (req, res, next) => {
  if (!req.file) {
    return next();
  }

  try {
    const filename = `image_${Date.now()}_${Math.round(Math.random() * 1E9)}.webp`;
    const filepath = path.join(imagesDir, filename);

    // Compress và convert ảnh sang WebP
    await sharp(req.file.buffer)
      .resize(1200, 1200, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .webp({ quality: 80 })
      .toFile(filepath);

    // Thêm thông tin file vào request
    req.processedFile = {
      filename: filename,
      path: filepath,
      url: `/uploads/images/${filename}`,
      originalname: req.file.originalname,
      mimetype: 'image/webp',
      size: fs.statSync(filepath).size
    };

    next();
  } catch (error) {
    console.error('Error processing image:', error);
    res.status(500).json({ message: 'Error processing image' });
  }
};

// Middleware để xóa file tạm nếu có lỗi
const cleanupOnError = (error, req, res, next) => {
  if (req.processedFile && fs.existsSync(req.processedFile.path)) {
    fs.unlinkSync(req.processedFile.path);
  }
  next(error);
};

module.exports = {
  upload: upload.single('image'),
  processImage,
  cleanupOnError
};
