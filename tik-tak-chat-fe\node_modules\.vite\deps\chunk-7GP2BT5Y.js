import {
  chakra,
  createContext,
  forwardRef,
  omitThemingProps,
  useStyleConfig
} from "./chunk-A7FHTI4F.js";
import {
  __DEV__,
  cx,
  dataAttr,
  import_lodash,
  useMergeRefs
} from "./chunk-PNVK53R2.js";
import {
  keyframes
} from "./chunk-6JY7F3B5.js";
import {
  require_react
} from "./chunk-QP4RLAFO.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@chakra-ui/button/dist/index.esm.js
var import_react3 = __toESM(require_react());
var import_react4 = __toESM(require_react());
var import_react5 = __toESM(require_react());

// node_modules/@chakra-ui/spinner/dist/index.esm.js
var import_react2 = __toESM(require_react());

// node_modules/@chakra-ui/visually-hidden/dist/index.esm.js
var import_react = __toESM(require_react());
var visuallyHiddenStyle = {
  border: "0px",
  clip: "rect(0px, 0px, 0px, 0px)",
  height: "1px",
  width: "1px",
  margin: "-1px",
  padding: "0px",
  overflow: "hidden",
  whiteSpace: "nowrap",
  position: "absolute"
};
var VisuallyHidden = chakra("span", {
  baseStyle: visuallyHiddenStyle
});
if (__DEV__) {
  VisuallyHidden.displayName = "VisuallyHidden";
}
var VisuallyHiddenInput = chakra("input", {
  baseStyle: visuallyHiddenStyle
});
if (__DEV__) {
  VisuallyHiddenInput.displayName = "VisuallyHiddenInput";
}

// node_modules/@chakra-ui/spinner/dist/index.esm.js
var spin = keyframes({
  "0%": {
    transform: "rotate(0deg)"
  },
  "100%": {
    transform: "rotate(360deg)"
  }
});
var Spinner = forwardRef((props, ref) => {
  const styles = useStyleConfig("Spinner", props);
  const {
    label = "Loading...",
    thickness = "2px",
    speed = "0.45s",
    emptyColor = "transparent",
    className,
    ...rest
  } = omitThemingProps(props);
  const _className = cx("chakra-spinner", className);
  const spinnerStyles = {
    display: "inline-block",
    borderColor: "currentColor",
    borderStyle: "solid",
    borderRadius: "99999px",
    borderWidth: thickness,
    borderBottomColor: emptyColor,
    borderLeftColor: emptyColor,
    animation: `${spin} ${speed} linear infinite`,
    ...styles
  };
  return import_react2.default.createElement(chakra.div, {
    ref,
    __css: spinnerStyles,
    className: _className,
    ...rest
  }, label && import_react2.default.createElement(VisuallyHidden, null, label));
});
if (__DEV__) {
  Spinner.displayName = "Spinner";
}

// node_modules/@chakra-ui/button/dist/index.esm.js
var import_react6 = __toESM(require_react());
var import_react7 = __toESM(require_react());
var import_react8 = __toESM(require_react());
var import_react9 = __toESM(require_react());
var [ButtonGroupProvider, useButtonGroup] = createContext({
  strict: false,
  name: "ButtonGroupContext"
});
function ButtonIcon(props) {
  const { children, className, ...rest } = props;
  const _children = (0, import_react5.isValidElement)(children) ? (0, import_react5.cloneElement)(children, {
    "aria-hidden": true,
    focusable: false
  }) : children;
  const _className = cx("chakra-button__icon", className);
  return import_react3.default.createElement(chakra.span, {
    display: "inline-flex",
    alignSelf: "center",
    flexShrink: 0,
    ...rest,
    className: _className
  }, _children);
}
if (__DEV__) {
  ButtonIcon.displayName = "ButtonIcon";
}
function ButtonSpinner(props) {
  const {
    label,
    placement,
    spacing = "0.5rem",
    children = import_react3.default.createElement(Spinner, {
      color: "currentColor",
      width: "1em",
      height: "1em"
    }),
    className,
    __css,
    ...rest
  } = props;
  const _className = cx("chakra-button__spinner", className);
  const marginProp = placement === "start" ? "marginEnd" : "marginStart";
  const spinnerStyles = (0, import_react6.useMemo)(() => ({
    display: "flex",
    alignItems: "center",
    position: label ? "relative" : "absolute",
    [marginProp]: label ? spacing : 0,
    fontSize: "1em",
    lineHeight: "normal",
    ...__css
  }), [__css, label, marginProp, spacing]);
  return import_react3.default.createElement(chakra.div, {
    className: _className,
    ...rest,
    __css: spinnerStyles
  }, children);
}
if (__DEV__) {
  ButtonSpinner.displayName = "ButtonSpinner";
}
function useButtonType(value) {
  const [isButton, setIsButton] = (0, import_react7.useState)(!value);
  const refCallback = (0, import_react7.useCallback)((node) => {
    if (!node)
      return;
    setIsButton(node.tagName === "BUTTON");
  }, []);
  const type = isButton ? "button" : void 0;
  return { ref: refCallback, type };
}
var Button = forwardRef((props, ref) => {
  const group = useButtonGroup();
  const styles = useStyleConfig("Button", { ...group, ...props });
  const {
    isDisabled = group == null ? void 0 : group.isDisabled,
    isLoading,
    isActive,
    children,
    leftIcon,
    rightIcon,
    loadingText,
    iconSpacing = "0.5rem",
    type,
    spinner,
    spinnerPlacement = "start",
    className,
    as,
    ...rest
  } = omitThemingProps(props);
  const buttonStyles = (0, import_react4.useMemo)(() => {
    const _focus = (0, import_lodash.default)({}, (styles == null ? void 0 : styles["_focus"]) ?? {}, { zIndex: 1 });
    return {
      display: "inline-flex",
      appearance: "none",
      alignItems: "center",
      justifyContent: "center",
      userSelect: "none",
      position: "relative",
      whiteSpace: "nowrap",
      verticalAlign: "middle",
      outline: "none",
      ...styles,
      ...!!group && { _focus }
    };
  }, [styles, group]);
  const { ref: _ref, type: defaultType } = useButtonType(as);
  const contentProps = { rightIcon, leftIcon, iconSpacing, children };
  return import_react3.default.createElement(chakra.button, {
    disabled: isDisabled || isLoading,
    ref: useMergeRefs(ref, _ref),
    as,
    type: type ?? defaultType,
    "data-active": dataAttr(isActive),
    "data-loading": dataAttr(isLoading),
    __css: buttonStyles,
    className: cx("chakra-button", className),
    ...rest
  }, isLoading && spinnerPlacement === "start" && import_react3.default.createElement(ButtonSpinner, {
    className: "chakra-button__spinner--start",
    label: loadingText,
    placement: "start",
    spacing: iconSpacing
  }, spinner), isLoading ? loadingText || import_react3.default.createElement(chakra.span, {
    opacity: 0
  }, import_react3.default.createElement(ButtonContent, {
    ...contentProps
  })) : import_react3.default.createElement(ButtonContent, {
    ...contentProps
  }), isLoading && spinnerPlacement === "end" && import_react3.default.createElement(ButtonSpinner, {
    className: "chakra-button__spinner--end",
    label: loadingText,
    placement: "end",
    spacing: iconSpacing
  }, spinner));
});
if (__DEV__) {
  Button.displayName = "Button";
}
function ButtonContent(props) {
  const { leftIcon, rightIcon, children, iconSpacing } = props;
  return import_react3.default.createElement(import_react3.default.Fragment, null, leftIcon && import_react3.default.createElement(ButtonIcon, {
    marginEnd: iconSpacing
  }, leftIcon), children, rightIcon && import_react3.default.createElement(ButtonIcon, {
    marginStart: iconSpacing
  }, rightIcon));
}
var ButtonGroup = forwardRef(function ButtonGroup2(props, ref) {
  const {
    size,
    colorScheme,
    variant,
    className,
    spacing = "0.5rem",
    isAttached,
    isDisabled,
    ...rest
  } = props;
  const _className = cx("chakra-button__group", className);
  const context = (0, import_react8.useMemo)(() => ({ size, colorScheme, variant, isDisabled }), [size, colorScheme, variant, isDisabled]);
  let groupStyles = {
    display: "inline-flex"
  };
  if (isAttached) {
    groupStyles = {
      ...groupStyles,
      "> *:first-of-type:not(:last-of-type)": { borderEndRadius: 0 },
      "> *:not(:first-of-type):not(:last-of-type)": { borderRadius: 0 },
      "> *:not(:first-of-type):last-of-type": { borderStartRadius: 0 }
    };
  } else {
    groupStyles = {
      ...groupStyles,
      "& > *:not(style) ~ *:not(style)": { marginStart: spacing }
    };
  }
  return import_react3.default.createElement(ButtonGroupProvider, {
    value: context
  }, import_react3.default.createElement(chakra.div, {
    ref,
    role: "group",
    __css: groupStyles,
    className: _className,
    "data-attached": isAttached ? "" : void 0,
    ...rest
  }));
});
if (__DEV__) {
  ButtonGroup.displayName = "ButtonGroup";
}
var IconButton = forwardRef((props, ref) => {
  const { icon, children, isRound, "aria-label": ariaLabel, ...rest } = props;
  const element = icon || children;
  const _children = (0, import_react9.isValidElement)(element) ? (0, import_react9.cloneElement)(element, {
    "aria-hidden": true,
    focusable: false
  }) : null;
  return import_react3.default.createElement(Button, {
    padding: "0",
    borderRadius: isRound ? "full" : void 0,
    ref,
    "aria-label": ariaLabel,
    ...rest
  }, _children);
});
if (__DEV__) {
  IconButton.displayName = "IconButton";
}

export {
  visuallyHiddenStyle,
  VisuallyHidden,
  VisuallyHiddenInput,
  Spinner,
  useButtonGroup,
  ButtonSpinner,
  Button,
  ButtonGroup,
  IconButton
};
//# sourceMappingURL=chunk-7GP2BT5Y.js.map
